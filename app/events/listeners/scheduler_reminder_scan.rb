class Listeners::SchedulerReminderScan
  include Listeners::DomainListeners

  SCHEDULER_REMINDER_SCAN_EVENT = "scheduler.reminder.scan"
  SCHEDULER_REMINDER_SCAN_QUEUE = "q.bulk.actions.scheduler.reminder.scan"

  def self.listen
    RabbitmqConnection.subscribe(SCHEDULER_EXCHANGE, SCHEDULER_REMINDER_SCAN_EVENT, SCHEDULER_REMINDER_SCAN_QUEUE) do
      Rails.logger.info "Received message for #{SCHEDULER_REMINDER_SCAN_EVENT}"
      BulkJobService.resume_halted_jobs
    end
  end
end

