# frozen_string_literal: true

class Listeners::ListenCampaignActionCommand
  include Listeners::DomainListeners
  include ActiveModel::Valida<PERSON>

  def self.listen
    RabbitmqConnection.subscribe(CAMPAIGN_EXCHANGE, CAMPAIGN_ACTION_COMMAND, CAMPAIGN_ACTION_COMMAND_QUEUE) do |payload|
      begin
        data = JSON.parse(payload)
        Rails.logger.info "Received message for #{CAMPAIGN_ACTION_COMMAND}, campaign id: #{data['campaign']['id']}, execution action: #{data['executionAction']}"
        Rails.logger.info "#{CAMPAIGN_ACTION_COMMAND} payload: #{data}"

        Rails.logger.info "Processing campaign action command: #{data['executionAction']} for campaign: #{data['campaign']['id']}, tenant: #{data['tenantId']}, user: #{data['userId']}"

        process_campaign_action_command(data)
        
        Rails.logger.info "Processed campaign action command: #{data['executionAction']} for campaign: #{data['campaign']['id']}, tenant: #{data['tenantId']}, user: #{data['userId']}"
      rescue StandardError => e
        Rails.logger.error "Error processing campaign action command: #{e.message}, campaign id: #{data.dig('campaign', 'id')}"
      end
    end
  end

  def self.process_campaign_action_command(data)
    campaign = data['campaign']
    creator_id = data.dig('creator', 'id') || data['creator']
    tenant_id = data['tenantId']
    user_id = data.dig('userId', 'id') || data['userId']
    started_by_user_id = data.dig('activity', 'startedBy', 'id')
    started_by_user_name = data.dig('activity', 'startedBy', 'name')

    permissions = admin_permissions
    token = GenerateToken.new("#{started_by_user_id}", "#{tenant_id}", permissions).call

    begin
      Thread.current[:token] = token
      tenant = Tenant.find_or_create_by(id: tenant_id)
      tenant_id = tenant.id
      Rails.logger.info "Created or found tenant: #{tenant_id}"
      Thread.current[:user] = User.find_or_create_by(id: started_by_user_id) do |user|
        user.tenant_id = tenant_id
        user.token = token
        user.name = started_by_user_name
      end

      activity = data['activity']

      case data['executionAction']
      when START
        create_bulk_job_for_activity(activity, campaign, started_by_user_id, tenant_id)
      when PAUSE, RESUME, ABORT
        process_campaign_status_change(activity, campaign, tenant_id, data['executionAction'])
      end
    end
  end

  def self.admin_permissions
    [
      {
        id: 1,
        name:  LEAD,
        description: 'has access to lead',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true,
          email: true
        }
      },
      {
        id: 2,
        name: CONTACT,
        description: 'has access to contact',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true,
          email: true
        }
      },
      {
        id: 3,
        name: 'sms',
        description: 'has access to sms',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          sms: true,
          write: true,
          readAll: true
        }
      },
      {
        id: 4,
        name: USER,
        description: 'has access to user',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true
        }
      },
      {
        id: 5,
        name: 'whatsappTemplate',
        description: 'has access to Whatsapp Templates',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          write: true,
          sms: true,
          readAll: true
        }
      },
      {
        id: 6,
        name: EMAIL,
        description: 'has access to email',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          write: true,
          email: true,
          readAll: true
        }
      },
      {
        id: 7,
        name: 'email_template',
        description: 'has access to email Templates',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          readAll: true
        }
      }
    ]
  end

  def self.create_bulk_job_for_activity(activity, campaign, started_by_user_id, tenant_id)
    activity_id = activity['id']
    activity_name = activity['name']
    entity = activity['entity']
    filters = activity['filters']
    payload = activity['payload']

    event_data = {
      campaignId: campaign['id'],
      activityId: activity_id,
      entity: entity,
      userId: started_by_user_id,
      tenantId: tenant_id
    }

    case payload['type']
    when 'WHATSAPP'
      begin
        bulk_job_params = {
          entity: entity.downcase,
          operation: WHATSAPP_MESSAGE_OPERATION,
          filters: filters,
          payload: {
            connectedAccount: payload['connectedAccount'],
            whatsappTemplate: payload['whatsappTemplate'],
            messageSendTo: [
              {
                "name": payload['sentTo'] == 'PRIMARY_PHONE_NUMBER' ? "Record's primary phone number" : 'All Available Phone numbers',
                "type": payload['sentTo']
              }
            ],
            campaign: {
              id: campaign['id'],
              name: campaign['name']
            },
            activity: {
              id: activity_id,
              name: activity_name
            }
          },
          category: CAMPAIGN_ACTION
        }

        if payload['retryConfig'].present?
          bulk_job_params[:payload][:retryConfig] = {
            noOfTimes: payload.dig('retryConfig', 'noOfTimes'),
            timesRetried: 0
          }
        else
          bulk_job_params[:payload][:retryConfig] = nil
        end
        
        bulk_job_id = BulkJobService.create(bulk_job_params.to_h.with_indifferent_access)
        Rails.logger.info "Created bulk job #{bulk_job_id} for Campaign Id: #{campaign['id']}, Activity id: #{activity_id}, Tenant Id: #{tenant_id}"
      rescue ExceptionHandler::InsufficientWhatsappCreditsForBulkAction => e
        Rails.logger.error "Insufficient WhatsApp credits for bulk action, Error: #{e.message}, Tenant Id: #{tenant_id}, Campaign Id: #{campaign['id']}, Activity Id: #{activity_id}"

        publish_campaign_activity_job_status_event(event_data)
      rescue ExceptionHandler::InvalidDataError => e
        Rails.logger.error "Invalid data for bulk job creation, Error: #{e.message}, Tenant Id: #{tenant_id}, Campaign Id: #{campaign['id']}, Activity Id: #{activity_id}"

        publish_campaign_activity_job_status_event(event_data)
      rescue StandardError => e
        Rails.logger.error "Error creating bulk job, Error: #{e.message}, Tenant Id: #{tenant_id}, Campaign Id: #{campaign['id']}, Activity Id: #{activity_id}"

        publish_campaign_activity_job_status_event(event_data)
      end
    when 'EMAIL'
      begin
        bulk_job_params = {
          entity: entity.downcase,
          operation: EMAIL_OPERATION,
          filters: filters,
          payload: {
            emailTemplate: payload['emailTemplate'],
            sentTo: payload['sentTo'],
            fromId: payload['fromId'],
            campaign: {
              id: campaign['id'],
              name: campaign['name']
            },
            activity: {
              id: activity_id,
              name: activity_name
            },
            trackingEnabled: true
          },
          category: CAMPAIGN_ACTION
        }

        bulk_job_id = BulkJobService.create(bulk_job_params.to_h.with_indifferent_access)
        Rails.logger.info "Created bulk job #{bulk_job_id} for Campaign Id: #{campaign['id']}, Activity id: #{activity_id}, Tenant Id: #{tenant_id}"
      rescue ExceptionHandler::InvalidDataError => e
        Rails.logger.error "Invalid data for bulk job creation, Error: #{e.message}, Tenant Id: #{tenant_id}, Campaign Id: #{campaign['id']}, Activity Id: #{activity_id}"

        publish_campaign_activity_job_status_event(event_data)
      rescue StandardError => e
        Rails.logger.error "Error creating bulk job, Error: #{e.message}, Tenant Id: #{tenant_id}, Campaign Id: #{campaign['id']}, Activity Id: #{activity_id}"

        publish_campaign_activity_job_status_event(event_data)
      end
    else
      Rails.logger.info "Skipping unsupported activity type #{payload['type']} for activity #{activity_id} in campaign #{campaign['id']}"
    end
  end

  def self.publish_campaign_activity_job_status_event(event_data, status = STATUS_FAILED.upcase)
    event_data[:jobStatus] = status
    Publishers::CampaignActivityBulkJobStatusPublisher.call(event_payload: event_data)
  end

  def self.process_campaign_status_change(activity, campaign, tenant_id, execution_action)
    activity_id = activity['id']
    payload = activity['payload']
    Rails.logger.info "Processing campaign status change for activity: #{activity_id}, campaign: #{campaign['id']}, execution action: #{execution_action}"

    if payload.blank? || !['WHATSAPP', 'EMAIL'].include?(payload['type'])
      Rails.logger.info "Skipping unsupported activity type #{payload&.dig('type')} for activity #{activity_id} in campaign #{campaign['id']}"
      return
    end

    bulk_job = BulkJob.where(
      "(payload->'activity'->>'id')::integer = ? AND category = ? AND tenant_id = ?",
      activity_id,
      CAMPAIGN_ACTION,
      tenant_id
    )&.first

    if bulk_job.blank?
      Rails.logger.error "No bulk jobs found for activity: #{activity_id}, campaign: #{campaign['id']}, tenant: #{tenant_id}"
      return
    end

    begin
      case execution_action
      when PAUSE
        Rails.logger.info "Pausing bulk job #{bulk_job.id} for activity: #{activity_id}, campaign: #{campaign['id']}, tenant: #{tenant_id}"
        bulk_job_service = BulkJobService.new(bulk_job.id, can_change_campaign_bulk_job_status: true)
        bulk_job_service.pause
        Rails.logger.info "Paused bulk job #{bulk_job.id} for activity: #{activity_id}, campaign: #{campaign['id']}, tenant: #{tenant_id}"
      when RESUME
        Rails.logger.info "Resuming bulk job #{bulk_job.id} for activity: #{activity_id}, campaign: #{campaign['id']}, tenant: #{tenant_id}"
        bulk_job_service = BulkJobService.new(bulk_job.id, can_change_campaign_bulk_job_status: true)
        bulk_job_service.resume
        Rails.logger.info "Resumed bulk job #{bulk_job.id} for activity: #{activity_id}, campaign: #{campaign['id']}, tenant: #{tenant_id}"
      when ABORT
        Rails.logger.info "Aborting bulk job #{bulk_job.id} for activity: #{activity_id}, campaign: #{campaign['id']}, tenant: #{tenant_id}"
        bulk_job_service = BulkJobService.new(bulk_job.id, can_change_campaign_bulk_job_status: true)
        bulk_job_service.abort
        Rails.logger.info "Aborted bulk job #{bulk_job.id} for activity: #{activity_id}, campaign: #{campaign['id']}, tenant: #{tenant_id}"
      end
    rescue StandardError => e
      Rails.logger.error "Error processing #{execution_action} command for bulk job #{bulk_job.id}: #{e.message}"
    end
  end
end
