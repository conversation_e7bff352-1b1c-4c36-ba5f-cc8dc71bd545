# frozen_string_literal: true

class Listeners::ListenWhatsappMessageStatusUpdated
  include Listeners::<PERSON><PERSON><PERSON><PERSON><PERSON>

  def self.listen
    RabbitmqConnection.subscribe(MESSAGE_EXCHANGE, WHATSAPP_MESSAGE_STATUS_UPDATED, WHATSAPP_MESSAGE_STATUS_UPDATED_QUEUE) do |payload|
      begin
        payload = JSON.parse(payload)

        Rails.logger.info "Received message for #{WHATSAPP_MESSAGE_STATUS_UPDATED}, message_id #{payload['id']}"

        phone_number_wise_record_detail = PhoneNumberWiseRecordDetail.find_by(message_id: payload['id'])

        unless phone_number_wise_record_detail.present?
          Rails.logger.error "ListenWhatsappMessageStatusUpdated | No record found with message_id #{payload['id']}"
          return
        end

        Rails.logger.info "ListenWhatsappMessageStatusUpdated | Record found: #{phone_number_wise_record_detail.inspect}"

        phone_number_wise_record_detail.update!(message_status: payload["status"], status: STATUS_SUCCEEDED)

        Rails.logger.info "ListenWhatsappMessageStatusUpdated | Updated message_status for record with message_id #{payload['id']} to #{payload['status']}"
      rescue => e
        Rails.logger.error "ListenWhatsappMessageStatusUpdated | Error processing message: #{e.message}"  
      end
    end
  end
end
