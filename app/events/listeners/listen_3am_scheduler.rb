# frozen_string_literal: true

class Listeners::Listen3amScheduler
  include Listeners::DomainListeners

  EVENT_3AM = 'scheduler.3am'
  EVENT_3AM_QUEUE = 'q.bulk.actions.scheduler.3am'

  def self.listen
    RabbitmqConnection.subscribe(SCHEDULER_EXCHANGE, EVENT_3AM, EVENT_3AM_QUEUE) do
      Rails.logger.info "Received message for #{EVENT_3AM}"
      BulkJobService.move_paused_jobs_to_aborted
    end
  end
end
