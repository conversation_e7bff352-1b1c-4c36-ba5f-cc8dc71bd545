# frozen_string_literal: true

class Listeners::ListenEvery15MinuteScheduler
  include Listeners::DomainListeners

  EVENT_EVERY_15_MINUTES = 'scheduler.every15Minute'
  EVENT_EVERY_15_MINUTES_QUEUE = 'q.scheduler.every15Minute.bulkActions'

  def self.listen
    RabbitmqConnection.subscribe(SCHEDULER_EXCHANGE, EVENT_EVERY_15_MINUTES, EVENT_EVERY_15_MINUTES_QUEUE) do
      Rails.logger.info "Received message for #{EVENT_EVERY_15_MINUTES}"
      BulkJobService.enqueue_retryable_jobs
    end
  end
end
