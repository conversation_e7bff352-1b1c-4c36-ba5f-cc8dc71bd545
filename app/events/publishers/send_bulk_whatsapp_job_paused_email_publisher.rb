# frozen_string_literal: true

class Publishers::SendBulkWhatsappJobPausedEmailPublisher
  def self.call(bulk_job, pause_reason)
    Rails.logger.info "Bulk Actions Service: Publishers::SendBulkWhatsappJobPausedEmailPublisher called"
    data = BulkJobSerializer::Details.serialize_bulk_whatsapp_job_paused_email(bulk_job, pause_reason)

    event = Event::BulkWhatsappJobPaused.new(data)
    PublishEvent.new(event).call

    Rails.logger.info "Event::BulkWhatsappJobPaused is sent, data #{event.to_json}"
  end
end
