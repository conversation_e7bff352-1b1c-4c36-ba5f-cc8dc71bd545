# frozen_string_literal: true

class Publishers::WhatsappCampaignActivityAbortedPublisher
  def self.call(bulk_job, retryable_message_ids)
    Rails.logger.info "Bulk Actions Service: Publishers::WhatsappCampaignActivityAbortedPublisher called, bulk job id: #{bulk_job.id}, tenant id: #{bulk_job.tenant_id}"

     data = {
      campaignId: bulk_job.payload.dig('campaign', 'id'),
      activityId: bulk_job.payload.dig('activity', 'id'),
      bulkJobId: bulk_job.id,
      entity: bulk_job.entity&.upcase,
      userId: bulk_job.user_id,
      tenantId: bulk_job.tenant_id,
      messageIds: retryable_message_ids
    }

    event = Event::WhatsappCampaignActivityAborted.new(data)
    PublishEvent.new(event).call

    Rails.logger.info "Event::WhatsappCampaignActivityAbortedPublisher is sent, data: #{event.to_json}"
  end
end
