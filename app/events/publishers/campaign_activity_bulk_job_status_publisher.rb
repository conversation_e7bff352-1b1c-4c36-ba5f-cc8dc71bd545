# frozen_string_literal: true

class Publishers::CampaignActivityBulkJobStatusPublisher
  def self.call(bulk_job: nil, status: nil, event_payload: {}, status_reason: nil)
    Rails.logger.info 'Bulk Actions Service: Publishers::CampaignActivityBulkJobStatusPublisher called'

    if bulk_job.present?
      data = {
        campaignId: bulk_job.payload.dig('campaign', 'id'),
        activityId: bulk_job.payload.dig('activity', 'id'),
        jobStatus: status,
        entity: bulk_job.entity&.upcase,
        userId: bulk_job.user_id,
        tenantId: bulk_job.tenant_id
      }

      data[:statusReason] = status_reason if status_reason.present?
    elsif event_payload.present?
      data = event_payload
    end

    event = Event::CampaignActivityBulkJobStatus.new(data)
    PublishEvent.new(event).call

    Rails.logger.info "Event::CampaignActivityBulkJobStatus is sent, data: #{event.to_json}"
  end
end
