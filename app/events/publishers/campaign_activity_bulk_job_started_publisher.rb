# frozen_string_literal: true

class Publishers::CampaignActivityBulkJobStartedPublisher
  def self.call(bulk_job, entity_details)
    Rails.logger.info 'Bulk Actions Service: Publishers::CampaignActivityBulkJobStartedPublisher called'

    data = {
      entityDetails: entity_details,
      campaignId: bulk_job.payload.dig('campaign', 'id'),
      activityId: bulk_job.payload.dig('activity', 'id'),
      bulkJobId: bulk_job.id,
      entity: bulk_job.entity&.upcase,
      userId: bulk_job.user_id,
      tenantId: bulk_job.tenant_id
    }

    event = Event::CampaignActivityBulkJobStarted.new(data)
    PublishEvent.new(event).call

    Rails.logger.info "Event::CampaignActivityBulkJobStarted is sent, CampaignId: #{bulk_job.payload.dig('campaign', 'id')}, ActivityId: #{bulk_job.payload.dig('activity', 'id')}, BulkJobId: #{bulk_job.id}"
  end
end
