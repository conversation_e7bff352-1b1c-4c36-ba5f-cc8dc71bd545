# frozen_string_literal: true

class Publishers::CampaignEntityActivityStatusPublisher
  def self.call(event_payload)
    Rails.logger.info 'Bulk Actions Service: Publishers::CampaignEntityActivityStatusPublisher called'

    event = Event::CampaignEntityActivityStatus.new(event_payload)
    PublishEvent.new(event).call

    Rails.logger.info "Event::CampaignEntityActivityStatus is sent, data: #{event.to_json}"
  end
end
