class BulkJobService
  MAX_ATTACHMENT_SIZE = 25 * 1064 * 1064

  def initialize(id, can_change_campaign_bulk_job_status: false)
    @bulk_job = BulkJob.find_by(id: id)
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless @bulk_job

    @can_change_campaign_bulk_job_status = can_change_campaign_bulk_job_status
  end

  def self.create params
    if EmbarkPlanRestrictionService.embark_plan?
      raise(ExceptionHandler::Forbidden, "#{ErrorCode.unauthorized}||Your current plan does not support bulk actions.")
    end
    
    begin
      if params[:entity] == NOTE
        raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized) unless has_permission_to_perform(params)
      end

      bulk_job = BulkJob.new(params.transform_keys(&:underscore))
      current_user = Thread.current[:user]
      bulk_job.user_id = current_user.id
      bulk_job.tenant_id = current_user.tenant_id
      bulk_job.status = STATUS_QUEUING

      if bulk_job.operation == 'whatsappMessage'
        bulk_job.operation = WHATSAPP_MESSAGE_OPERATION

        bulk_message_credits_status = GetWhatsappCreditsStatusForBulkAction.new(Thread.current[:token]).call

        Rails.logger.info "Bulk message credits status: #{bulk_message_credits_status}" 
        unless bulk_message_credits_status['availableForBulkMessages']
          raise(ExceptionHandler::InsufficientWhatsappCreditsForBulkAction, "#{ErrorCode.insufficient_whatsapp_credits_for_bulk}||#{I18n.t('whatsapp_message.insufficient_whatsapp_credits_for_bulk')}")
        end

        if bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaId').present?
          validate_dynamic_template_media(bulk_job)
        end
      end

      BulkJob.transaction do
        if bulk_job.operation == EMAIL_OPERATION
          %w[cc bcc].each do |key|
            val = bulk_job.payload[key]
            if val.is_a?(String)
              bulk_job.payload[key] =
                begin
                  JSON.parse(val)
                rescue
                  val
                end
            end
          end
          bulk_job.save!
          validate_and_upload_attachments(params, bulk_job.remote_file_location)
        else
          bulk_job.save!
        end
        current_user.update!(token: Thread.current[:token])
      end
      QueueBulkJob.perform_later(bulk_job.id)
      bulk_job.id
    rescue ArgumentError, ActiveRecord::RecordInvalid => e
      Rails.logger.error "Error while creating Bulk Job: #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{e.message}")
    end
  end

  def self.list(params)
    current_user = Thread.current[:user]
    paginated_params = validate_pagination_params params

    # Filtering
    filtered_bulk_jobs =
      if params[:json_rule]
        json_rules = params.dig(:json_rule, :rules) || []
        bulk_jobs_criteria = JsonRule::Evaluator.new(json_rules, current_user).evaluate
        BulkJob.joins(
          "LEFT OUTER JOIN summary_files AS error_summary_files ON \
          error_summary_files.bulk_job_id = bulk_jobs.id \
          AND \
          error_summary_files.file_type = 'error' \
          LEFT OUTER JOIN summary_files AS success_summary_files ON \
          success_summary_files.bulk_job_id = bulk_jobs.id \
          AND \
          success_summary_files.file_type = 'success'"
        ).where(bulk_jobs_criteria)
      else
        BulkJob.where(tenant_id: current_user.tenant_id)
      end

    bulk_jobs = filtered_bulk_jobs.paginate(
      page: paginated_params[:page].to_i + 1, per_page: paginated_params[:per_page]
    ).order("#{paginated_params[:sort_by]} #{paginated_params[:order]}")

    BulkJobSerializer::List.serialize(bulk_jobs)
  end

  def self.validate_pagination_params params
    sort_params = params[:sort].to_s.split(',')
    paginated_params = { page: params[:page].to_i, per_page: params[:size].to_i, sort_by: sort_params[0].to_s.underscore,  order: sort_params[1].to_s.upcase }
    if paginated_params[:page] < 0 || paginated_params[:per_page] < 0 || !BulkJob.attribute_names.include?(paginated_params[:sort_by]) || !['ASC', 'DESC'].include?(paginated_params[:order])
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
      Rails.logger.error "Invalid pagination parameters #{paginated_params}"
    end
    paginated_params
  end

  def summary_file
    check_file_download_permission
    file = @bulk_job.summary_file
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless file
    { url: GetPresignedS3Url.remote_url("#{@bulk_job.remote_file_location}/#{SummaryFile::SUCCESS_FILE_NAME}", @bulk_job.success_file_name) }
  end

  def error_summary_file
    check_file_download_permission
    file = @bulk_job.error_summary_file
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless file
    { url: GetPresignedS3Url.remote_url("#{@bulk_job.remote_file_location}/#{SummaryFile::ERROR_FILE_NAME}", @bulk_job.error_file_name) }
  end

  def attachment
    check_file_download_permission
    if @bulk_job.operation == WHATSAPP_MESSAGE_OPERATION
      media_name = @bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaName')
      raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless media_name
      { url: GetPresignedS3Url.remote_url("#{@bulk_job.remote_file_location}/#{media_name}", "#{@bulk_job.id}-#{media_name}") }
    else
      raise(ExceptionHandler::NotFound, ErrorCode.not_found)
    end
  end

  def self.resume_halted_jobs
    BulkJob.where.not(sidekiq_job_id: nil).find_each do |job|
      next if sidekiq_job_exist_by_id?(job.sidekiq_job_id, job.sidekiq_queue_name)

      if job.queuing?
        QueueBulkJob.perform_later(job.id)
      else
        BulkActionJob.set(queue: job.sidekiq_queue_name).perform_later job.id
      end
    end
  end

  def abort
    validate_status('abort')

    prev_status = @bulk_job.status

    @bulk_job.update(status: STATUS_ABORTING, sidekiq_job_id: nil)
    Rails.logger.info "Bulk Job #{@bulk_job.id} aborted"
    AbortBulkJob.perform_later(@bulk_job.id) if [STATUS_PAUSED, STATUS_QUEUED, STATUS_RETRYING].include?(prev_status)

    schedule_next_queued_job
  end

  def pause
    validate_status('pause')

    @bulk_job.update(status: STATUS_PAUSED, sidekiq_job_id: nil)
    Rails.logger.info "Bulk Job #{@bulk_job.id} paused"
    schedule_next_queued_job
  end

  def resume
    validate_status('resume')

    @bulk_job.queued!
    Rails.logger.info "Bulk Job #{@bulk_job.id} resumed"
    unless @bulk_job.tenant.running_job?
      BulkActionJob.set(queue: @bulk_job.sidekiq_queue_name).perform_later(@bulk_job.id)
    end
  end

  def delete
    validate_status('delete')

    begin
      files_to_delete = []
      if @bulk_job.summary_file.present?
        files_to_delete << "#{@bulk_job.remote_file_location}/#{SummaryFile::SUCCESS_FILE_NAME}"
        @bulk_job.summary_file.destroy!
      end

      if @bulk_job.error_summary_file.present?
        files_to_delete << "#{@bulk_job.remote_file_location}/#{SummaryFile::ERROR_FILE_NAME}"
        @bulk_job.error_summary_file.destroy!
      end

      if @bulk_job.operation == WHATSAPP_MESSAGE_OPERATION && @bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaName').present?
        files_to_delete << "#{@bulk_job.remote_file_location}/#{@bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaName')}"
      end

      DeleteFileFromS3.new(files_to_delete).call if files_to_delete.any?

      current_tenant = @bulk_job.tenant
      @bulk_job.records.destroy_all
      @bulk_job.destroy!

      schedule_next_queued_job unless current_tenant.running_job?
    rescue => e
      Rails.logger.error "Error while deleting Bulk Job: #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.delete_error}||#{e.message}")
    end
  end

  def self.move_paused_jobs_to_aborted
    bulk_job_ids = BulkJob.where(status: STATUS_PAUSED).where("started_at < ?", 7.days.ago).pluck(:id)
    BulkJob.where(id: bulk_job_ids).update_all(status: STATUS_ABORTING)

    BulkJob.where(id: bulk_job_ids).each do |bulk_job|
      success_count = Record.where(bulk_job_id: bulk_job.id, tenant_id: bulk_job.tenant_id, status: Record.statuses["succeeded"]).count
      failure_count = Record.where(bulk_job_id: bulk_job.id, tenant_id: bulk_job.tenant_id, status: Record.statuses["failed"]).count
      CsvGenerator.new(bulk_job).call

      bulk_job.records.destroy_all
      bulk_job.update(
        status: STATUS_ABORTED,
        sidekiq_job_id: nil,
        successful: success_count,
        failure: failure_count
      )
    end
  end

  def self.enqueue_retryable_jobs
    BulkJob.where(status: [STATUS_RETRYING])
        .where("payload->'retryConfig'->>'nextRetryAt' IS NOT NULL")
        .where("CAST(payload->'retryConfig'->>'nextRetryAt' AS timestamp) < ?", DateTime.now).find_each do |bulk_job|
      Rails.logger.info "Enqueuing retryable Bulk Job #{bulk_job.id}, status: #{bulk_job.status}, bulk_job id: #{bulk_job.id}, tenant id: #{bulk_job.tenant_id}"
      bulk_job.update(status: STATUS_QUEUED, sidekiq_job_id: nil)

      BulkActionJob.set(queue: bulk_job.sidekiq_queue_name).perform_later(bulk_job.id) unless bulk_job.tenant.running_job?
    end
  end

  private_class_method :validate_pagination_params

  private

  def validate_status(status)
    status_allowed_response = @bulk_job.status_change_allowed?(status)
    
    status_allowed_response = { allowed: false, error: 'campaign_action_job_update_not_allowed' } if @bulk_job.category == CAMPAIGN_ACTION && !@can_change_campaign_bulk_job_status

    unless status_allowed_response[:allowed]
      if status_allowed_response[:error] == 'invalid_status'
        raise(
          ExceptionHandler::InvalidDataError,
          "#{ErrorCode.send("#{status}_error")}||#{I18n.t('bulk_jobs.cannot_change_status', current: @bulk_job.status, to: status)}"
        )
      elsif status_allowed_response[:error] == 'campaign_action_job_update_not_allowed'
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('bulk_jobs.cannot_change_campaign_action_job_status')}")
      end

      raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
    end
  end

  def schedule_next_queued_job
    next_queued_job = @bulk_job.tenant.first_queued_job
    BulkActionJob.set(queue: next_queued_job.sidekiq_queue_name).perform_later(next_queued_job.id) if next_queued_job
  end

  def self.sidekiq_job_exist_by_id?(sidekiq_job_id, sidekiq_queue_name)
    return true if Sidekiq::WorkSet.new.find_all.any?{|pid, tid, work| work['payload']['args']&.first['job_id']== sidekiq_job_id }
    Sidekiq::Queue.new(sidekiq_queue_name).any?{|c| c['args']&.first['job_id']== sidekiq_job_id}
  end

  def check_file_download_permission
    current_user = Thread.current[:user]
    return true if @bulk_job.user_id.eql? current_user.id
    token = Thread.current[:token]
    auth_data = Auth::TokenParser.parse(token)
    return true if((@bulk_job.tenant_id.eql? current_user.tenant_id) && auth_data.can_access?('lead','read_all'))
    raise(ExceptionHandler::Forbidden, ErrorCode.unauthorized)
  end

  def self.validate_and_upload_attachments(params, remote_file_location)
    if params.dig('payload', 'attachments').present? && params['payload']['attachments'].is_a?(Array)
      total_file_size =
        params['payload']['attachments'].map { |attachment| attachment['data'].path }
                                        .inject(0) { |sum, file| sum + File.size(file) }
      if total_file_size > MAX_ATTACHMENT_SIZE
        Rails.logger.error "BulkJobService FileSizeExceeded"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.file_size_limit_exceeded}||File Size Limit Exceeded")
      end

      params['payload']['attachments'].each do |attachment|
        FileUploader.upload(attachment['data'].path, "#{remote_file_location}/#{attachment['fileName']}")
      end
    end
  end

  def self.has_permission_to_perform(params)
    auth_data = Auth::TokenParser.parse(Thread.current[:token])
    auth_data.can_access?(params[:entity], params[:operation])
  end

  def self.validate_dynamic_template_media(bulk_job)
    connected_account_id = bulk_job.payload.dig('connectedAccount', 'id')
    template_media_id = bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaId')

    begin
      RestClient.get(
        "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/#{connected_account_id}/template-media/#{template_media_id}",
        { Authorization: "Bearer #{Thread.current[:token]}" }
      )
    rescue RestClient::Exception => e
      Rails.logger.error "Error validating WhatsApp template media: #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{e.message}")
    end
  end
end
