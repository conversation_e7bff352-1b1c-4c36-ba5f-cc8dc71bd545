class CsvGenerator
  attr_accessor :bulk_job

  def initialize job
    @bulk_job = job
  end

  def call
    return nil unless @bulk_job

    return nil if [STATUS_COMPLETED, STATUS_ABORTING, STATUS_ABORTED].exclude?(@bulk_job.status)

    generate_success_csv
    generate_error_csv
  end

  private

  def generate_success_csv
    return unless Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["succeeded"]).exists?
    file = Tempfile.new("summary_#{@bulk_job.id}.csv")
    command = "\\copy (#{success_query}) to '#{file.path}' with csv header"
    copy_data_from_pg command
    FileUploader.upload(file.path, "#{@bulk_job.remote_file_location}/#{SummaryFile::SUCCESS_FILE_NAME}")
    @bulk_job.create_summary_file
    file.unlink
  end

  def generate_error_csv
    return unless Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["failed"]).exists?
    file = Tempfile.new("error_#{@bulk_job.id}.csv")
    command = "\\copy (#{error_query}) to '#{file.path}' with csv header"
    copy_data_from_pg command
    FileUploader.upload(file.path, "#{@bulk_job.remote_file_location}/#{SummaryFile::ERROR_FILE_NAME}")
    @bulk_job.create_error_summary_file
    file.unlink
  end

  def copy_data_from_pg command
    config = SdBulkActions::Application.config.database_configuration[::Rails.env]
    dbhost, dbuser, dbname, dbpassword, port = config['host'], config['username'], config['database'], config['password'], config['port']
    sql_command = "PGPASSWORD=#{dbpassword} psql -U #{dbuser} -p #{port} -h #{dbhost} #{dbname} -c \"#{command}\""
    `#{sql_command}`
  end

  def success_query
    if @bulk_job.entity == DEAL && @bulk_job.operation == EMAIL_OPERATION
      Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["succeeded"]).select("parent_entity_id AS deal_id, entity_id as contact_id").to_sql
    elsif @bulk_job.entity == NOTE
      Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["succeeded"]).select(
        "entity_id,
        (relations->>'lead_id')::int AS lead_id,
        (relations->>'deal_id')::int AS deal_id,
        (relations->>'contact_id')::int AS contact_id,
        (relations->>'task_id')::int AS task_id,
        (relations->>'meeting_id')::int AS meeting_id,
        (relations->>'call_id')::int AS call_id"
      ).to_sql
    elsif @bulk_job.operation == WHATSAPP_MESSAGE_OPERATION
      if @bulk_job.entity == DEAL
        "SELECT records.parent_entity_id AS deal_id, records.entity_id AS contact_id,
        phone_wise_details.phone_number_for_log AS phone_number
        FROM records LEFT OUTER JOIN phone_number_wise_record_details phone_wise_details
        ON records.id = phone_wise_details.record_id
        WHERE phone_wise_details.status = 'succeeded' and
        records.bulk_job_id = #{@bulk_job.id}
        ORDER BY parent_entity_id"
      else
        "SELECT records.entity_id AS entity_id, phone_wise_details.phone_number_for_log AS phone_number
        FROM records LEFT OUTER JOIN phone_number_wise_record_details phone_wise_details
        ON records.id = phone_wise_details.record_id
        WHERE phone_wise_details.status = 'succeeded' and
        records.bulk_job_id = #{@bulk_job.id}
        ORDER BY entity_id"
      end
    else
      Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["succeeded"]).select("entity_id").to_sql
    end
  end

  def error_query
    if @bulk_job.entity == DEAL && @bulk_job.operation == EMAIL_OPERATION
      Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["failed"]).select("parent_entity_id AS deal_id, CASE WHEN entity_id = 0 THEN NULL ELSE entity_id END AS contact_id, error_message").to_sql
    elsif @bulk_job.entity == NOTE
      Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["failed"]).select(
        "entity_id,
        (relations->>'lead_id')::int AS lead_id,
        (relations->>'deal_id')::int AS deal_id,
        (relations->>'contact_id')::int AS contact_id,
        (relations->>'task_id')::int AS task_id,
        (relations->>'meeting_id')::int AS meeting_id,
        (relations->>'call_id')::int AS call_id,
        error_message"
      ).to_sql
    elsif @bulk_job.operation == WHATSAPP_MESSAGE_OPERATION
      if @bulk_job.entity == DEAL
        "SELECT records.parent_entity_id AS deal_id, records.entity_id AS contact_id, phone_wise_details.phone_number_for_log AS phone_number,
        CASE
          WHEN phone_wise_details.phone_number IS NULL AND records.entity_id = 0 THEN 'No associated contacts are present on the deal record'
          WHEN phone_wise_details.phone_number IS NULL AND records.entity_id != 0 THEN 'No phone numbers are present on the entity'
          ELSE phone_wise_details.error_message
        END AS error_message
        FROM
          records LEFT OUTER JOIN phone_number_wise_record_details phone_wise_details
        ON records.id = phone_wise_details.record_id
        WHERE (phone_wise_details.status = 'failed' OR (records.status = 3 AND phone_wise_details.record_id IS NULL)) AND
        records.bulk_job_id = #{@bulk_job.id}
        ORDER BY parent_entity_id"
      else
        "SELECT records.entity_id AS entity_id, phone_wise_details.phone_number_for_log AS phone_number,
        CASE
          WHEN phone_wise_details.phone_number IS NULL THEN 'No phone numbers are present on the entity'
          ELSE phone_wise_details.error_message
        END AS error_message
        FROM
          records LEFT OUTER JOIN phone_number_wise_record_details phone_wise_details
        ON records.id = phone_wise_details.record_id
        WHERE (phone_wise_details.status = 'failed' OR (records.status = 3 AND phone_wise_details.record_id IS NULL)) AND
        records.bulk_job_id = #{@bulk_job.id}
        ORDER BY entity_id"
      end
    else
      Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["failed"]).select("entity_id, error_message").to_sql
    end
  end
end
