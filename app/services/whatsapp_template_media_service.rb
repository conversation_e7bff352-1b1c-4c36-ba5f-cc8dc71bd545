class WhatsappTemplateMediaService
  def initialize(bulk_job)
    @bulk_job = bulk_job
    @connected_account_id = bulk_job.payload.dig('connectedAccount', 'id')
    @template_media_id = bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaId')
  end

  def download_and_upload
    media_details = fetch_media_details

    downloaded_file = download_file(media_details['mediaUrl']['url'], media_details['fileName'])

    upload_to_s3(downloaded_file, media_details)
    whatsapp_template = @bulk_job.payload.dig('whatsappTemplate')
    updated_template = {
      'id' => whatsapp_template['id'],
      'name' => whatsapp_template['name'],
      'dynamicTemplateMediaId' => whatsapp_template['dynamicTemplateMediaId'],
      'dynamicTemplateMediaName' => media_details['fileName']
    }
    @bulk_job.update(payload: @bulk_job.payload.merge('whatsappTemplate' => updated_template))
  end

  private

  def fetch_media_details
    response = RestClient.get(
      "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/#{@connected_account_id}/template-media/#{@template_media_id}",
      { Authorization: "Bearer #{@bulk_job.user.token}" }
    )
    JSON.parse(response.body)
  rescue RestClient::Exception => e
    Rails.logger.error "Error fetching WhatsApp template media details: #{e.message}"
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{e.message}")
  end

  def download_file(url, file_name)
    response = RestClient.get(url)
    temp_file = Tempfile.new(file_name)
    temp_file.binmode
    temp_file.write(response.body)
    temp_file.rewind
    temp_file
  rescue RestClient::Exception => e
    Rails.logger.error "Error downloading WhatsApp template media: #{e.message}"
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{e.message}")
  end

  def upload_to_s3(file, media_details)
    FileUploader.upload(file.path, "#{@bulk_job.remote_file_location}/#{media_details['fileName']}")
  end
end
