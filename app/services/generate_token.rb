class GenerateToken
  def initialize(user_id, tenant_id, permissions, profile_id_empty = false)
    @user_id = user_id
    @tenant_id = tenant_id
    @permissions = permissions
    @profile_id_empty = profile_id_empty
  end

  def call
    payload = {
      data: {
        accessToken: "1c77c32f-5f5b-4342-91b2-961e38045498",
        expiresIn: 3600,
        expiry: (DateTime.now  + 1.hour).to_i,
        userId: @user_id,
        tenantId: @tenant_id,
        tokenType: 'Bearer',
        permissions: @permissions
      }
    }

    if @profile_id_empty
      payload[:data][:meta] = {
        pid: nil
      }
    end

    Rails.logger.info "GenerateToken:: Generating token for User: #{@user_id}, tenant id: #{@tenant_id}"
    token = JWT.encode payload, JWT_KEY, algorithm='HS256', header_fields={}
    token
  end
end
