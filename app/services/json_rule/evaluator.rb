# frozen_string_literal: true

# Evalutes the JsonRule
class JsonRule::Evaluator
  OPERATOR_MAPPING = {
    'equal' => '=',
    'not_equal' => '!=',
    'greater' => '>',
    'less' => '<',
    'greater_or_equal' => '>=',
    'less_or_equal' => '<=',
    'between' => 'between',
    'not_between' => 'not between',
    'in' => 'in',
    'not_in' => 'not in',
    'is_null' => 'is null',
    'is_not_null' => 'is not null'
  }.freeze

  INTERNAL_FIELD_MAP = {
    'submitted_at' => 'created_at',
    'submitted_by' => 'user_id'
  }.freeze

  def initialize(rules, user)
    @field_wise_filters = YAML.safe_load(File.read(File.join(File.dirname(__FILE__), 'fieldWiseFilters.yaml')))
    @rules = rules
    validate
    @queries = ["bulk_jobs.tenant_id = #{user.tenant_id}"]
  end

  def evaluate
    @queries.concat(@rules&.map { |rule| build_query(rule) } || [])
    @queries&.join(' and ')
  end

  private

  def build_query(rule)
    field = fetch_mapped_field(rule['field'].underscore)

    field = "bulk_jobs.#{field}"

    if %w[in not_in].include?(rule['operator'])
      values = rule['value'].split(',').flatten.compact_blank
      query = ''
      if status_field?(field)
        if values.include?(COMPLETED_WITH_ERROR)
          values -= [COMPLETED_WITH_ERROR]
          case rule['operator']
          when 'in'
            query += "error_summary_files.file_type = 'error'"
            query += ' OR ' if values.present?
          when 'not_in'
            query += "
              (
                bulk_jobs.status != '4'
                or
                (
                  bulk_jobs.status = '4'
                  and
                  (
                    (
                      error_summary_files.file_type != 'error'
                      and
                      success_summary_files.file_type = 'success'
                    )
                    or
                    (error_summary_files.file_type is null)
                  )
                )
              )
            ".squish
            query += ' AND ' if values.present?
          end
        end

        if values.include?(COMPLETED)
          case rule['operator']
          when 'in'
            query += "
              (
                (
                  success_summary_files.file_type = 'success'
                  and
                  error_summary_files.file_type != 'error'
                )
                or
                (
                  error_summary_files.file_type is null
                )
              )
            ".squish
            query += ' AND ' if values.present?
          when 'not_in'
            query += "(bulk_jobs.status = '4' and error_summary_files.file_type = 'error')"
            query += ' OR ' if values.present?
          end
        end
      end

      if values.present?
        query += "#{field} #{OPERATOR_MAPPING[rule['operator']]} (#{values.map do |c|
                                                           "'#{format_values(c, field)}'"
                                                         end.join(',')})"
      end

      "(#{query.strip})"
    elsif %w[is_null is_not_null].include?(rule['operator'])
      "#{field} #{OPERATOR_MAPPING[rule['operator']]}"
    elsif %w[between not_between].include?(rule['operator'])
      "(#{field} #{OPERATOR_MAPPING[rule['operator']]} '#{rule['value'].first}' and '#{rule['value'].last}')"
    elsif %w[not_equal].include?(rule['operator'])
      if status_field?(field) && rule['value'].upcase == COMPLETED_WITH_ERROR
        "
          (
            bulk_jobs.status is not null and
            (
              error_summary_files.file_type != 'error'
              or
              error_summary_files.file_type is null
            )
          )
        ".squish
      elsif status_field?(field) && rule['value'].upcase == COMPLETED
        "
          (
            #{field} #{OPERATOR_MAPPING[rule['operator']]} '#{format_values(rule['value'], field)}'
            or
            (
              error_summary_files.file_type = 'error'
            )
          )
        ".squish
      else
        "#{field} #{OPERATOR_MAPPING[rule['operator']]} '#{format_values(rule['value'], field)}'"
      end
    elsif %w[equal].include?(rule['operator'])
      if status_field?(field) && rule['value'].upcase == COMPLETED_WITH_ERROR
        "(bulk_jobs.status = '4' and error_summary_files.file_type = 'error')"
      elsif status_field?(field) && rule['value'].upcase == COMPLETED
        "
          (
            bulk_jobs.status = '4' and
            (
              (
                success_summary_files.file_type = 'success'
                and
                error_summary_files.file_type != 'error'
              )
              or
              (
                error_summary_files.file_type is null
              )
            )
          )
        ".squish
      else
        "#{field} #{OPERATOR_MAPPING[rule['operator']]} '#{format_values(rule['value'], field)}'"
      end
    else
      "#{field} #{OPERATOR_MAPPING[rule['operator']]} '#{format_values(rule['value'], field)}'"
    end
  end

  def fetch_mapped_field(field)
    if INTERNAL_FIELD_MAP.keys.include?(field)
      INTERNAL_FIELD_MAP[field]
    else
      field
    end
  end

  def format_values(value, field)
    if status_field?(field)
      value == COMPLETED_WITH_ERROR ? ERROR : BulkJob.statuses[value&.downcase]
    else
      value.strip.downcase
    end
  rescue StandardError
    value
  end

  def validate
    return unless @rules

    invalid = @rules.any? do |rule|
      !@field_wise_filters[rule['type']] ||
        @field_wise_filters[rule['type']].exclude?(rule['operator']) ||
        (%w[between not_between].include?(rule['operator']) && !rule['value'].is_a?(Array))
    end

    invalid ||= validate_values(@rules)
    invalid && raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data} || Json Rule invalid")
  end

  def validate_values(rules)
    fields = rules.map { |rule| rule['field'] }
    if fields.include?('status')
      status_rule = rules.find { |rule| rule['field'] == 'status' }

      return false if status_rule['value'].blank?

      status_rule['value'].split(',').flatten.any? do |value|
        valid_status_values_for_filters.exclude?(value)
      end
    end
  end

  def valid_status_values_for_filters
    [COMPLETED_WITH_ERROR, BulkJob.statuses.keys.map(&:upcase)].flatten
  end

  def status_field?(field)
    ['status', 'bulk_jobs.status'].include?(field)
  end
end
