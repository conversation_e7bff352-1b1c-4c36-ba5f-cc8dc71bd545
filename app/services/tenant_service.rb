# frozen_string_literal: true

class TenantService
  def self.fetch_tenant
    response = RestClient.get(
      SERVICE_IAM + "/v1/tenants",
      {
        Authorization: "Bearer #{Thread.current[:token]}",
        content_type: :json,
        accept: :json
      }
    )
    return JSON(response.body)
  rescue RestClient::NotFound => e
    Rails.logger.error "TenantService | 404 while fetching tenant: #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
  rescue RestClient::InternalServerError => e
    Rails.logger.error "TenantService | 500 while fetching tenant: #{e.message}"
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_server_error)
  rescue RestClient::BadRequest => e
    Rails.logger.error "TenantService | 400 while fetching tenant: #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
  rescue StandardError => e
    Rails.logger.error "TenantService | Error while fetching tenant: #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
  end
end
