class FileUploader
  def self.upload(local_path, remote_path)
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    bucket = s3.bucket(S3_BULK_ACTION_BUCKET)

    begin
      obj = bucket.object(remote_path)
      obj.upload_file(local_path)
    rescue StandardError => e
      Rails.logger.error "Bulk Job: error while uploading file: #{e.message} | #{remote_path}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.file_upload_error)
    ensure
      File.delete(local_path)
    end
  end
end
