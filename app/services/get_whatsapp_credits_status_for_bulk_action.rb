# frozen_string_literal: true

require 'rest-client'

class GetWhatsappCreditsStatusForBulkAction
  def initialize(token)
    @token = token
  end

  def call
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless @token

    begin
      response = RestClient.get(
        "#{SERVICE_MESSAGE}/v1/messages/whatsapp-credits/status",
        {
          'Authorization': "Bearer #{@token}"
        }
      )

      return JSON(response.body) unless response.nil?
      Rails.logger.error 'GetWhatsappCreditsStatusForBulkAction - invalid response'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)

    rescue RestClient::NotFound
      Rails.logger.error 'GetWhatsappCreditsStatusForBulkAction - 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)

    rescue RestClient::InternalServerError
      Rails.logger.error 'GetWhatsappCreditsStatusForBulkAction - 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)

    rescue RestClient::BadRequest
      Rails.logger.error 'GetWhatsappCreditsStatusForBulkAction - 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end
