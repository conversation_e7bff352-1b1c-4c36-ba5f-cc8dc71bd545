#freeze_string_literal: true

class EmbarkPlanRestrictionService
  def self.embark_plan?
    begin
      data = TenantService.fetch_tenant
      plan_name = data && data['planName'] ? data['planName'].to_s : nil

      plan_name == EMBARK
    rescue StandardError => e
      Rails.logger.error "EmbarkPlanRestrictionService | Error while checking subscription details: #{e.message}"
      false
    end
  end
end
