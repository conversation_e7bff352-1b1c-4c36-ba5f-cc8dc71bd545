# frozen_string_literal: true

class DeleteFileFromS3
  def initialize(file_paths)
    @file_paths = file_paths
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(S3_BULK_ACTION_BUCKET)
  end

  def call
    begin
      if @file_paths.present?
        @bucket.delete_objects({
          delete: {
            objects: @file_paths.map { |path| { key: path } }
          }
        })
      end
    rescue StandardError => e
      Rails.logger.error "Failed to delete file from s3: #{e.message} | #{@file_paths}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('could_not_delete_file')}")
    end
  end
end
