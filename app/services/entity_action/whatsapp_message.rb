class EntityAction::WhatsappMessage < EntityAction::Base
  ERROR_MAPPINGS = {
    '022001' => I18n.t('authorization_error'),
    '022002' => I18n.t('whatsapp_message.no_whatsapp_template_read_permission'),
    '022003' => I18n.t('whatsapp_message.template_entity_and_preview_entity_mismatch'),
    '022006' => I18n.t('whatsapp_message.whatsapp_template_not_found'),
    '022007' => I18n.t('whatsapp_message.message_not_allowed'),
    '022017' => I18n.t('whatsapp_message.third_party_api_error'),
    '022028' => I18n.t('whatsapp_message.insufficient_whatsapp_credits_for_bulk'),
    '022029' => I18n.t('whatsapp_message.inactive_whatsapp_template'),
    '022034' => I18n.t('whatsapp_message.chatbot_in_progress')
  }

  def perform
    return nil unless @bulk_job.present?

    return nil if %[aborted completed].include?(@bulk_job.status)

    return { paused: true } if @bulk_job.paused?

    process_dynamic_media if dynamic_media_present?
    @is_retrying = false

    if @bulk_job.payload.dig('retryConfig', 'nextRetryAt').present?
      Rails.logger.info "Retrying whatsapp action bulk job, bulk job id: #{@bulk_job.id}, tenant id: #{@bulk_job.tenant_id}, timed retried: #{@bulk_job.payload.dig('retryConfig', 'timesRetried')}"
      @is_retrying = true
    end

    @bulk_job.update({ status: BulkJob.statuses[:in_progress], started_at: Time.now })
    skip_sleep = false

    Publishers::CampaignActivityBulkJobStatusPublisher.call(bulk_job: @bulk_job, status: STATUS_IN_PROGRESS.upcase) if @bulk_job.category == CAMPAIGN_ACTION
    bulk_job_records = 
      if @is_retrying
        Record.where(bulk_job_id: @bulk_job.id).where(status: [STATUS_SUCCEEDED, STATUS_FAILED])
      else
        Record.where(bulk_job_id: @bulk_job.id, status: STATUS_QUEUED)
      end

    bulk_job_records.find_in_batches do |records|
      return if @bulk_job.aborted?

      return { paused: true } if @bulk_job.paused?

      @retry_config = @bulk_job.payload['retryConfig'] || {}

      records.each do |record|
        break if @bulk_job.reload.aborting?

        return { paused: true } if @bulk_job.paused?

        failed_phone_numbers = []

        phone_number_wise_entity_details =
          if @is_retrying
            record.phone_number_wise_record_details.where(message_status: SENDING, status: STATUS_RETRYING)
          else
            record.phone_number_wise_record_details.where(status: STATUS_QUEUED)
          end
        
        next if @is_retrying && phone_number_wise_entity_details&.blank?
        
        phone_number_wise_entity_details.each do |phone_number_wise_entity_detail|
          begin
            skip_sleep = false

            if @bulk_job.category == CAMPAIGN_ACTION
              phone_number_object = phone_number_wise_entity_detail.payload['phoneNumbers'].find { |phone| "#{phone['dialCode']}#{phone['value']}" == phone_number_wise_entity_detail.phone_number }

              campaign_activity_status_event_data = {
                campaignId: @bulk_job.payload.dig('campaign', 'id'),
                activityId: @bulk_job.payload.dig('activity', 'id'),
                userId: @bulk_job.user_id,
                tenantId: @bulk_job.tenant_id,
                entity: @bulk_job.entity&.upcase,
                entityId: record.entity_id,
                phoneNumber: phone_number_object
              }
            end

            response = execute_api_call(phone_number_wise_entity_detail)

            response_body = JSON.parse(response.body) rescue {}

            Rails.logger.info "Send Message API response, response body: #{response_body}, message id: #{response_body['id']}"

            if(response.nil? || response.code != 200)
              phone_number_wise_entity_detail.update(status: STATUS_FAILED, error_message: get_error_message(response), message_status: FAILED)
              failed_phone_numbers += [phone_number_wise_entity_detail.phone_number]

              if @bulk_job.category == CAMPAIGN_ACTION
                campaign_activity_status_event_data[:error] = get_error_message(response)
                campaign_activity_status_event_data[:status] = ERROR.upcase
                Publishers::CampaignEntityActivityStatusPublisher.call(campaign_activity_status_event_data)
              end
            else
              phone_number_wise_entity_detail.update(status: STATUS_SUCCEEDED, message_id: response_body['id'], message_status: SENDING)

              if @bulk_job.category == CAMPAIGN_ACTION
                campaign_activity_status_event_data[:status] = SUCCESS.upcase
                Publishers::CampaignEntityActivityStatusPublisher.call(campaign_activity_status_event_data)
              end
            end
          rescue StandardError => e
            resp = JSON.parse(e.http_body) rescue nil
            message = resp.present? ? get_error_message(resp.with_indifferent_access).presence || e.message : e.message

            if pause_job?(resp.try(:[], 'errorCode'))
              case resp.try(:[], 'errorCode')
              when '022003'
                @paused_on_template_entity_and_preview_entity_mismatch = true
              when '022028'
                @paused_on_insufficient_credits = true
              when '022029'
                @paused_on_inactive_whatsapp_template = true
              end

              @message = message
              break
            end
            
            Rails.logger.info "Send Message API response, response body: #{resp}"
            phone_number_wise_entity_detail.update(status: STATUS_FAILED, error_message: message, message_status: STATUS_FAILED)

            if @bulk_job.category == CAMPAIGN_ACTION
              campaign_activity_status_event_data[:error] = message
              campaign_activity_status_event_data[:status] = ERROR.upcase
              Publishers::CampaignEntityActivityStatusPublisher.call(campaign_activity_status_event_data)
            end

            failed_phone_numbers += [phone_number_wise_entity_detail.phone_number]
            skip_sleep = skip_sleep_message?(message)
            if abort_job?(resp.try(:[], 'errorCode'))
              @abort_on_error = true
              @message = message
              break
            end
          end

          sleep(rand(0.5..1)) unless skip_sleep
        end

        break if @abort_on_error || @paused_on_insufficient_credits || @paused_on_inactive_whatsapp_template || @paused_on_template_entity_and_preview_entity_mismatch

        if failed_phone_numbers.present?
          record.update(status: STATUS_FAILED, error_message: "Message is not delivered successfully on following phone numbers: #{failed_phone_numbers.join(', ')}")
        else
          record.update(status: STATUS_SUCCEEDED)
        end
      end
    end

    if @abort_on_error
      bulk_job_records = Record.where(bulk_job_id: @bulk_job.id, status: 'queued')
      bulk_job_record_ids = bulk_job_records.pluck(:id)
      bulk_job_records.update_all(status: STATUS_FAILED, error_message: @message)
      PhoneNumberWiseRecordDetail.where(record_id: bulk_job_record_ids).update_all(status: STATUS_FAILED, error_message: @message)
    end

    if @bulk_job.entity == DEAL
      Record.where(bulk_job_id: @bulk_job.id).where(entity_id: 0).update_all(status: STATUS_FAILED, error_message: 'No associated contacts are present on the deal record')
      Record.where(bulk_job_id: @bulk_job.id).where.not(entity_id: 0).where.missing(:phone_number_wise_record_details).update_all(status: STATUS_FAILED, error_message: 'No phone numbers are present on the record')
    else
      Record.where(bulk_job_id: @bulk_job.id).where.missing(:phone_number_wise_record_details).update_all(status: STATUS_FAILED, error_message: 'No phone numbers are present on the record')
    end

    if @paused_on_insufficient_credits || @paused_on_inactive_whatsapp_template || @paused_on_template_entity_and_preview_entity_mismatch
      @bulk_job.update(status: STATUS_PAUSED, sidekiq_job_id: nil)
      pause_reason =
        if @paused_on_template_entity_and_preview_entity_mismatch
          TEMPLATE_ENTITY_MISMATCH_OR_VARIABLSES_NOT_MAPPED_REASON
        elsif @paused_on_insufficient_credits
          INSUFFICIENT_WHATSAPP_CREDITS_REASON
        else
          INACTIVE_WHATSAPP_TEMPLATE_REASON
        end

      @bulk_job.payload['errorMessage'] = @message
      @bulk_job.save

      Publishers::CampaignActivityBulkJobStatusPublisher.call(bulk_job: @bulk_job, status: STATUS_PAUSED.upcase, status_reason: @message) if @bulk_job.category == CAMPAIGN_ACTION

      unless @bulk_job.is_paused_email_sent
        Publishers::SendBulkWhatsappJobPausedEmailPublisher.call(@bulk_job, pause_reason)
        @bulk_job.update(is_paused_email_sent: true)
      end

      return { paused: true }
    end

    @bulk_job.payload['errorMessage'] = nil
    @bulk_job.save

    if @bulk_job.payload['retryConfig'].present? && !@bulk_job.aborting? && @bulk_job.category == CAMPAIGN_ACTION
      if @is_retrying
        @bulk_job.payload['retryConfig']['timesRetried'] = @bulk_job.payload.dig('retryConfig', 'timesRetried').to_i + 1
        @bulk_job.save!
      end
      
      if @bulk_job.payload.dig('retryConfig', 'timesRetried').to_i < @bulk_job.payload.dig('retryConfig', 'noOfTimes').to_i
        sleep(10)
        records_with_sending_status = PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id).pluck(:id), message_status: SENDING)

        if records_with_sending_status.exists?
          Rails.logger.info "Whatsapp Bulk Job | Retrying as some messages are still in sending state, bulk job id: #{@bulk_job.id}, tenant id: #{@bulk_job.tenant_id}, retrying message ids: #{records_with_sending_status.pluck(:message_id)}"
          retryable_message_count = records_with_sending_status.update_all(status: STATUS_RETRYING)
          @bulk_job.payload['retryConfig']['nextRetryAt'] = 24.hours.from_now.to_s
          @bulk_job.payload['retryConfig']['retryableMessages'] = retryable_message_count.to_i
          @bulk_job.status = STATUS_RETRYING
          @bulk_job.save!
          
          Publishers::CampaignActivityBulkJobStatusPublisher.call(bulk_job: @bulk_job, status: STATUS_RETRYING.upcase) if @bulk_job.category == CAMPAIGN_ACTION

          return { retrying: true }
        end
      else
        @bulk_job.payload['retryConfig']['retryableMessages'] = 0
        @bulk_job.save!
      end
    end

    unless %w[aborted aborting paused].include?(@bulk_job.reload.status)
      @bulk_job.completed!
      Publishers::CampaignActivityBulkJobStatusPublisher.call(bulk_job: @bulk_job, status: STATUS_COMPLETED.upcase) if @bulk_job.category == CAMPAIGN_ACTION
    end

    if @bulk_job.aborting?
      if @bulk_job.category == CAMPAIGN_ACTION
        sleep(10)
        retryable_records = PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id).pluck(:id), message_status: SENDING)
        @records_message_ids = retryable_records.pluck(:message_id)&.compact
        @records_ids = retryable_records.pluck(:record_id)&.compact

        if @records_ids.present?
          Rails.logger.info "Updating retryable record statuses to failed for bulk job id: #{@bulk_job.id}, tenant id: #{@bulk_job.tenant_id}"
          Record.where(id: @records_ids, bulk_job_id: @bulk_job.id, tenant_id: @bulk_job.tenant_id).update_all(status: Record.statuses["failed"])

          retryable_records.update_all(error_message: 'Retry aborted by user', status: STATUS_FAILED, message_status: FAILED)
        end

        # We only have to publish bulk job abort event when user manually abort the job via campaign
        if @records_message_ids.present?
          Rails.logger.info "Publishing WhatsappCampaignActivityAborted event, bulk job id: #{@bulk_job.id}, tenant id: #{@bulk_job.tenant_id}"
          Publishers::WhatsappCampaignActivityAbortedPublisher.call(@bulk_job, @records_message_ids)
        end

        if @bulk_job.payload['retryConfig'].present?
          @bulk_job.payload['retryConfig']['retryableMessages'] = 0
          @bulk_job.save!
        end
      end

      @bulk_job.aborted!
    end

    post_process
  end

  private

  def dynamic_media_present?
    @bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaId').present?
  end

  def process_dynamic_media
    WhatsappTemplateMediaService.new(@bulk_job).download_and_upload
  end

  def execute_api_call(phone_number_wise_entity_detail)
    if @is_retrying
      RestClient.put(
        action_url(phone_number_wise_entity_detail),
        record_specific_payload(phone_number_wise_entity_detail),
        {
          Authorization: "Bearer #{@token}"
        }
      )
    else
      RestClient.post(
        action_url(phone_number_wise_entity_detail),
        record_specific_payload(phone_number_wise_entity_detail),
        {
          Authorization: "Bearer #{@token}"
        }
      )
    end
  end

  def action_url(phone_number_wise_entity_detail)
    if @is_retrying
      "#{SERVICE_MESSAGE}/v1/messages/whatsapp/#{phone_number_wise_entity_detail.message_id}/retry"
    else
      "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/#{@bulk_job.payload.dig('whatsappTemplate', 'id')}/send-bulk-message"
    end
  end

  def record_specific_payload(phone_number_wise_entity_detail)
    if @bulk_job.entity == DEAL
      # For deals, we need to find the phone number from the contact data
      # but the payload contains deal data, so we need to construct the phone number details
      record_specific_payload = {
        entityType: CONTACT, # contact
        entityId: phone_number_wise_entity_detail.record.entity_id, # contact id
        phoneNumber: phone_number_wise_entity_detail.phone_number_details,
        entityData: phone_number_wise_entity_detail.payload, # Deal data
        bulkJob: true,
        dynamicTemplateMediaId: @bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaId'),
        templateEntityId: phone_number_wise_entity_detail.record.parent_entity_id,
        templateEntity: @bulk_job.entity # Deal
      }
    else
      phone_number_details = phone_number_wise_entity_detail.payload['phoneNumbers'].find { |phone_number| "#{phone_number['dialCode']}#{phone_number['value']}" == phone_number_wise_entity_detail.phone_number }

      record_specific_payload = {
        entityType: @bulk_job.entity,
        entityId: phone_number_wise_entity_detail.record.entity_id,
        phoneNumber: phone_number_details,
        entityData: phone_number_wise_entity_detail.payload,
        bulkJob: true,
        dynamicTemplateMediaId: @bulk_job.payload.dig('whatsappTemplate', 'dynamicTemplateMediaId')
      }
    end

    if @bulk_job.category == CAMPAIGN_ACTION
      record_specific_payload[:campaignId] = @bulk_job.payload.dig('campaign', 'id')
      record_specific_payload[:activityId] = @bulk_job.payload.dig('activity', 'id')

      if @retry_config['noOfTimes'].present?
        record_specific_payload[:retryConfig] = {
          noOfTimes: @retry_config['noOfTimes'],
          timesRetried: @retry_config['timesRetried'].to_i 
        }
      end

      if @is_retrying
        record_specific_payload[:retryConfig][:timesRetried] = @retry_config['timesRetried'].to_i + 1
        record_specific_payload[:whatsappTemplateId] = @bulk_job.payload.dig('whatsappTemplate', 'id')
      end
    end

    record_specific_payload
  end

  def get_error_message(response)
    error_message =
      if response['errorCode'].present?
        ERROR_MAPPINGS[response['errorCode']]
      end

    error_message = I18n.t('something_went_wrong') unless error_message.present?
    error_message
  end

  def abort_job?(error_code)
    %w[022001 022002 022006].include?(error_code)
  end

  def pause_job?(error_code)
    %w[022003 022028 022029].include?(error_code)
  end

  def skip_sleep_message?(message)
    [I18n.t('whatsapp_message.insufficient_whatsapp_credits_for_bulk')].include?(message)
  end
end
