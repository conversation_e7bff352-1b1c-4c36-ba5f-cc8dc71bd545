class EntityAction::Base
  attr_accessor :bulk_job, :token

  def initialize job
    @bulk_job = job
    @token = @bulk_job.user.token
  end

  def perform
    return nil unless @bulk_job

    return nil if %w[aborted completed].include?(@bulk_job.status)

    return { paused: true } if @bulk_job.paused?

    Rails.logger.info "BULK JOB ROUND: #{@bulk_job.id} | Picking batch size: #{batch_size} | current_jobs_count: #{current_jobs_count}"
    @bulk_job.update({ status: BulkJob.statuses[:in_progress], started_at: Time.now }) unless @bulk_job.aborting?

    Record.where(bulk_job_id: @bulk_job.id, status: 'queued').find_in_batches(batch_size: batch_size) do |records|
      return if @bulk_job.reload.aborted?

      return { paused: true } if @bulk_job.paused?

      total_records = records.count
      current_record = 1
      last_record = nil
      records.each do |record|
        case @bulk_job.reload.status
        when 'aborting'
          last_record = record
          break

        when 'paused'
          return { paused: true }

        else
          if current_record == total_records
            last_record = record
            next
          end
          perform_action(record)
          current_record += 1
        end
      end

      if last_record
        perform_action(last_record, true)
        unless @bulk_job.aborting?
          Rails.logger.info "BULK JOB ROUND: #{@bulk_job.id} | current_jobs_count: #{current_jobs_count}"
          if current_jobs_count > 0
            BulkActionJob.set(queue: @bulk_job.sidekiq_queue_name).perform_later @bulk_job.id
            return { round_robin_aborted: true }
          end
        end
      end

      @bulk_job.update(successful: @bulk_job.records.succeeded.count , failure: @bulk_job.records.failed.count)
    end

    @bulk_job.completed! unless %w[aborted aborting paused].include?(@bulk_job.reload.status)

    @bulk_job.update(status: STATUS_ABORTED, completed_at: Time.now) if @bulk_job.aborting?

    post_process
  end

  def current_jobs_count
    Sidekiq::Queue.new(@bulk_job.sidekiq_queue_name).count
  end

  def batch_size
    return 100 if current_jobs_count < 10
    return 50 if current_jobs_count < 30
    return 20
  end

  private

  def perform_action(record, publish_usage = false)
    begin
      resp = execute_api_call(record, publish_usage)
      if(resp.nil? || resp.code != 200)
        record.update({ status: Record.statuses[:failed], error_message: get_error_message(resp) })
      else
        record.succeeded!
      end
    rescue StandardError => e
      resp = JSON.parse(e.http_body) rescue nil
      message = resp.present? ? get_error_message(resp.with_indifferent_access) : e.message
      record.update({ status: Record.statuses[:failed], error_message: message })
    end
  end

  def post_process
    success_count = Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["succeeded"]).count
    failure_count = Record.where(bulk_job_id: @bulk_job.id, status: Record.statuses["failed"]).count
    @bulk_job.update(successful: success_count, failure: failure_count)
    CsvGenerator.new(@bulk_job).call
  end

  def get_error_message resp
    resp['errorDetails'].blank? ? resp['message'] : "#{resp['errorDetails'].first['field']}: #{resp['errorDetails'].first['message']}"
  end
end
