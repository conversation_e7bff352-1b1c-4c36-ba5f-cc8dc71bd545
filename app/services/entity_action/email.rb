class EntityAction::Email < EntityAction::Base

  attr_reader :attachments

  ERROR_MAPPING = {
    '********' => I18n.t('authorization_error'),
    '********' => I18n.t('cannot_read_email_template'),
    '********' => I18n.t('not_found'),
    '********' => I18n.t('third_party_authorization_error'),
    '********' => I18n.t('connected_account_not_found'),
    '********' => I18n.t('invalid_provider'),
    '********' => I18n.t('outlook_authorization_error'),
    '********' => I18n.t('inactive_email_template'),
    '********' => I18n.t('rate_limit_error'),
    '********' => I18n.t('smtp_unauthorization'),
    '********' => I18n.t('unsupported_media_type'),
    '********' => I18n.t('http_method_not_implemented'),
    '********' => I18n.t('bad_gateway'),
    '********' => I18n.t('service_unavailable'),
    '********' => I18n.t('gateway_timeout')
  }.with_indifferent_access

  def perform
    return nil unless @bulk_job

    return nil if %w[aborted completed].include?(@bulk_job.status)

    return { paused: true } if @bulk_job.paused?

    download_attachments if @bulk_job.payload['attachments'].present?
    @bulk_job.update({ status: BulkJob.statuses[:in_progress], started_at: Time.now })

    Publishers::CampaignActivityBulkJobStatusPublisher.call(bulk_job: @bulk_job, status: STATUS_IN_PROGRESS.upcase) if @bulk_job.category == CAMPAIGN_ACTION

    skip_sleep = false
    Record.where(bulk_job_id: @bulk_job.id, status: 'queued').find_in_batches do |records|
      return if @bulk_job.reload.aborted?

      return { paused: true } if @bulk_job.paused?

      records.each do |record|
        break if @bulk_job.reload.aborting?

        return { paused: true } if @bulk_job.paused?

        begin
          skip_sleep = false

          if @bulk_job.category == CAMPAIGN_ACTION
            @campaign_activity_status_event_data = {
              campaignId: @bulk_job.payload.dig('campaign', 'id'),
              activityId: @bulk_job.payload.dig('activity', 'id'),
              userId: @bulk_job.user_id,
              tenantId: @bulk_job.tenant_id,
              entity: @bulk_job.entity&.upcase,
              entityId: record.entity_id
            }
          end

          resp = execute_api_call record

          if(resp.nil? || resp.code != 200)
            record.update({ status: Record.statuses[:failed], error_message: get_error_message(resp) })
            if @bulk_job.category == CAMPAIGN_ACTION
              @campaign_activity_status_event_data[:status] = ERROR.upcase
              @campaign_activity_status_event_data[:error] = get_error_message(resp)
            end
          else
            record.succeeded!
            if @bulk_job.category == CAMPAIGN_ACTION
              @campaign_activity_status_event_data[:status] = SUCCESS.upcase
              @campaign_activity_status_event_data[:sentAt] = Time.now.utc.strftime('%FT%T.%LZ')
            end
          end

          Publishers::CampaignEntityActivityStatusPublisher.call(@campaign_activity_status_event_data) if @bulk_job.category == CAMPAIGN_ACTION
        rescue StandardError => e
          resp = JSON.parse(e.http_body) rescue nil

          message = resp.present? ? get_error_message(resp.with_indifferent_access).presence || e.message : e.message
          if pause_job?(resp.try(:[], 'errorCode'))
            @paused_on_error = true
            @message = message
            break
          end

          record.update({ status: Record.statuses[:failed], error_message: message })
          if @bulk_job.category == CAMPAIGN_ACTION
            @campaign_activity_status_event_data[:error] = message
            @campaign_activity_status_event_data[:status] = ERROR.upcase
            Publishers::CampaignEntityActivityStatusPublisher.call(@campaign_activity_status_event_data)
          end

          skip_sleep = skip_sleep_message?(message)
          if abort_job?(resp.try(:[], 'errorCode'))
            @abort_on_error = true
            @message = message
            break
          end
        end
        sleep(rand(1..3)) unless skip_sleep
      end

      break if @abort_on_error || @paused_on_error
    end

    if @paused_on_error
      @bulk_job.update(status: BulkJob.statuses[:paused], sidekiq_job_id: nil)
      @bulk_job.payload['errorMessage'] = @message
      @bulk_job.save

      Publishers::CampaignActivityBulkJobStatusPublisher.call(bulk_job: @bulk_job, status: STATUS_PAUSED.upcase, status_reason: @message) if @bulk_job.category == CAMPAIGN_ACTION

      # TODO: Send email bulk job paused email to user
      return { paused: true }
    end

    Record.where(bulk_job_id: @bulk_job.id, status: 'queued').update_all({ status: Record.statuses[:failed], error_message: @message }) if @abort_on_error

    unless %w[aborted aborting paused].include?(@bulk_job.reload.status)
      @bulk_job.completed!
      Publishers::CampaignActivityBulkJobStatusPublisher.call(bulk_job: @bulk_job, status: STATUS_COMPLETED.upcase) if @bulk_job.category == CAMPAIGN_ACTION
    end

    @bulk_job.aborted! if @bulk_job.aborting?

    post_process
  end

  def execute_api_call(record)
    RestClient.post(action_url,
      record_specific_payload(record).merge(multipart: true),
      {
        Authorization: "Bearer #{@token}"
      }
    )
  end

  private

  def download_attachments
    @attachments =
      @bulk_job.payload['attachments'].map do |file_data|
        url = GetPresignedS3Url.remote_url("#{@bulk_job.remote_file_location}/#{file_data['fileName']}", file_data['fileName'])
        file_content = URI.open(url).read
        file_path = "/tmp/#{@bulk_job.id}-#{file_data['fileName']}"
        begin
          # default encoding here is utf 8 failing for some file types
          File.write(file_path, file_content)
        rescue StandardError
          File.write(file_path, file_content, encoding: 'ASCII-8BIT')
        end
        { id: nil, data: file_path, fileName: file_data['fileName'] }
      end
  end

  def delete_attachments
    attachments.each do |attachment|
      File.delete(attachment[:data]) if File.exists?(attachment[:data])
    end
  end

  def action_url
    "#{SERVICE_EMAIL}/v1/emails/send-email"
  end

  def record_specific_payload(record)
    validate_and_modify_payload(entity_details(record), @bulk_job.payload, record)
  end

  def entity_details(record)
    raise I18n.t('no_contact_associated') if record.entity_id.zero?
    JSON.parse(RestClient.get("#{entity_specific_url}/#{record.entity_id}", { Authorization: "Bearer #{@token}" }))
  end

  def entity_specific_url
    case @bulk_job.entity
    when LEAD
      "#{SERVICE_SALES}/v1/leads"
    when CONTACT, DEAL
      "#{SERVICE_SALES}/v1/contacts"
    end
  end

  def validate_and_modify_payload(entity_details, payload, record)
    payload = payload.with_indifferent_access

    unless entity_details.dig('recordActions', 'email')
      raise I18n.t('no_email_permission')
    end

    primary_email = entity_details['emails']&.find { |email| email['primary'] }.try(:[], 'value')
    if primary_email.blank?
      raise I18n.t('email_not_found')
    end

    @campaign_activity_status_event_data[:email] = entity_details['emails']&.find { |email| email['primary'] } if @campaign_activity_status_event_data.present?
    
    if @bulk_job.entity == DEAL
      payload[:relatedTo] = {
        entity: @bulk_job.entity,
        id: record.parent_entity_id,
      }
      payload[:to] = [{
        entity: CONTACT,
        id: record.entity_id,
        name: "#{entity_details['firstName']} #{entity_details['lastName']}".strip,
        email: primary_email
      }]
    else
      payload[:relatedTo] = {
        entity: @bulk_job.entity,
        id: record.entity_id,
        name: "#{entity_details['firstName']} #{entity_details['lastName']}".strip,
        email: primary_email
      }
      payload[:to] = [payload[:relatedTo]]
    end
    payload[:attachments] = attachments.map { |attachment| attachment.merge(data: File.open(attachment[:data])) } if attachments.present?

    if payload[:emailTemplate].present?
      payload[:emailTemplateId] = payload.dig(:emailTemplate, :id)
      payload[:emailTemplateName] = payload.dig(:emailTemplate, :name)
      payload.delete(:emailTemplate)
    end
    
    payload
  end

  def get_error_message(resp)
    resp['errorCode'].present? ? ERROR_MAPPING[resp['errorCode']] : I18n.t('something_went_wrong')
  end

  def skip_sleep_message?(message)
    [I18n.t('no_email_permission'), I18n.t('email_not_found'), I18n.t('no_contact_associated')].include?(message)
  end

  def abort_job?(error_code)
    %w[******** ******** ******** ******** ********].include?(error_code)
  end

  def pause_job?(error_code)
    %w[******** ******** ******** ******** ******** ******** ******** ******** ********].include?(error_code)
  end

  def post_process
    super
    delete_attachments if @attachments.present?
  end
end
