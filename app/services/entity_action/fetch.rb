module EntityAction
  class Fetch

    MAX_PAGE_SIZE = 1000
    def initialize id
      begin
        @bulk_job = BulkJob.find id
      rescue ArgumentError, ActiveRecord::RecordInvalid, ActiveRecord::RecordNotFound => e
        Rails.logger.error "Error while Fetching records for Bulk Job (#{id}): #{e.message}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{e.message}")
      end
    end

    def call
      entity_ids_with_details = get_entity_ids
      save_records(entity_ids_with_details)
      @bulk_job.update(number_of_records: @bulk_job.reload.records.count)
    end

    private

    def build_url(skip_share_rule_evaluation: false)
      case @bulk_job.entity
      when LEAD
        return SERVICE_SEARCH + (skip_share_rule_evaluation ? "/v2/search/lead" : "/v1/search/lead")
      when DEAL
        return SERVICE_SEARCH + "/v1/search/deal"
      when CONTACT
        return SERVICE_SEARCH + (skip_share_rule_evaluation ? "/v2/search/contact" : "/v1/search/contact")
      when TAS<PERSON>
        return SERVICE_PRODUCTIVITY + "/v1/tasks/search"
      when CALL_LOG
        return "#{SERVICE_CALL}/v1/call-logs/search"
      when USER
        return "#{SERVICE_IAM}/v1/users/search"
      when EMAIL
        return "#{SERVICE_EMAIL}/v2/email-threads/search?view=bulk"
      when COMPANY
        return "#{SERVICE_SEARCH}/v1/search/company"
      when MEETING
        return "#{SERVICE_MEETING}/v1/meetings/search"
      else
        Rails.logger.error "Bulk Job fetch not added for entity #{@bulk_job.entity} | #{@bulk_job.id}"

        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}")
      end
    end

    def get_payload
      if @bulk_job.entity == DEAL && @bulk_job.operation == EMAIL_OPERATION
        {
          fields: %w[id associatedContacts],
          jsonRule: @bulk_job.filters['jsonRule']
        }
      elsif @bulk_job.operation == WHATSAPP_MESSAGE_OPERATION || (@bulk_job.operation == EMAIL_OPERATION && @bulk_job.category == CAMPAIGN_ACTION)
        {
          fields: [],
          jsonRule: @bulk_job.filters['jsonRule']
        }
      else
        {
          fields: %w[id],
          jsonRule: @bulk_job.filters['jsonRule']
        }
      end
    end

    def get_entity_ids(skip_share_rule_evaluation: false)
      parent_child_entity_ids = []
      entities_details = {}
      request_payload = get_payload
      size = (@bulk_job.filters['size'] || MAX_PAGE_SIZE).to_i
      pages = 1

      if @bulk_job.category == CAMPAIGN_ACTION && [WHATSAPP_MESSAGE_OPERATION, EMAIL_OPERATION].include?(@bulk_job.operation)
        url = build_url(skip_share_rule_evaluation: skip_share_rule_evaluation)
        query_params = "page=0&size=1"
        url = url.include?('?') ? "#{url}&#{query_params}" : "#{url}?#{query_params}"
        user_token =
          if skip_share_rule_evaluation
            GenerateToken.new(@bulk_job.user.id, @bulk_job.user.tenant_id, admin_permissions, true).call
          else
            @bulk_job.user.token
          end

        begin
          response = RestClient.post(
            url,
            request_payload.to_json,
            {
              :Authorization => "Bearer #{user_token}",
              content_type: :json,
              accept: :json
            }
          )
          if response.nil?
            Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} invalid response"

            raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
          end

          size = JSON(response.body)['totalElements'] || 0
          @bulk_job.filters['size'] = size
          @bulk_job.save
        rescue RestClient::NotFound
          Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} 404"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        rescue RestClient::InternalServerError
          Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} 500"
          raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        rescue RestClient::BadRequest
          Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} 400"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        rescue StandardError => e
          Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} #{e.message}"

          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        end
      end

      if size > MAX_PAGE_SIZE
        pages = size / MAX_PAGE_SIZE
        pages = pages + 1 if(size % MAX_PAGE_SIZE > 0)
        size = MAX_PAGE_SIZE
      end

      page_offset = ENTITIES_PAGE_OFFSET_ONE.include?(@bulk_job.entity) ? 1 : 0
      pages.times do |page|
        url =  build_url(skip_share_rule_evaluation: skip_share_rule_evaluation)
        query_params = "page=#{page + page_offset}&size=#{size}"
        
        if @bulk_job.filters['sort'].present?
          query_params = query_params + "&sort=#{@bulk_job.filters['sort']}" 
        elsif [EMAIL, MEETING].exclude?(@bulk_job.entity)
          query_params = query_params + '&sort=id,asc'
        end

        url = url.include?('?') ? "#{url}&#{query_params}" : "#{url}?#{query_params}"
        user_token =
          if skip_share_rule_evaluation
            GenerateToken.new(@bulk_job.user.id, @bulk_job.user.tenant_id, admin_permissions, true).call
          else
            @bulk_job.user.token
          end

        begin
          response = RestClient.post(
            url,
            request_payload.to_json,
            {
              :Authorization => "Bearer #{user_token}",
              content_type: :json,
              accept: :json
            }
          )
          if response.nil?
            Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} invalid response"

            raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
          end

          parent_child_entity_ids += get_parent_child_entity_ids(JSON(response.body)['content'])

          entities_details.merge!(prepare_entity_details_payload(JSON(response.body))) if @bulk_job.operation == WHATSAPP_MESSAGE_OPERATION || (@bulk_job.operation == EMAIL_OPERATION && @bulk_job.category == CAMPAIGN_ACTION)
        rescue RestClient::NotFound
          Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} 404"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        rescue RestClient::InternalServerError
          Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} 500"
          raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        rescue RestClient::BadRequest
          Rails.logger.error "Entity::Fetch -> get_entity_ids -> Entity: #{@bulk_job.entity} 400"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        end
      end

      return [parent_child_entity_ids, entities_details]
    end

    def get_parent_child_entity_ids(content_array)
      if @bulk_job.entity == DEAL && [WHATSAPP_MESSAGE_OPERATION, EMAIL_OPERATION].include?(@bulk_job.operation)
        content_array.inject([]) do |arr, deal|
          arr + (deal['associatedContacts'].to_a.map { |contact| [deal['id'], contact['id']] }.presence || [[deal['id'], 0]])
        end
      else
        content_array.collect { |entity| [nil, entity['id']] }
      end
    end

    def prepare_entity_details_payload(response_body)
      entity_wise_details = {}

      response_body['content'].each do |entity_data|
        entity_data['metaData'] = response_body['metaData']
        entity_wise_details["#{entity_data['id']}"] = entity_data
      end

      entity_wise_details
    end

    def save_records entity_ids_with_details
      ids = entity_ids_with_details.first
      entities_details = entity_ids_with_details.last
      return if ids.blank?

      current_time = DateTime.now.utc

      existing_ids = Record.where(
        tenant_id: @bulk_job.tenant_id,
        bulk_job_id: @bulk_job.id,
        parent_entity_id: ids.map(&:first),
        entity_id: ids.map(&:last)
      ).pluck(:parent_entity_id, :entity_id)

      records = (ids - existing_ids).map do |entity_ids|
          {
            tenant_id: @bulk_job.tenant_id,
            bulk_job_id: @bulk_job.id,
            parent_entity_id:entity_ids.first,
            entity_id: entity_ids.last,
            created_at: current_time,
            updated_at: current_time
          }
        end

      if records.any?
        Record.insert_all(records)

        if @bulk_job.operation == WHATSAPP_MESSAGE_OPERATION
          handle_whatsapp_campaign_activity(records, entities_details)
        elsif @bulk_job.operation == EMAIL_OPERATION && @bulk_job.category == CAMPAIGN_ACTION
          handle_email_campaign_activity(records, entities_details)
        end
      end
    end

    def handle_whatsapp_campaign_activity(records, entities_details)
      record_entity_ids = records.map { |record| record[:entity_id] }
      current_time = Time.current

      masking_enabled = false
      entity_for_masking = @bulk_job.entity == DEAL ? CONTACT : @bulk_job.entity
      masked_fields = GetMaskedFields.new(entity_for_masking, @bulk_job.user.token).call
      masking_enabled = masked_fields.find { |field| field['name'] == 'phoneNumbers' }.present?

      if masking_enabled && @bulk_job.entity != DEAL
        get_entity_ids_with_details = get_entity_ids(skip_share_rule_evaluation: true)
        entities_details = get_entity_ids_with_details.last
      end

      record_details_with_phone_numbers = []

      if @bulk_job.entity == DEAL
        # For deals, we need to fetch contact details and use contact phone numbers
        process_deal_phone_numbers(record_entity_ids, entities_details, masking_enabled, current_time)
      else
        Record.where(entity_id: record_entity_ids, bulk_job_id: @bulk_job.id, tenant_id: @bulk_job.tenant_id, status: STATUS_QUEUED).find_in_batches do |records|
          phone_number_with_record_details = []

          records.each do |record|
            entity_data = entities_details["#{record.entity_id}"]
            phone_numbers_to_add =
              if @bulk_job.payload['messageSendTo']&.first&.dig('type') == 'PRIMARY_PHONE_NUMBER'
                entity_data['phoneNumbers']&.select { |phone_number| phone_number['primary'] }
              else
                entity_data['phoneNumbers']
              end

            if phone_numbers_to_add.blank?
              record_details_with_phone_numbers << {
                entityId: record.entity_id,
                entityName: "#{entity_data['firstName']} #{entity_data['lastName']}",
                phoneNumber: nil,
                error: 'No phone numbers found'
              }

              next
            end

            phone_number_with_record_details +=
              phone_numbers_to_add.map do |phone|
                phone_number_for_log = masking_enabled ? "#{phone['dialCode']}#{'*' * 4}#{phone['value'][-3, 3]}" : "#{phone['dialCode']}#{phone['value']}"

                if @bulk_job.category == CAMPAIGN_ACTION
                  record_details_with_phone_numbers << {
                    entityId: record.entity_id,
                    entityName: "#{entity_data['firstName']} #{entity_data['lastName']}",
                    phoneNumber: phone
                  }
                end

                {
                  phone_number: "#{phone['dialCode']}#{phone['value']}",
                  phone_number_for_log: phone_number_for_log,
                  payload: entity_data,
                  phone_number_details: phone,
                  status: STATUS_QUEUED,
                  record_id: record.id,
                  created_at: current_time,
                  updated_at: current_time
                }
              end
          end

          PhoneNumberWiseRecordDetail.insert_all(phone_number_with_record_details) unless phone_number_with_record_details.blank?
        end
      end

      @bulk_job.payload['totalPhoneNumbers'] = Record.where(bulk_job_id: @bulk_job.id).joins(:phone_number_wise_record_details).count
      @bulk_job.save

      if @bulk_job.category == CAMPAIGN_ACTION && record_details_with_phone_numbers.present?
        Publishers::CampaignActivityBulkJobStartedPublisher.call(@bulk_job, record_details_with_phone_numbers)
      end
    end

    def admin_permissions
      [
        {
          id: 1,
          name:  LEAD,
          description: 'has access to lead',
          limits: -1,
          units: 'count',
          action: {
            readAll: true,
            read: true,
            sms: true,
            write: true,
            update: true
          }
        },
        {
          id: 2,
          name: CONTACT,
          description: 'has access to contact',
          limits: -1,
          units: 'count',
          action: {
            readAll: true,
            read: true,
            sms: true,
            write: true,
            update: true
          }
        },
        {
          id: 3,
          name: 'sms',
          description: 'has access to sms',
          limits: -1,
          units: 'count',
          action: {
            read: true,
            sms: true,
            write: true
          }
        },
        {
          id: 4,
          name: USER,
          description: 'has access to user',
          limits: -1,
          units: 'count',
          action: {
            read: true
          }
        }
      ]
    end

    def fetch_contact_details_for_deals(contact_ids, skip_share_rule_evaluation: false)
      return {} if contact_ids.empty?

      contact_details = {}
      user_token = skip_share_rule_evaluation ?
        GenerateToken.new(@bulk_job.user.id, @bulk_job.user.tenant_id, admin_permissions, true).call :
        @bulk_job.user.token

      # Fetch contacts in batches to avoid payload size limits
      contact_ids.each_slice(100) do |batch_contact_ids|
        # Use contact search API with JSON payload instead of query params
        url = skip_share_rule_evaluation ? "#{SERVICE_SEARCH}/v2/search/contact" : "#{SERVICE_SEARCH}/v1/search/contact"

        json_rule = {
          rules: [
            {
              operator: "in",
              id: "id",
              field: "id",
              type: "long",
              value: batch_contact_ids
            }
          ],
          condition: "AND",
          valid: true
        }

        payload = {
          fields: ['id', 'firstName', 'lastName', 'phoneNumbers'],
          jsonRule: json_rule
        }

        begin
          response = RestClient.post(
            url,
            payload.to_json,
            {
              Authorization: "Bearer #{user_token}",
              content_type: :json,
              accept: :json
            }
          )

          if response&.code == 200
            response_data = JSON.parse(response.body)
            response_data['content']&.each do |contact|
              contact_details[contact['id'].to_s] = contact
            end
          end
        rescue RestClient::ExceptionWithResponse => e
          Rails.logger.error "Error fetching contact details for deal WhatsApp: #{e.message}"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        end
      end

      contact_details
    end

    def handle_email_campaign_activity(records, entities_details)
      record_entity_ids = records.map { |record| record[:entity_id] }
      record_details_with_emails = []

      Record.where(entity_id: record_entity_ids, bulk_job_id: @bulk_job.id, tenant_id: @bulk_job.tenant_id, status: STATUS_QUEUED).find_in_batches do |records|
        records.each do |record|
          entity_data = entities_details["#{record.entity_id}"]
          emails_to_add =
            if @bulk_job.payload['sentTo'] == 'PRIMARY_EMAIL'
              entity_data['emails']&.select { |email| email['primary'] }
            end

          if emails_to_add.blank?
            record_details_with_emails << {
              entityId: record.entity_id,
              entityName: "#{entity_data['firstName']} #{entity_data['lastName']}",
              email: nil,
              error: 'No emails found'
            }
          else
            emails_to_add.each do |email|
              record_details_with_emails << {
                entityId: record.entity_id,
                entityName: "#{entity_data['firstName']} #{entity_data['lastName']}",
                email: {
                  type: email['type'],
                  value: email['value'],
                  primary: email['primary']
                }
              }
            end
          end
        end
      end

      if record_details_with_emails.present?
        Publishers::CampaignActivityBulkJobStartedPublisher.call(@bulk_job, record_details_with_emails)
      end
    end

    def process_deal_phone_numbers(record_entity_ids, entities_details, masking_enabled, current_time)
      # Get all contact IDs from deals
      all_contact_ids = []
      Record.where(entity_id: record_entity_ids, bulk_job_id: @bulk_job.id, tenant_id: @bulk_job.tenant_id, status: STATUS_QUEUED).find_in_batches do |records|
        records.each do |record|
          deal_data = entities_details["#{record.parent_entity_id}"]
          next unless deal_data && deal_data['associatedContacts']

          deal_data['associatedContacts'].each do |contact|
            all_contact_ids << contact['id'] if contact['id'] != 0
          end
        end
      end

      # Fetch contact details with phone numbers
      contact_details = fetch_contact_details_for_deals(all_contact_ids.uniq, skip_share_rule_evaluation: masking_enabled)

      # Process each record (which represents a deal-contact relationship)
      Record.where(entity_id: record_entity_ids, bulk_job_id: @bulk_job.id, tenant_id: @bulk_job.tenant_id, status: STATUS_QUEUED).find_in_batches do |records|
        phone_number_with_record_details = []

        records.each do |record|
          # For deals, record.parent_entity_id is the deal ID and record.entity_id is the contact ID
          deal_data = entities_details["#{record.parent_entity_id}"]
          contact_data = contact_details["#{record.entity_id}"]

          # Skip if no contact found or contact ID is 0 (deal with no contacts)
          next if record.entity_id == 0 || contact_data.blank?

          phone_numbers_to_add =
            if @bulk_job.payload['messageSendTo']&.first&.dig('type') == 'PRIMARY_PHONE_NUMBER'
              contact_data['phoneNumbers']&.select { |phone_number| phone_number['primary'] }
            else
              contact_data['phoneNumbers']
            end

          next if phone_numbers_to_add.blank?

          phone_number_with_record_details +=
            phone_numbers_to_add.map do |phone|
              phone_number_for_log = masking_enabled ? "#{phone['dialCode']}#{'*' * 4}#{phone['value'][-3, 3]}" : "#{phone['dialCode']}#{phone['value']}"

              {
                phone_number: "#{phone['dialCode']}#{phone['value']}",
                phone_number_for_log: phone_number_for_log,
                payload: deal_data,
                phone_number_details: phone,
                status: STATUS_QUEUED,
                record_id: record.id,
                created_at: current_time,
                updated_at: current_time
              }
            end
        end

        PhoneNumberWiseRecordDetail.insert_all(phone_number_with_record_details) unless phone_number_with_record_details.blank?
      end
    end
  end
end
