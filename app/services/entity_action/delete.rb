class EntityAction::Delete < EntityAction::Base

  def execute_api_call(record, publish_usage = true)
    sleep rand(0.3)
    RestClient.delete(entity_specific_url(record, publish_usage),
      {
        :authorization => "Bearer #{@token}"
      }
    )
  end

  private

  def entity_specific_url(record, publish_usage)
    case @bulk_job.entity
    when LEAD
      "#{SERVICE_SALES}/v1/leads/#{record.entity_id}?publishUsage=#{publish_usage}"
    when DEAL
      "#{SERVICE_DEAL}/v1/deals/#{record.entity_id}?publishUsage=#{publish_usage}"
    when CONTACT
      "#{SERVICE_SALES}/v1/contacts/#{record.entity_id}?publishUsage=#{publish_usage}"
    when TASK
      "#{SERVICE_PRODUCTIVITY}/v1/tasks/#{record.entity_id}?publishUsage=#{publish_usage}"
    when CALL_LOG
      "#{SERVICE_CALL}/v1/call-logs/#{record.entity_id}?publishUsage=#{publish_usage}"
    when EMAIL
      "#{SERVICE_EMAIL}/v1/email-threads/#{record.entity_id}?publishUsage=#{publish_usage}"
    when COMPANY
      "#{SERVICE_COMPANY}/v1/companies/#{record.entity_id}?publishUsage=#{publish_usage}"
    when MEETING
      "#{SERVICE_MEETING}/v1/meetings/#{record.entity_id}?publishUsage=#{publish_usage}"
    when NOTE
      "#{SERVICE_PRODUCTIVITY}/v1/notes/#{record.entity_id}/deleteNoteWithAllRelations?publishUsage=#{publish_usage}"
    end
  end
end
