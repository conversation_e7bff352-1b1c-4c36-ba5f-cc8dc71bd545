module EntityAction
  class FetchNotes

    MAX_PAGE_SIZE = 1000

    def initialize id
      begin
        @bulk_job = BulkJob.find id
      rescue ArgumentError, ActiveRecord::RecordInvalid, ActiveRecord::RecordNotFound => e
        Rails.logger.error "Error while Fetching records for Bulk Job (#{id}): #{e.message}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{e.message}")
      end
    end

    def call
      save_records(get_notes)
      @bulk_job.update(number_of_records: @bulk_job.reload.records.count)
    end

    private

    def build_url
      "#{SERVICE_PRODUCTIVITY}/v1/notes/search"
    end

    def get_payload
      {
        fields: %w[id relations],
        jsonRule: @bulk_job.filters['jsonRule']
      }
    end

    def get_notes
      notes = []
      total_pages = 1
      current_page = 0
      url =  build_url

      while current_page < total_pages
        query_params = "page=#{current_page}&size=#{MAX_PAGE_SIZE}"
        begin
          response = RestClient.post(
            "#{url}?#{query_params}",
            get_payload.to_json,
            {
              Authorization: "Bearer #{@bulk_job.user.token}",
              content_type: :json,
              accept: :json
            }
          )
          if response.nil?
            Rails.logger.error "Entity::FetchNotes -> get_notes -> invalid response"
            raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
          end
          notes_response = JSON(response.body)
          notes += parse_notes_from_content(notes_response['content'])
          if(total_pages == 1)
            total_pages = notes_response['totalPages']
          end
          current_page += 1
        rescue RestClient::NotFound
          Rails.logger.error "Entity::FetchNotes -> get_notes -> 404"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        rescue RestClient::InternalServerError
          Rails.logger.error "Entity::FetchNotes -> get_notes -> 500"
          raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        rescue RestClient::BadRequest
          Rails.logger.error "Entity::FetchNotes -> get_notes -> 400"
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        end
      end
      notes
    end

    def parse_notes_from_content(notes_content)
      notes_content.map do |note| 
        [note['id'], get_note_relations(note)]
      end
    end

    def get_note_relations note
      note['relations'].inject({}) do |relations, relation|
        relations["#{relation['entityType'].downcase}_id"] = relation['entityId']
        relations
      end
    end

    def save_records(notes)
      return if notes.blank?

      current_time = DateTime.now.utc

      existing_notes = Record.where(
        tenant_id: @bulk_job.tenant_id,
        bulk_job_id: @bulk_job.id,
        entity_id: notes.map(&:first),
      ).pluck(:entity_id)

      records = notes.reject { |note| existing_notes.include?(note.first) }.map do |note|
        {
          tenant_id: @bulk_job.tenant_id,
          bulk_job_id: @bulk_job.id,
          entity_id: note.first,
          relations: note.last,
          created_at: current_time,
          updated_at: current_time
        }
      end

      Record.insert_all(records) if records.any?
    end
  end
end
