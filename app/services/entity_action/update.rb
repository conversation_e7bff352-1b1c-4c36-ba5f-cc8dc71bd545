class EntityAction::Update < EntityAction::Base

  def execute_api_call(record, updateUsage = nil)
    RestClient.patch("#{entity_specific_url}/#{record.entity_id}",
      entity_specific_payload.with_indifferent_access.to_json,
      {
        :content_type => 'application/json',
        :authorization => "Bearer #{@token}"
      }
    )
  end

  private

  def entity_specific_url
    case @bulk_job.entity
    when 'lead'
      "#{SERVICE_SALES}/v1/leads/bulk-patch"
    when 'deal'
      "#{SERVICE_DEAL}/v1/deals/bulk-patch"
    when 'contact'
      "#{SERVICE_SALES}/v1/contacts"
    when 'company'
    "#{SERVICE_COMPANY}/v1/companies/bulk-patch"
    when TASK
      "#{SERVICE_PRODUCTIVITY}/v1/tasks"
    when USER
      "#{SERVICE_IAM}/v1/users"
    end
  end

  def entity_specific_payload
    case @bulk_job.entity
    when 'lead'
      {
        lead: @bulk_job.payload,
        executeWorkflow: @bulk_job.execute_workflow,
        sendNotification: false,
        executeScoreRule: @bulk_job.execute_score_rule
      }
    when 'deal'
      {
        deal: @bulk_job.payload,
        executeWorkflow: @bulk_job.execute_workflow,
        sendNotification: false,
        executeScoreRule: @bulk_job.execute_score_rule
      }
    when 'contact'
      {
        contact: @bulk_job.payload,
        executeWorkflow: @bulk_job.execute_workflow,
        sendNotification: false,
        executeScoreRule: @bulk_job.execute_score_rule
      }
    when 'company'
      {
        company: @bulk_job.payload,
        sendNotification: false
      }
    when TASK
      {
        task: @bulk_job.payload,
        executeWorkflow: @bulk_job.execute_workflow,
        sendNotification: false
      }
    when USER
      {
        user: @bulk_job.payload
      }
    end
  end
end
