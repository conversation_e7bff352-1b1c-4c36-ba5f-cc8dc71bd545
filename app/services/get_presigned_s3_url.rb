class GetPresignedS3Url
  def self.remote_url(remote_path, file_name)
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(S3_BULK_ACTION_BUCKET)
    
    begin
      obj = @bucket.object(remote_path)
      url = obj.presigned_url(:get, expires_in: 300, response_content_disposition: "attachment; filename=#{file_name}")
      url
    rescue StandardError => e
      Rails.logger.error "Bulk Action Service | Failed to get signed URL of file from s3: #{e.message} | #{remote_path}"
    end
  end
end
