class Tenant < ApplicationRecord

  has_many :bulk_jobs

  def self.update_record data
    return if Tenant.find_by(id: data["id"])
    Tenant.create(id: data["id"])
  end

  def self.get_by_id id
    Tenant.find_by(id: id)
  end

  def first_queued_job
    bulk_jobs.queued.order(:created_at).first
   end

  def running_job?
    bulk_jobs.in_progress.exists?
  end

  def running_job
    bulk_jobs.in_progress.first
  end
end
