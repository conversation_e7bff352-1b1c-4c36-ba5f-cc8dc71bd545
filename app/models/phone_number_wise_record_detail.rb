# frozen_string_literal: true

class PhoneNumberWiseRecordDetail < ApplicationRecord
  validates :phone_number, :phone_number_for_log, :status, :payload, presence: :true
  # We are adding phone number value with country code, eg. '+918888992211'

  validates :status, inclusion: { in: [STATUS_QUEUED, STATUS_SUCCEEDED, STATUS_FAILED, STATUS_RETRYING], message: "is invalid" }

  validates :message_status, inclusion: { in: [SENT, DELIVERED, READ, FAILED, SENDING] }, allow_blank: true
  belongs_to :record
end
