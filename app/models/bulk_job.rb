class BulkJob < ApplicationRecord
  validates :user_id, :tenant_id, :entity, :operation, :filters, :status, presence: true
  validates :entity, inclusion: { in: [LEAD, DEAL, CONTACT, TASK, CALL_LOG, USER, EMAIL, COMPANY, MEETING, NOTE], message: "is invalid"}
  validates :operation, inclusion: { in: [UPDATE_OPERATION, DELETE_OPERATION, EMAIL_OPERATION, WHATSAPP_MESSAGE_OPERATION], message: "is invalid"}
  validates :category, inclusion: { in: [BULK_ACTION, CAMPAIGN_ACTION], message: "is invalid" }
  validate :restrict_bulk_email
  validate :restrict_bulk_whatsapp_message

  belongs_to :user
  belongs_to :tenant
  has_many :records
  has_one :error_summary_file, -> { where file_type: SummaryFile::ERROR}, class_name: 'SummaryFile'
  has_one :summary_file, -> { where file_type: SummaryFile::SUCCESS }

  enum status: { queued: 0, in_progress: 1, paused: 2, aborted: 3, completed: 4, aborting: 5, queuing: 6, retrying: 7 }

  def completed!
    self.completed_at = Time.now
    super
  end

  def sidekiq_queue_name
    return QUEUE_SK_QUEUE if self.status == STATUS_QUEUING

    return DELETE_SK_QUEUE if operation.eql? DELETE_OPERATION

    DEFAULT_SK_QUEUE
  end

  def remote_file_location
    "#{self.tenant_id}/#{self.id}"
  end

  def success_file_name
    "#{id}-#{SummaryFile::SUCCESS_FILE_NAME}"
  end

  def error_file_name
    "#{id}-#{SummaryFile::ERROR_FILE_NAME}"
  end

  def get_record_actions
    return nil if [STATUS_ABORTING, STATUS_QUEUING].include?(self.status)

    current_user = Thread.current[:user]
    auth_data = Auth::TokenParser.parse(Thread.current[:token])

    {
      update: (self.id == current_user.id || auth_data.can_access?(self.entity, 'update_all')),
      delete: status_change_allowed?('delete')[:allowed]
    }
  end

  def status_change_allowed?(status)
    valid_status =
      case status
      when 'pause'
        [STATUS_IN_PROGRESS, STATUS_QUEUED].include?(self.status)
      when 'resume'
        [STATUS_PAUSED].include?(self.status)
      when 'abort'
        [STATUS_QUEUED, STATUS_IN_PROGRESS, STATUS_PAUSED, STATUS_RETRYING].include?(self.status)
      when 'delete'
        [STATUS_PAUSED, STATUS_ABORTED, STATUS_QUEUED, STATUS_COMPLETED].include?(self.status)
      end

    return { allowed: false, error: 'invalid_status' } unless valid_status
    
    current_user = Thread.current[:user]
    token = Thread.current[:token]
    auth_data = Auth::TokenParser.parse(token)

    return { allowed: true, error: nil } if self.user_id == current_user&.id

    return { allowed: true, error: nil } if (current_user.tenant_id == self.tenant_id && auth_data.can_access?(self.entity, 'update_all'))

    return { allowed: false, error: 'unauthorized' }
  end

  private

  def restrict_bulk_email
    if operation == EMAIL_OPERATION && [LEAD, CONTACT, DEAL].exclude?(entity)
      errors.add(:base, "cannot perform bulk emails for #{entity.pluralize}")
    end
  end

  def restrict_bulk_whatsapp_message
    if operation == WHATSAPP_MESSAGE_OPERATION && [LEAD, CONTACT, DEAL].exclude?(entity)
      errors.add(:base, "cannot perform bulk whatsapp message for #{entity.pluralize}")
    end
  end
end
