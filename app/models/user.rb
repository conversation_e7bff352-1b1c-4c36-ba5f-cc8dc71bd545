class User < ApplicationRecord
  validates :name, :tenant_id, presence: true

  belongs_to :tenant

  def self.get_by_id id
    User.find_by(id: id)
  end

  def self.update_record data
    user = User.find_or_initialize_by(id: data[:id], tenant_id: data[:tenant_id])
    if user.new_record?
      user.assign_attributes(name: data[:name])
    end
    raise(ExceptionHandler::AuthenticationError, ErrorCode.invalid_token) unless user.save
    user
  end
end
