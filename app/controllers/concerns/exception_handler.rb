module ExceptionHandler
  extend ActiveSupport::Concern
  class AuthenticationError < StandardError; end
  class InternalServerError < StandardError; end
  class InvalidDataError < StandardError; end
  class NotFound < StandardError; end
  class Forbidden < StandardError; end
  class InsufficientWhatsappCreditsForBulkAction < StandardError; end
  
  included do
    # Define custom handlers
    rescue_from ExceptionHandler::AuthenticationError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.unauthorized,
          message: message
        },
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InternalServerError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.internal_error,
          message: message
        },
        :internal_server_error
      )
    end
    rescue_from ExceptionHandler::InvalidDataError do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.invalid_data,
          message: message
        },
        :unprocessable_entity
      )
    end
    rescue_from ExceptionHandler::NotFound do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.not_found,
          message: message
        },
        :not_found
      )
    end
    rescue_from ExceptionHandler::Forbidden do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.unauthorized,
          message: message
        },
        :unauthorized
      )
    end

    rescue_from ExceptionHandler::InsufficientWhatsappCreditsForBulkAction do |e|
      errorCode, message = e.message&.split('||')
      json_response(
        {
          errorCode: errorCode || ErrorCode.unauthorized,
          message: message
        },
        :unprocessable_entity
      )
    end

    def json_response(object, status = :ok)
      render json: object, status: status
    end
  end
end
