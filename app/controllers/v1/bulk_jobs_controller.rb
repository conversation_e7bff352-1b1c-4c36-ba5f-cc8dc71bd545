module V1
  class BulkJobsController < ApplicationController

    def create
      app_id = BulkJobService.create(permit_params.to_h.with_indifferent_access)
      render json: { id: app_id }, status: :created
    end

    def index
      render json: BulkJobService.list(underscorize_params)
    end

    def search
      render json: BulkJobService.list(underscorize_params)
    end

    def summary_file
      render json: BulkJobService.new(params[:id]).summary_file
    end

    def error_file
      render json: BulkJobService.new(params[:id]).error_summary_file
    end

    def attachment
      render json: BulkJobService.new(params[:id]).attachment
    end

    def abort
      BulkJobService.new(params[:id]).abort
      render json: { id: params[:id] }, status: :ok
    end

    def pause
      BulkJobService.new(params[:id]).pause
      render json: { id: params[:id] }, status: :ok
    end

    def resume
      BulkJobService.new(params[:id]).resume
      render json: { id: params[:id] }, status: :ok
    end

    def destroy
      BulkJobService.new(params[:id]).delete
      head :ok
    end

    private

    def permit_params
      case action_name
      when 'create'
        params.permit(:operation, :entity, :executeWorkflow, :executeScoreRule, filters: {}, payload: {})
      when 'search'
        params.permit(:page, :size, :sort, jsonRule: {})
      else
        params.permit!
      end
    end
  end
end
