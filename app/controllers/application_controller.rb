class ApplicationController < ActionController::API
  include ExceptionHandler

  before_action :authenticate, except: [:health]

  def health
    head :ok
  end

  def underscorize_params
    permitted_params = permit_params
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data) unless permitted_params.permitted?
    UnderscorizeKeys.do(permitted_params.to_h).with_indifferent_access
  end

  private
  def authenticate
    headers = request.headers
    begin
      SdAuthenticator.authenticate({authorization_header: headers['Authorization'] })
    rescue AuthExceptionHandler::SdAuthException => e
      Rails.logger.error "Error while authenticating user: #{e.message}"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.invalid_token} || #{e.message}")
    end
  end
end
