module BulkJobSerializer
  class List
    def self.serialize jobs
      return nil unless jobs
      json = Jbuilder.new
      content = json.array! jobs do |job|
        json.(job, :id, :number_of_records, :completed_at, :payload, :successful, :failure)
        json.entity job.entity.upcase
        json.operation job.operation.upcase
        json.category job.category.upcase
        json.status job.status.upcase
        json.submitted_at job.created_at
        json.errorFile error_file job
        json.successFile success_file job
        json.submitted_by Jbuilder.new{|resp| resp.(job.user, :id, :name) }
        json.executeWorkflow job.execute_workflow
        json.executeScoreRule job.execute_score_rule
        json.recordActions job.get_record_actions
      end

      JSON(
        Jbuilder.new { |resp|
          resp.content content
          resp.total_elements jobs.total_entries
          resp.total_pages jobs.total_pages
          resp.first jobs.previous_page.nil?
          resp.number_of_elements jobs.count
          resp.last jobs.next_page.nil?
          resp.size jobs.per_page
          resp.number (jobs.current_page.to_i - 1)
        }.target!
      )
    end

    def self.error_file job
      job.error_summary_file.present? ? SummaryFile::ERROR_FILE_NAME : nil
    end

    def self.success_file job
      job.summary_file.present? ? SummaryFile::SUCCESS_FILE_NAME : nil
    end
  end

  class Details
    def self.serialize_bulk_whatsapp_job_paused_email(job, pause_reason)
      return nil unless job.present?

      bulk_job_created_by_users = UserSerializer::Details.serialize(job.user)
      JSON(
        Jbuilder.new do |payload|
          payload.tenantId job.tenant_id
          payload.users [bulk_job_created_by_users]
          payload.entityGroup "Bulk Actions"
          payload.bulkJobId job.id
          payload.whatsappTemplate do
            payload.id job.payload.dig('whatsappTemplate', 'id')
            payload.name job.payload.dig('whatsappTemplate', 'name')
          end
          payload.reason pause_reason
          payload.campaignInfo do
            payload.campaignId job.payload.dig('campaign', 'id') if job.payload.dig('campaign', 'id').present?
            payload.campaignName job.payload.dig('campaign', 'name') if job.payload.dig('campaign', 'name').present?
            payload.activityId job.payload.dig('activity', 'id') if job.payload.dig('activity', 'id').present?
            payload.activityName job.payload.dig('activity', 'name') if job.payload.dig('activity', 'name').present?
          end
        end
        .target!
      )
    end
  end
end
