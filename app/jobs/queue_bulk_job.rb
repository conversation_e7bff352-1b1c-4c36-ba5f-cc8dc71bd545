# frozen_string_literal: true

class QueueBulkJob < ApplicationJob
  queue_as :queue_job
  sidekiq_options retry: 3

  before_enqueue do |job|
    bulk_job = BulkJob.find_by(id: job.arguments[0])
    bulk_job.update(sidekiq_job_id: job_id)
  end

  def perform(job_id)
    bulk_job = BulkJob.find_by(id: job_id, status: STATUS_QUEUING)

    return unless bulk_job.present?

    if bulk_job.entity == NOTE
      EntityAction::FetchNotes.new(bulk_job.id).call     
    else
      EntityAction::Fetch.new(bulk_job.id).call
    end

    bulk_job.reload.update(status: STATUS_QUEUED, sidekiq_job_id: nil)
    BulkActionJob.set(queue: bulk_job.sidekiq_queue_name).perform_later(bulk_job.id) unless bulk_job.tenant.running_job?
  end
end
