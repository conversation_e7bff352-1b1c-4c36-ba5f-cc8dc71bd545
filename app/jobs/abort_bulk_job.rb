# frozen_string_literal: true

class AbortBulkJob < ApplicationJob
  queue_as :queue_job
  sidekiq_options retry: 3

  def perform(job_id)
    bulk_job = BulkJob.find_by(id: job_id)
    return unless bulk_job&.status == STATUS_ABORTING
  
    if bulk_job.category == CAMPAIGN_ACTION && bulk_job.operation == WHATSAPP_MESSAGE_OPERATION
      bulk_job_records = Record.where(bulk_job_id: bulk_job.id).pluck(:id)
      retryable_messages = PhoneNumberWiseRecordDetail.where(record_id: bulk_job_records, status: STATUS_RETRYING, message_status: SENDING)
      records_message_ids = retryable_messages&.pluck(:message_id)
      retryable_record_ids = retryable_messages&.pluck(:record_id)

      if retryable_record_ids.present?
        Rails.logger.info "Updating retryable record statuses to failed for bulk job id: #{bulk_job.id}, tenant id: #{bulk_job.tenant_id}"
        Record.where(id: retryable_record_ids, bulk_job_id: bulk_job.id, tenant_id: bulk_job.tenant_id).update_all(status: Record.statuses["failed"])

        retryable_messages.update_all(error_message: 'Retry aborted by user', status: STATUS_FAILED, message_status: FAILED)
      end

      if records_message_ids.present?
        Rails.logger.info "Publishing WhatsappCampaignActivityAborted event, bulk job id: #{bulk_job.id}, tenant id: #{bulk_job.tenant_id}"
        Publishers::WhatsappCampaignActivityAbortedPublisher.call(bulk_job, records_message_ids)
      end

      if bulk_job.payload['retryConfig'].present?
        bulk_job.payload['retryConfig']['retryableMessages'] = 0
        bulk_job.save!
      end
    end

    success_count = Record.where(bulk_job_id: bulk_job.id, tenant_id: bulk_job.tenant_id, status: Record.statuses["succeeded"]).count
    failure_count = Record.where(bulk_job_id: bulk_job.id, tenant_id: bulk_job.tenant_id, status: Record.statuses["failed"]).count

    bulk_job.update(successful: success_count, failure: failure_count)
    CsvGenerator.new(bulk_job).call

    bulk_job.records.destroy_all
    bulk_job.update(completed_at: Time.now, status: STATUS_ABORTED)
  end
end
