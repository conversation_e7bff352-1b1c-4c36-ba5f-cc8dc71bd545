class BulkActionJob < ApplicationJob

  queue_as :default
  sidekiq_options retry: 3

  before_enqueue do |job|
    bulk_job = BulkJob.find_by(id: job.arguments[0])
    bulk_job.update(sidekiq_job_id: job_id)
  end

  after_perform do |job|
    bulk_job = BulkJob.find_by(id: job.arguments[0])
    bulk_job.update(sidekiq_job_id: nil)
  end

  def perform(job_id)
    bulk_job = BulkJob.find_by(id: job_id)

    Rails.logger.info "BULK JOB ROUND: #{job_id} | started"
    return if bulk_job.tenant.running_job? && bulk_job.tenant.running_job.id != bulk_job.id

    unless bulk_job.completed?
      operation_klass = "EntityAction::#{bulk_job.operation.camelize}".constantize
      res = operation_klass.new(bulk_job).perform
      if res&.is_a?(Hash)
        if res[:round_robin_aborted]
          Rails.logger.info "BULK JOB ROUND: #{job_id} | Round Robin Paused"
          return
        end

        if res[:paused]
          Rails.logger.info "BULK JOB ROUND: #{job_id} | Paused"
          return
        end

        if res[:retrying]
          Rails.logger.info "BULK JOB ROUND: #{job_id} | Retrying"
          enqueue_next_job(bulk_job)

          return
        end
      end
      
      bulk_job.records.destroy_all
    end

    Rails.logger.info "BULK JOB ROUND: #{job_id} | completed"
    enqueue_next_job(bulk_job)
  end

  private

  def enqueue_next_job(bulk_job)
    next_queued_job = bulk_job.tenant.first_queued_job
    BulkActionJob.set(queue: next_queued_job.sidekiq_queue_name).perform_later next_queued_job.id if next_queued_job
  end
end
