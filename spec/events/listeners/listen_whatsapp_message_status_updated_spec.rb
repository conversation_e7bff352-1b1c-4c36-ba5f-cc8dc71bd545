require 'rails_helper'

RSpec.describe Listeners::ListenWhatsappMessageStatusUpdated do
  describe '.listen' do
    let(:payload) do
      {
        'id' => 123,
        'status' => 'sent',
        'conversationId' => 1,
        'relatedTo' => [
          {
            'id' => 1,
            'entity' => 'lead',
            'name' => 'Lead 1'
          }
        ],
        'conversationOwnerId' => 2,
        'tenantId' => 2
      }
    end

    let(:message) { payload.to_json }
    let(:tenant) { create(:tenant)}
    let(:bulk_job) { create(:bulk_job, tenant_id: tenant.id)}
    let(:record) { create(:record, bulk_job_id: bulk_job.id, tenant_id: tenant.id)}
    let(:phone_number_wise_record_detail) { create(:phone_number_wise_record_detail, message_id: 123, message_status: 'sending', record_id: record.id) }

    before do
      allow(RabbitmqConnection).to receive(:subscribe)
        .with(MESSAGE_EXCHANGE, WHATSAPP_MESSAGE_STATUS_UPDATED, WHATSAPP_MESSAGE_STATUS_UPDATED_QUEUE)
        .and_yield(payload.to_json)
    end

    context 'when phone_number_wise_record_detail is found' do
      it 'updates message_status' do
        expect(phone_number_wise_record_detail.message_id).to eq(123)
        expect(phone_number_wise_record_detail.status).to eq('queued')
        expect(phone_number_wise_record_detail.message_status).to eq('sending')

        described_class.listen

        expect(phone_number_wise_record_detail.reload.message_status).to eq('sent')
        expect(phone_number_wise_record_detail.reload.status).to eq('succeeded')
      end
    end

    context 'when phone_number_wise_record_detail is not found' do
      it 'logs error when phone_number_wise_record_detail is not found' do
        expect(PhoneNumberWiseRecordDetail).to receive(:find_by).with(message_id: 123).and_return(nil)
        expect(Rails.logger).to receive(:error).with('ListenWhatsappMessageStatusUpdated | No record found with message_id 123')

        described_class.listen
      end
    end
  end
end
