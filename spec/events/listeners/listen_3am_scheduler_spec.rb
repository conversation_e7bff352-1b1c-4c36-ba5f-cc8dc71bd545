# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe Listeners::Listen3amScheduler do
  describe '#listen' do
    context "3 am event" do
      before do
        @bulk_job = create(:bulk_job, sidekiq_job_id: 1, started_at: 8.days.ago, status: 'paused')

        connection = BunnyMock.new
        @channel = connection.start.channel
        @exchange = @channel.topic SCHEDULER_EXCHANGE
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: described_class::EVENT_3AM
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SCHEDULER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe).with(
          SCHEDULER_EXCHANGE, described_class::EVENT_3AM, described_class::EVENT_3AM_QUEUE
        ).and_yield
      end

      it 'aborts old paused jobs' do
        described_class.listen()
        expect(@bulk_job.reload.status).to eq('aborted')
      end
    end
  end
end
