require 'rails_helper'
require 'bunny-mock'

RSpec.describe Listeners::SchedulerReminderScan do
  describe '#listen' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SCHEDULER_EXCHANGE
      @queue = @channel.queue ""
      @queue.bind @exchange, routing_key: routing_key
      allow(RabbitmqConnection).to receive(:get_exchange)
        .with(SCHEDULER_EXCHANGE)
        .and_return(@exchange)
      allow(RabbitmqConnection).to receive(:get_channel)
        .and_return(@channel)
    end

    context "scheduler reminder scan event" do
      let(:routing_key){ Listeners::SchedulerReminderScan::SCHEDULER_REMINDER_SCAN_EVENT }

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(SCHEDULER_EXCHANGE, Listeners::SchedulerReminderScan::SCHEDULER_REMINDER_SCAN_EVENT,
               Listeners::SchedulerReminderScan::SCHEDULER_REMINDER_SCAN_QUEUE).and_yield
        @bulk_job = create(:bulk_job, sidekiq_job_id: 1)
      end

      it 'should resume halted jobs' do
        Listeners::SchedulerReminderScan.listen
        expect(@bulk_job.reload.sidekiq_job_id).not_to eq 1
        expect(@bulk_job.reload.sidekiq_job_id).not_to be_blank
      end
    end
  end
end
