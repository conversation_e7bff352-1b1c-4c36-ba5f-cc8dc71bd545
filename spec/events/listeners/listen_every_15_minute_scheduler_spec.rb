# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe Listeners::ListenEvery15MinuteScheduler do
  describe '#listen' do
    context "15 minute event" do
      before do
        @bulk_job = create(:bulk_job, sidekiq_job_id: 1, started_at: 2.minutes.ago, status: 'retrying')
        @bulk_job.payload['retryConfig'] = { 'noOfTimes' => 3, 'retriedCount' => 1, 'nextRetryAt' => 20.minute.ago }

        @bulk_job.save!
        connection = BunnyMock.new
        @channel = connection.start.channel
        @exchange = @channel.topic SCHEDULER_EXCHANGE
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: described_class::EVENT_EVERY_15_MINUTES
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SCHEDULER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe).with(
          SCHEDULER_EXCHANGE, described_class::EVENT_EVERY_15_MINUTES, described_class::EVENT_EVERY_15_MINUTES_QUEUE
        ).and_yield
      end

      it 'updates status of bulk job to queued' do
        described_class.listen()
        expect(@bulk_job.reload.status).to eq('queued')
      end
    end
  end
end
