# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe Listeners::ListenCampaignActionCommand do
  describe '.listen' do
    let(:payload) do
      {
        'campaign' => { 'id' => 101, 'name' => 'Test Campaign' },
        'executionAction' => 'START',
        'activity' => {
          'id' => 1,
          'name' => 'Activity 1',
          'entity' => 'LEAD',
          'filters' => {},
          'payload' => { 'type' => 'WHATSAPP', 'connectedAccount' => {}, 'whatsappTemplate' => {}, 'sentTo' => 'PRIMARY_PHONE_NUMBER' },
          'startedBy' => {
            'id' => 12,
            'name' => 'test user'
          }
        },
        'creator' => 12,
        'userId' => 12,
        'tenantId' => 3212
      }.to_json
    end

    before do
      allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload)
      allow(Rails.logger).to receive(:info)
      allow(Rails.logger).to receive(:error)
    end

    it 'calls process_campaign_action_command and logs info' do
      expect(described_class).to receive(:process_campaign_action_command)
      expect(Rails.logger).to receive(:info).at_least(:once)
      described_class.listen
    end

    it 'logs error if StandardError is raised' do
      allow(described_class).to receive(:process_campaign_action_command).and_raise(StandardError, 'fail')
      expect(Rails.logger).to receive(:error).with(/Error processing campaign action command: fail/)
      described_class.listen
    end
  end

  describe '.process_campaign_action_command' do
    let(:campaign) { { 'id' => 101, 'name' => 'Test Campaign' } }
    let(:creator) { { 'id' => 11164, 'name' => 'Abhinav Kale' } }
    let(:creator_id) { creator['id'] }
    let(:tenant_id) { 3212 }
    let(:user_id) { { 'id' => 3841, 'name' => 'Karan Sharma' } }
    let(:activity) do
      {
        'id' => 1,
        'name' => 'Activity 1',
        'entity' => 'LEAD',
        'filters' => {},
        'payload' => { 'type' => 'WHATSAPP', 'connectedAccount' => {}, 'whatsappTemplate' => {}, 'sentTo' => 'PRIMARY_PHONE_NUMBER' },
        'startedBy' => {
          'id' => 12,
          'name' => 'test user'
        }
      }
    end

    let(:data) do
      {
        'campaign' => campaign,
        'creator' => creator,
        'tenantId' => tenant_id,
        'userId' => user_id,
        'executionAction' => execution_action,
        'activity' => activity
      }
    end

    before do
      allow(GenerateToken).to receive_message_chain(:new, :call).and_return('token')
      allow(User).to receive(:find_or_create_by).and_return(double('user', tenant_id: tenant_id, token: 'token'))
      allow(Thread.current).to receive(:[]=)
    end

    context 'when executionAction is START' do
      let(:execution_action) { START }

      it 'calls create_bulk_job_for_activity for each activity with userId' do
        expect(described_class).to receive(:create_bulk_job_for_activity).with(activity, campaign, 12, tenant_id)
        described_class.process_campaign_action_command(data)
      end
    end

    context 'when executionAction is PAUSE' do
      let(:execution_action) { PAUSE }

      it 'calls process_campaign_status_change for each activity' do
        expect(described_class).to receive(:process_campaign_status_change).with(activity, campaign, tenant_id, PAUSE)
        described_class.process_campaign_action_command(data)
      end
    end

    context 'when executionAction is RESUME' do
      let(:execution_action) { RESUME }
      
      it 'calls process_campaign_status_change for each activity' do
        expect(described_class).to receive(:process_campaign_status_change).with(activity, campaign, tenant_id, RESUME)
        described_class.process_campaign_action_command(data)
      end
    end

    context 'when executionAction is ABORT' do
      let(:execution_action) { ABORT }

      it 'calls process_campaign_status_change for each activity' do
        expect(described_class).to receive(:process_campaign_status_change).with(activity, campaign, tenant_id, ABORT)
        described_class.process_campaign_action_command(data)
      end
    end
  end

  describe '.create_bulk_job_for_activity' do
    let(:activity) do
      {
        'id' => 1,
        'name' => 'Name of Activity',
        'utmSource' => 'google',
        'utmCampaign' => 'utm campaign',
        'utmMedium' => 'medium',
        'utmContent' => 'content',
        'utmTerm' => 'term',
        'filters' => {
          'jsonRule' => {
            'rules' => [
              {
                'operator' => 'in',
                'field' => 'id',
                'type' => 'long',
                'value' => '553092,553052',
                'timeZone' => nil,
                'id' => 'id'
              }
            ],
            'condition' => 'AND',
            'valid' => true
          }
        },
        'payload' => {
          'type' => 'WHATSAPP',
          'connectedAccount' => { 'id' => 4, 'name' => 'New Wa Ba' },
          'whatsappTemplate' => { 'id' => 47, 'name' => 'Welcome to Kylas' },
          'sentTo' => 'PRIMARY_PHONE_NUMBER'
        },
        'startedBy' => {
          'id' => 3841,
          'name' => 'test user'
        },
        'entity' => 'LEAD',
        "creator": {
          "id": 11164,
          "name": "Abhinav Kale"
        },
        "userId": {
          "id": 3841,
          "name": "Karan Sharma"
        },
        "tenantId": 2048
      }
    end

    let(:activity_with_retry_details) do
      {
        'id' => 1,
        'name' => 'Name of Activity',
        'utmSource' => 'google',
        'utmCampaign' => 'utm campaign',
        'utmMedium' => 'medium',
        'utmContent' => 'content',
        'utmTerm' => 'term',
        'filters' => {
          'jsonRule' => {
            'rules' => [
              {
                'operator' => 'in',
                'field' => 'id',
                'type' => 'long',
                'value' => '553092,553052',
                'timeZone' => nil,
                'id' => 'id'
              }
            ],
            'condition' => 'AND',
            'valid' => true
          }
        },
        'payload' => {
          'type' => 'WHATSAPP',
          'connectedAccount' => { 'id' => 4, 'name' => 'New Wa Ba' },
          'whatsappTemplate' => { 'id' => 47, 'name' => 'Welcome to Kylas' },
          'sentTo' => 'PRIMARY_PHONE_NUMBER',
          'retryConfig' => { 'noOfTimes' => 3 }
        },
        'startedBy' => {
          'id' => 3841,
          'name' => 'test user'
        },
        'entity' => 'LEAD',
        "creator": {
          "id": 11164,
          "name": "Abhinav Kale"
        },
        "userId": {
          "id": 3841,
          "name": "Karan Sharma"
        },
        "tenantId": 2048
      }
    end

    let(:campaign) { { 'id' => 101, 'name' => 'Summer Sale Campaign' } }
    let(:creator) { { 'id' => 11164, 'name' => 'Abhinav Kale' } }
    let(:creator_id) { creator['id'] }
    let(:user_id_value) { 3841 }
    let(:tenant_id) { 3212 }

    context 'when activity type is WHATSAPP' do
      context 'success' do
        it 'creates a bulk job' do
          expected_params = {
            entity: 'lead',
            operation: WHATSAPP_MESSAGE_OPERATION,
            filters: activity['filters'],
            payload: {
              connectedAccount: activity['payload']['connectedAccount'],
              whatsappTemplate: activity['payload']['whatsappTemplate'],
              messageSendTo: [
                {
                  name: "Record's primary phone number", 
                  type: 'PRIMARY_PHONE_NUMBER'
                }
              ],
              campaign: {
                id: campaign['id'],
                name: campaign['name']
              },
              activity: {
                id: activity['id'],
                name: activity['name']
              },
              retryConfig: nil
            },
            category: CAMPAIGN_ACTION
          }

          expect(BulkJobService).to receive(:create).with(expected_params).and_return(1)

          described_class.create_bulk_job_for_activity(activity, campaign, user_id_value, tenant_id)
        end

        context 'when retryDetails is provided in activity payload' do
          it 'creates a bulk job with retryConfig in payload' do
            expected_params = {
              entity: 'lead',
              operation: WHATSAPP_MESSAGE_OPERATION,
              filters: activity_with_retry_details['filters'],
              payload: {
                connectedAccount: activity_with_retry_details['payload']['connectedAccount'],
                whatsappTemplate: activity_with_retry_details['payload']['whatsappTemplate'],
                messageSendTo: [
                  {
                    name: "Record's primary phone number", 
                    type: 'PRIMARY_PHONE_NUMBER'
                  }
                ],
                campaign: {
                  id: campaign['id'],
                  name: campaign['name']
                },
                activity: {
                  id: activity_with_retry_details['id'],
                  name: activity_with_retry_details['name']
                },
                retryConfig: {
                  noOfTimes: activity_with_retry_details.dig('payload', 'retryConfig', 'noOfTimes'),
                  timesRetried: 0
                }
              },
              category: CAMPAIGN_ACTION
            }

            expect(BulkJobService).to receive(:create).with(expected_params).and_return(1)

            described_class.create_bulk_job_for_activity(activity_with_retry_details, campaign, user_id_value, tenant_id)
          end
        end
      end

      context 'Error' do
        context 'when insufficient whatsapp credits are available' do
          it 'publishes CampaignActivityBulkJobStatus event and logs an error' do
            allow(BulkJobService).to receive(:create).and_raise(ExceptionHandler::InsufficientWhatsappCreditsForBulkAction, I18n.t('whatsapp_message.insufficient_whatsapp_credits_for_bulk'))
            expect(Rails.logger).to receive(:error).with(/Insufficient WhatsApp credits for bulk action/)
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with({:event_payload=>{:activityId=>1, :campaignId=>101, :entity=>"LEAD", :jobStatus=>"FAILED", :tenantId=>3212, :userId=>3841}})

            described_class.create_bulk_job_for_activity(activity, campaign, user_id_value, tenant_id)
          end
        end

        context 'when exception is raised while creating bulk job' do
          it 'publishes CampaignActivityBulkJobStatus event and logs an error' do
            allow(BulkJobService).to receive(:create).and_raise(ExceptionHandler::InvalidDataError, 'Invalid Data')
            expect(Rails.logger).to receive(:error).with(/Invalid data for bulk job creation, Error: Invalid Data, Tenant Id: 3212, Campaign Id: 101, Activity Id: 1/)
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with({:event_payload=>{:activityId=>1, :campaignId=>101, :entity=>"LEAD", :jobStatus=>"FAILED", :tenantId=>3212, :userId=>3841}})

            described_class.create_bulk_job_for_activity(activity, campaign, user_id_value, tenant_id)
          end
        end
      end
    end

    context 'when activity type is EMAIL' do
      let(:email_activity) do
        activity.deep_dup.tap do |act|
          act['payload'] = {
            'type' => 'EMAIL',
            'emailTemplate' => { 'id' => 47, 'name' => 'Welcome to Kylas' },
            'sentTo' => 'PRIMARY_EMAIL',
            'fromId' => '<EMAIL>'
          }
        end
      end

      context 'success' do
        it 'creates a bulk job' do
          expected_params = {
            entity: 'lead',
            operation: EMAIL_OPERATION,
            filters: email_activity['filters'],
            payload: {
              emailTemplate: email_activity['payload']['emailTemplate'],
              sentTo: email_activity['payload']['sentTo'],
              fromId: email_activity['payload']['fromId'],
              campaign: {
                id: campaign['id'],
                name: campaign['name']
              },
              activity: {
                id: email_activity['id'],
                name: email_activity['name']
              },
              trackingEnabled: true
            },
            category: CAMPAIGN_ACTION
          }

          expect(BulkJobService).to receive(:create).with(expected_params).and_return(1)

          described_class.create_bulk_job_for_activity(email_activity, campaign, user_id_value, tenant_id)
        end
      end

      context 'Error' do
        context 'when exception is raised while creating bulk job' do
          it 'publishes CampaignActivityBulkJobStatus event and logs an error' do
            allow(BulkJobService).to receive(:create).and_raise(ExceptionHandler::InvalidDataError, 'Invalid Data')
            expect(Rails.logger).to receive(:error).with(/Invalid data for bulk job creation, Error: Invalid Data, Tenant Id: 3212, Campaign Id: 101, Activity Id: 1/)
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with({:event_payload=>{:activityId=>1, :campaignId=>101, :entity=>"LEAD", :jobStatus=>"FAILED", :tenantId=>3212, :userId=>3841}})

            described_class.create_bulk_job_for_activity(email_activity, campaign, user_id_value, tenant_id)
          end
        end
      end
    end

    context 'when activity type is unsupported' do
      it 'skips creating a bulk job' do
        unsupported_activity = activity.deep_dup
        unsupported_activity['payload']['type'] = 'SMS'

        expect(BulkJobService).not_to receive(:create)
        expect(Rails.logger).to receive(:info).with(/Skipping unsupported activity type SMS/)

        described_class.create_bulk_job_for_activity(unsupported_activity, campaign, user_id_value, tenant_id)
      end
    end
  end

  describe '.admin_permissions' do
    it 'includes both whatsappTemplate and email permissions' do
      result = described_class.admin_permissions

      whatsapp_permission = result.find { |p| p[:name] == 'whatsappTemplate' }
      expect(whatsapp_permission).to be_present
      expect(whatsapp_permission[:id]).to eq(5)

      email_permission = result.find { |p| p[:name] == EMAIL }
      expect(email_permission).to be_present
      expect(email_permission[:id]).to eq(6)
      expect(email_permission[:action][:email]).to be true
    end

    it 'includes all base permissions' do
      result = described_class.admin_permissions
      expect(result.length).to eq(7)
      expect(result.map { |p| p[:name] }).to match_array([LEAD, CONTACT, 'sms', USER, 'whatsappTemplate', EMAIL, 'email_template'])
    end
  end

  describe '.publish_campaign_activity_job_status_event' do
    let(:event_data) { { campaignId: 1, activityId: 2, entity: 'LEAD', userId: 3, tenantId: 4 } }

    it 'calls the publisher with default status' do
      expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(event_payload: event_data.merge(jobStatus: STATUS_FAILED.upcase))
      described_class.publish_campaign_activity_job_status_event(event_data)
    end

    it 'calls the publisher with custom status' do
      expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(event_payload: event_data.merge(jobStatus: 'SUCCESS'))
      described_class.publish_campaign_activity_job_status_event(event_data, 'SUCCESS')
    end
  end

  describe '.process_campaign_status_change' do
    let(:activity_id) { 1 }
    let(:campaign) { { 'id' => 101, 'name' => 'Test Campaign' } }
    let(:tenant_id) { 3212 }
    let(:activity) do
      {
        'id' => activity_id,
        'payload' => { 'type' => 'WHATSAPP' }
      }
    end
    let(:bulk_job) { double('BulkJob', id: 55) }

    before do
      allow(BulkJob).to receive(:where).and_return([bulk_job])
      allow(Rails.logger).to receive(:info)
      allow(Rails.logger).to receive(:error)
    end

    context 'when payload is blank' do
      it 'logs and returns' do
        activity['payload'] = nil
        expect(Rails.logger).to receive(:info).with(/Skipping unsupported activity type/)
        described_class.process_campaign_status_change(activity, campaign, tenant_id, PAUSE)
      end
    end

    context 'when payload type is unsupported' do
      it 'logs and returns' do
        activity['payload'] = { 'type' => 'SMS' }
        expect(Rails.logger).to receive(:info).with(/Skipping unsupported activity type SMS/)
        described_class.process_campaign_status_change(activity, campaign, tenant_id, PAUSE)
      end
    end

    context 'when payload type is EMAIL' do
      let(:email_activity) do
        {
          'id' => activity_id,
          'payload' => { 'type' => 'EMAIL' }
        }
      end

      it 'processes the email activity' do
        expect(BulkJob).to receive(:where).and_return([bulk_job])
        expect(Rails.logger).not_to receive(:info).with(/Skipping/)
        described_class.process_campaign_status_change(email_activity, campaign, tenant_id, PAUSE)
      end
    end

    context 'when no bulk job found' do
      it 'logs and returns' do
        allow(BulkJob).to receive(:where).and_return([])
        expect(Rails.logger).to receive(:error).with(/No bulk jobs found/)
        described_class.process_campaign_status_change(activity, campaign, tenant_id, PAUSE)
      end
    end

    context 'when execution_action is PAUSE' do
      it 'pauses the bulk job' do
        service = double('BulkJobService')
        expect(BulkJobService).to receive(:new).with(bulk_job.id, can_change_campaign_bulk_job_status: true).and_return(service)
        expect(service).to receive(:pause)
        described_class.process_campaign_status_change(activity, campaign, tenant_id, PAUSE)
      end
    end

    context 'when execution_action is RESUME' do
      it 'resumes the bulk job' do
        service = double('BulkJobService')
        expect(BulkJobService).to receive(:new).with(bulk_job.id, can_change_campaign_bulk_job_status: true).and_return(service)
        expect(service).to receive(:resume)
        described_class.process_campaign_status_change(activity, campaign, tenant_id, RESUME)
      end
    end

    context 'when execution_action is ABORT' do
      it 'aborts the bulk job' do
        service = double('BulkJobService')
        expect(BulkJobService).to receive(:new).with(bulk_job.id, can_change_campaign_bulk_job_status: true).and_return(service)
        expect(service).to receive(:abort)
        described_class.process_campaign_status_change(activity, campaign, tenant_id, ABORT)
      end
    end

    context 'when StandardError is raised' do
      it 'logs the error' do
        service = double('BulkJobService')
        expect(BulkJobService).to receive(:new).and_return(service)
        allow(service).to receive(:pause).and_raise(StandardError, 'fail')
        expect(Rails.logger).to receive(:error).with("Error processing PAUSE command for bulk job #{bulk_job.id}: fail")
        described_class.process_campaign_status_change(activity, campaign, tenant_id, PAUSE)
      end
    end
  end
end
