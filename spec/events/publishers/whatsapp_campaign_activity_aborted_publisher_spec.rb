# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::WhatsappCampaignActivityAbortedPublisher do
  let(:bulk_job) do
    double(
      'BulkJob',
      id: 101,
      entity: 'lead',
      user_id: 1,
      tenant_id: 99,
      payload: {
        'campaign' => { 'id' => 456 },
        'activity' => { 'id' => 789 }
      }
    )
  end
  let(:retryable_message_ids) { [111, 222, 333] }
  let(:event_instance) { instance_double(Event::WhatsappCampaignActivityAborted, to_json: '{"campaignId":"456","tenantId": 99, "messageIds": [111, 222, 333]}') }
  let(:publish_event_instance) { instance_double(PublishEvent, call: true) }

  before do
    allow(Event::WhatsappCampaignActivityAborted).to receive(:new).and_return(event_instance)
    allow(PublishEvent).to receive(:new).with(event_instance).and_return(publish_event_instance)
    allow(Rails.logger).to receive(:info)
  end

  it 'logs the start and end of the publisher, builds and publishes the event' do
    described_class.call(bulk_job, retryable_message_ids)

    expect(Rails.logger).to have_received(:info).with("Bulk Actions Service: Publishers::WhatsappCampaignActivityAbortedPublisher called, bulk job id: #{bulk_job.id}, tenant id: #{bulk_job.tenant_id}")
    expect(Event::WhatsappCampaignActivityAborted).to have_received(:new).with(
      campaignId: 456,
      activityId: 789,
      bulkJobId: 101,
      entity: 'LEAD',
      userId: 1,
      tenantId: 99,
      messageIds: retryable_message_ids
    )
    expect(PublishEvent).to have_received(:new).with(event_instance)
    expect(publish_event_instance).to have_received(:call)
    expect(Rails.logger).to have_received(:info).with("Event::WhatsappCampaignActivityAbortedPublisher is sent, data: #{event_instance.to_json}")
  end
end
