# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe Publishers::SendBulkWhatsappJobPausedEmailPublisher do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
      @queue = @channel.queue BULK_JOB_PAUSED
      @queue.bind @exchange, routing_key: BULK_JOB_PAUSED
      allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
    end

    context 'when the SendBulkWhatsappJobPausedEmailPublisher is called' do
      let(:payload) {
        {
          "connectedAccount": {
            "id": 123,
            "name": "Whatsapp Account 1"
          },
          "whatsappTemplate": {
            "id": 123,
            "name": "Whatsapp Template 1"
          },
          "messageSendTo": [
            {
              "name": "Record's primary phone number",
              "type": "PRIMARY_PHONE_NUMBER"
            }
          ]
        }
      }

      let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: '<PERSON>', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload, entity: 'lead') }

      context 'when paused reason is inactive_whatsapp_template' do
        before do
          bulk_job
          Publishers::SendBulkWhatsappJobPausedEmailPublisher.call(bulk_job, INACTIVE_WHATSAPP_TEMPLATE_REASON)
        end

        it 'publishes correct number of events' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload' do
          payload = @queue.pop
          expect(JSON.parse(payload[:message])).to eq(
            {
              "tenantId"=>99,
              "users"=> [
                {
                "id"=> 1,
                "name"=> "Tony Stark"
                }
              ],
              "entityGroup"=> "Bulk Actions",
              "bulkJobId"=> bulk_job.id,
              "whatsappTemplate"=> {
                "id"=> 123,
                "name"=> "Whatsapp Template 1"
              },
              "reason"=> "inactive_whatsapp_template"
            }
          )
        end
      end

      context 'when paused reason is insufficient_whatsapp_credits' do
        before do
          bulk_job
          Publishers::SendBulkWhatsappJobPausedEmailPublisher.call(bulk_job, INSUFFICIENT_WHATSAPP_CREDITS_REASON)
        end

        it 'publishes correct number of events' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload' do
          payload = @queue.pop
          expect(JSON.parse(payload[:message])).to eq(
            {
              "tenantId"=>99,
              "users"=> [
                {
                "id"=> 1,
                "name"=> "Tony Stark"
                }
              ],
              "entityGroup"=> "Bulk Actions",
              "bulkJobId"=> bulk_job.id,
              "whatsappTemplate"=> {
                "id"=> 123,
                "name"=> "Whatsapp Template 1"
              },
              "reason"=> "insufficient_whatsapp_credits"
            }
          )
        end
      end

      context 'when paused reason is template_entity_mismatch_or_variables_not_mapped' do
        before do
          bulk_job
          Publishers::SendBulkWhatsappJobPausedEmailPublisher.call(bulk_job, TEMPLATE_ENTITY_MISMATCH_OR_VARIABLSES_NOT_MAPPED_REASON)
        end

        it 'publishes correct number of events' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload' do
          payload = @queue.pop
          expect(JSON.parse(payload[:message])).to eq(
            {
              "tenantId"=>99,
              "users"=> [
                {
                "id"=> 1,
                "name"=> "Tony Stark"
                }
              ],
              "entityGroup"=> "Bulk Actions",
              "bulkJobId"=> bulk_job.id,
              "whatsappTemplate"=> {
                "id"=> 123,
                "name"=> "Whatsapp Template 1"
              },
              "reason"=> "template_entity_mismatch_or_variables_not_mapped"
            }
          )
        end
      end

      context 'when campaign info is present on the bulk job' do
        before do
          bulk_job.payload['campaign'] = { id: 123, name: 'Campaign 1' }
          bulk_job.payload['activity'] = { id: 234, name: 'Activity 1' }
          bulk_job.save
          Publishers::SendBulkWhatsappJobPausedEmailPublisher.call(bulk_job, TEMPLATE_ENTITY_MISMATCH_OR_VARIABLSES_NOT_MAPPED_REASON)
        end

        it 'publishes correct number of events' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes the correct payload with campaign info' do
          payload = @queue.pop
          expect(JSON.parse(payload[:message])).to eq(
            {
              "tenantId"=>99,
              "users"=> [
                {
                "id"=> 1,
                "name"=> "Tony Stark"
                }
              ],
              "entityGroup"=> "Bulk Actions",
              "bulkJobId"=> bulk_job.id,
              "whatsappTemplate"=> {
                "id"=> 123,
                "name"=> "Whatsapp Template 1"
              },
              "reason"=> "template_entity_mismatch_or_variables_not_mapped",
              "campaignInfo" => {
                "campaignId"=>123, 
                "campaignName"=>"Campaign 1", 
                "activityId"=>234, 
                "activityName"=>"Activity 1"
              }
            }
          )
        end
      end
    end 
  end
end
