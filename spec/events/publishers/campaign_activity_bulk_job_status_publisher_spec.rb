# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe Publishers::CampaignActivityBulkJobStatusPublisher, type: :model do
  describe '.call' do
    let(:bulk_job) do
      instance_double(
        'BulkJob',
        id: 1,
        entity: 'lead',
        user_id: 123,
        tenant_id: 456,
        payload: {
          'campaign' => { 'id' => 'campaign_1' },
          'activity' => { 'id' => 'activity_1' }
        }
      )
    end

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
      @queue = @channel.queue CAMPAIGN_ACTIVITY_BULK_JOB_STATUS
      @queue.bind @exchange, routing_key: CAMPAIGN_ACTIVITY_BULK_JOB_STATUS
      allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
    end

    context 'when the CampaignActivityBulkJobStatusPublisher is called' do
      it 'creates an event with the correct data' do
        expected_data = {
          campaignId: 'campaign_1',
          activityId: 'activity_1',
          jobStatus: 'IN_PROGRESS',
          entity: 'LEAD',
          userId: 123,
          tenantId: 456
        }

        expect(Rails.logger).to receive(:info).with('Bulk Actions Service: Publishers::CampaignActivityBulkJobStatusPublisher called')
        expect(Rails.logger).to receive(:info).with("Event::CampaignActivityBulkJobStatus is sent, data: #{expected_data.to_json}")

        described_class.call(bulk_job: bulk_job, status:'IN_PROGRESS')
      end

      it 'publishes correct number of events' do
        described_class.call(bulk_job: bulk_job, status: 'IN_PROGRESS')

        expect(@queue.message_count).to eq(1)
      end

      context 'when status reason is provided' do
        it 'includes status reason in the event data' do
          status_reason = 'Whatsapp Template is not Approved'
          expected_data = {
            campaignId: 'campaign_1',
            activityId: 'activity_1',
            jobStatus: 'PAUSED',
            entity: 'LEAD',
            userId: 123,
            tenantId: 456,
            statusReason: status_reason
          }

          expect(Rails.logger).to receive(:info).with('Bulk Actions Service: Publishers::CampaignActivityBulkJobStatusPublisher called')
          expect(Rails.logger).to receive(:info).with("Event::CampaignActivityBulkJobStatus is sent, data: #{expected_data.to_json}")

          described_class.call(bulk_job: bulk_job, status: 'PAUSED', status_reason: status_reason)
        end
      end
    end
  end
end
