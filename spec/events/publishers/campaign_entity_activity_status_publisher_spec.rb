# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe Publishers::CampaignEntityActivityStatusPublisher, type: :model do
  describe '.call' do
    let(:bulk_job) do
      instance_double(
        'BulkJob',
        id: 1,
        entity: 'lead',
        user_id: 123,
        tenant_id: 456,
        payload: {
          'campaign' => { 'id' => 'campaign_1' },
          'activity' => { 'id' => 'activity_1' }
        }
      )
    end
    
    let(:event_payload) do
      {
        campaignId: 456,
        activityId: 789,
        userId: 1,
        tenantId: 99,
        entity: 'LEAD',
        entityId: 1,
        phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'**********', 'primary'=>true, 'dialCode'=>'+91'},
        status: 'SUCCESS'
      }
    end

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
      @queue = @channel.queue CAMPAIGN_ENTITY_ACTIVITY_STATUS
      @queue.bind @exchange, routing_key: CAMPAIGN_ENTITY_ACTIVITY_STATUS
      allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
    end

    context 'when the CampaignEntityActivityStatusPublisher is called' do
      it 'creates an event with the correct data' do
        expect(Rails.logger).to receive(:info).with('Bulk Actions Service: Publishers::CampaignEntityActivityStatusPublisher called')
        expect(Rails.logger).to receive(:info).with("Event::CampaignEntityActivityStatus is sent, data: #{event_payload.to_json}")

        described_class.call(event_payload)
      end

      it 'publishes correct number of events' do
        described_class.call(event_payload)

        expect(@queue.message_count).to eq(1)
      end
    end
  end
end
