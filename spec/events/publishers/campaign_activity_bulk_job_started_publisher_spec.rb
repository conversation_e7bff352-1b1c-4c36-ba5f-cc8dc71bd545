# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe Publishers::CampaignActivityBulkJobStartedPublisher, type: :model do
  describe '.call' do
    let(:bulk_job) do
      instance_double(
        'BulkJob',
        id: 1,
        entity: 'lead',
        user_id: 123,
        tenant_id: 456,
        payload: {
          'campaign' => { 'id' => 'campaign_1' },
          'activity' => { 'id' => 'activity_1' }
        }
      )
    end
    let(:entity_details) do
      [
        { 
          entityId: 123, 
          entityName: 'Lead Name', 
          phoneNumber: {
            "id": 31657,
            "value": "**********",
            "dialCode": "+91"
          }
        }
      ]
    end

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
      @queue = @channel.queue CAMPAIGN_ACTIVITY_BULK_JOB_STARTED
      @queue.bind @exchange, routing_key: CAMPAIGN_ACTIVITY_BULK_JOB_STARTED
      allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
    end

    context 'when the CampaignActivityBulkJobStartedPublisher is called' do
      it 'creates an event with the correct data' do
        expected_data = {
          entityDetails: entity_details,
          campaignId: 'campaign_1',
          activityId: 'activity_1',
          bulkJobId: 1,
          entity: 'LEAD',
          userId: 123,
          tenantId: 456
        }

        expect(Rails.logger).to receive(:info).with('Bulk Actions Service: Publishers::CampaignActivityBulkJobStartedPublisher called')
        expect(Rails.logger).to receive(:info).with("Event::CampaignActivityBulkJobStarted is sent, CampaignId: campaign_1, ActivityId: activity_1, BulkJobId: 1")

        described_class.call(bulk_job, entity_details)
      end

      it 'publishes correct number of events' do
        described_class.call(bulk_job, entity_details)

        expect(@queue.message_count).to eq(1)
      end
    end
  end
end
