# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PhoneNumberWiseRecordDetail, type: :model do
  let(:user) { User.create(id:1, tenant_id: 1, name: '<PERSON><PERSON><PERSON> User') }
  let(:tenant) { Tenant.create(id: 1) }
  let(:bulk_job) {
    BulkJob.create(entity: LEAD, operation: WHATSAPP_MESSAGE_OPERATION, filters: { jsonRule: [{id: 1}] } , user: user, tenant: tenant)
  }
  let(:record) { Record.create(tenant: tenant, bulk_job: bulk_job, entity_id: 1) }
  let(:phone_number_wise_record_detail) { create(:phone_number_wise_record_detail, record_id: record.id, phone_number: '+918888663311') }

  describe '#validations' do
    context 'success' do
      it 'should be valid' do
        expect(phone_number_wise_record_detail).to be_valid
      end
    end

    context 'error' do
      context 'when phone number is not present' do
        it 'should be invalid' do
          phone_number_wise_record_detail.phone_number = nil
          expect(phone_number_wise_record_detail).to be_invalid
        end
      end

      context 'when phone number for log is not present' do
        it 'should be invalid' do
          phone_number_wise_record_detail.phone_number_for_log = nil
          expect(phone_number_wise_record_detail).to be_invalid
        end
      end

      context 'when status is not present' do
        it 'should be invalid' do
          phone_number_wise_record_detail.status = nil
          expect(phone_number_wise_record_detail).to be_invalid
        end
      end

      context 'when payload is not present' do
        it 'should be invalid' do
          phone_number_wise_record_detail.payload = nil
          expect(phone_number_wise_record_detail).to be_invalid
        end
      end
    end
  end

  describe '#associations' do
    it 'belongs to record' do
      record_relation = PhoneNumberWiseRecordDetail.reflect_on_association(:record)
      expect(record_relation.macro).to eq(:belongs_to)
    end
  end
end
