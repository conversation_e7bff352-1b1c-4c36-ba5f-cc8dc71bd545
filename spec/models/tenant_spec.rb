require 'rails_helper'

RSpec.describe Tenant,type: :model do
  describe 'methods' do
    let(:tenant) { create(:tenant) }
    let!(:first_queued_bulk_job) { create(:bulk_job, tenant: tenant, status: BulkJob.statuses[:queued]) }
    let!(:second_queued_bulk_job) { create(:bulk_job, tenant: tenant, status: BulkJob.statuses[:queued]) }
    
    context 'first_queued_job' do
      it 'returns first queued job sorted by id' do
        expect(tenant.first_queued_job).to eq first_queued_bulk_job
      end
    end

    context 'running_job?' do
      it 'returns true when job is running' do
        second_queued_bulk_job.update(status: BulkJob.statuses[:in_progress])
        expect(tenant.running_job?).to eq true
      end

      it 'returns false when job is not running' do
        expect(tenant.running_job?).to eq false
      end
    end
  end
end
