require 'rails_helper'

RSpec.describe BulkJob,type: :model do
  describe 'validations' do
    let(:bulk_job) {
      BulkJob.new(entity: LEAD, operation: UPDATE_OPERATION, filters: { jsonRule: [{id: 1}] },
                  user: User.new(id:1, tenant_id: 1, name: '<PERSON><PERSON><PERSON> User'),
                  tenant: Tenant.new(id: 1))
    }

    context 'when attributes are correct' do
      it 'should be valid' do
        expect(bulk_job).to be_valid
      end

      it 'email is valid entity' do
        bulk_job.entity = EMAIL
        expect(bulk_job).to be_valid
      end

      context 'when entity is task' do
        it 'should be valid' do
          bulk_job.entity = 'task'
          expect(bulk_job).to be_valid
        end
      end

      context 'when entity is note' do
        it 'should be valid' do
          bulk_job.entity = 'note'
          expect(bulk_job).to be_valid
        end
      end
    end

    context 'when attributes are not correct' do
      context 'when entity is blank' do
        it 'should be invalid' do
          bulk_job.entity = nil
          expect(bulk_job).to be_invalid
        end
      end

      context 'when status is blank' do
        it 'should be invalid' do
          bulk_job.status = nil
          expect(bulk_job).to be_invalid
        end
      end

      context 'when entity is invalid' do
        it 'should be invalid' do
          bulk_job.entity = 'invalid'
          expect(bulk_job).to be_invalid
        end
      end

      context 'when operation is invalid' do
        it 'should be invalid' do
          bulk_job.operation = 'invalid'
          expect(bulk_job).to be_invalid
        end
      end

      context 'when operation is blank' do
        it 'should be invalid' do
          bulk_job.operation = nil
          expect(bulk_job).to be_invalid
        end
      end

      context 'when email operation for entity other than lead, contact' do
        [TASK].each do |entity|
          it 'should be invalid with proper message' do
            bulk_job.operation = EMAIL_OPERATION
            bulk_job.entity = entity
            expect(bulk_job).to be_invalid
            expect(bulk_job.errors[:base].first).to eq("cannot perform bulk emails for #{entity.pluralize}")
          end
        end
      end

      context 'when whatsapp message operation for entity other than lead, contact and deal' do
        [TASK, CALL_LOG, USER, EMAIL, COMPANY, MEETING, NOTE].each do |entity|
          it "should be invalid for #{entity} with proper message" do
            bulk_job.operation = WHATSAPP_MESSAGE_OPERATION
            bulk_job.entity = entity
            expect(bulk_job).to be_invalid
            expect(bulk_job.errors[:base].first).to eq("cannot perform bulk whatsapp message for #{entity.pluralize}")
          end
        end
      end

      context 'when whatsapp message operation for deal entity' do
        it 'should be valid' do
          bulk_job.operation = WHATSAPP_MESSAGE_OPERATION
          bulk_job.entity = DEAL
          expect(bulk_job).to be_valid
        end

        it 'should allow deal whatsapp message with proper payload' do
          bulk_job.operation = WHATSAPP_MESSAGE_OPERATION
          bulk_job.entity = DEAL
          bulk_job.payload = {
            'connectedAccount' => { 'id' => 123 },
            'whatsappTemplate' => { 'id' => 456 },
            'messageSendTo' => [{ 'type' => 'PRIMARY_PHONE_NUMBER' }]
          }
          expect(bulk_job).to be_valid
        end

        it 'should allow deal whatsapp message with all phone numbers option' do
          bulk_job.operation = WHATSAPP_MESSAGE_OPERATION
          bulk_job.entity = DEAL
          bulk_job.payload = {
            'connectedAccount' => { 'id' => 123 },
            'whatsappTemplate' => { 'id' => 456 },
            'messageSendTo' => [{ 'type' => 'ALL_PHONE_NUMBERS' }]
          }
          expect(bulk_job).to be_valid
        end

        it 'should allow deal whatsapp message with campaign action category' do
          bulk_job.operation = WHATSAPP_MESSAGE_OPERATION
          bulk_job.entity = DEAL
          bulk_job.category = CAMPAIGN_ACTION
          bulk_job.payload = {
            'connectedAccount' => { 'id' => 123 },
            'whatsappTemplate' => { 'id' => 456 },
            'messageSendTo' => [{ 'type' => 'PRIMARY_PHONE_NUMBER' }],
            'campaign' => { 'id' => 789 },
            'activity' => { 'id' => 101112 }
          }
          expect(bulk_job).to be_valid
        end
      end

      context 'when category is other than bulk or campaign action' do
        it 'should be invalid' do
          bulk_job.category = 'invalid_category'
          expect(bulk_job).to be_invalid
          expect(bulk_job.errors[:category].first).to eq('is invalid')
        end
      end
    end
  end

  describe '#methods' do
    let(:bulk_job) {
      BulkJob.new(entity: LEAD, filters: { jsonRule: [{id: 1}] },
                  user: User.new(id:1, tenant_id: 1, name: 'Kylas User'),
                  tenant: Tenant.new(id: 1), operation: DELETE_OPERATION)
    }


    context 'sidekiq_queue_name' do
      it 'returns delete when operation is delete' do
        bulk_job.update(operation: DELETE_OPERATION)
        expect(bulk_job.reload.sidekiq_queue_name).to eq 'delete'
      end

      it 'returns default when operation is update' do
        bulk_job.update(operation: UPDATE_OPERATION)
        expect(bulk_job.reload.sidekiq_queue_name).to eq 'default'
      end

      it 'returns queue_job when status of job is queuing' do
        bulk_job.queuing!
        expect(bulk_job.reload.sidekiq_queue_name).to eq('queue_job')
      end
    end
  end

  describe '#get_record_actions' do
    before do
      @user = create(:user)
      @another_user = create(:user, tenant_id: @user.tenant_id)
    end

    context 'when user is not owner of bulk job' do
      before do
        @bulk_job = create(:bulk_job, user_id: @another_user.id, tenant_id: @user.tenant_id, status: 'in_progress')
        Thread.current[:user] = @user
      end

      context 'and user has update_all permission on entity' do
        before { Thread.current[:token] = get_test_jwt(@user.id, @user.tenant_id, true) }

        it 'returns correct record actions' do
          expect(@bulk_job.get_record_actions).to eq({
            update: true,
            delete: false
          })
        end
      end

      context 'and user does not have update_all permission on entity' do
        before { Thread.current[:token] = get_test_jwt(@user.id, @user.tenant_id, false) }

        it 'returns correct record actions' do
          expect(@bulk_job.get_record_actions).to eq({
            update: false,
            delete: false
          })
        end
      end
    end

    context 'when user is owner of bulk_job' do
      before do
        @bulk_job = create(:bulk_job, user_id: @user.id, tenant_id: @user.tenant_id)
        Thread.current[:user] = @user
        Thread.current[:token] = get_test_jwt(@user.id, @user.tenant_id, false, false)
      end

      it 'has permissions' do
        expect(@bulk_job.get_record_actions).to eq({
          delete: true,
          update: false
        })
      end
    end

    context 'when status is aborting or queuing' do
      before do
        @bulk_job = create(:bulk_job, user_id: @user.id, tenant_id: @user.tenant_id, status: 'queuing')
      end

      it 'returns nil' do
        expect(@bulk_job.get_record_actions).to be_nil
      end
    end
  end
end
