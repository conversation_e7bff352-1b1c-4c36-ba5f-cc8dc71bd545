require 'rails_helper'

RSpec.describe Record,type: :model do
  describe 'validations' do
    let(:user) { User.create(id:1, tenant_id: 1, name: '<PERSON><PERSON><PERSON> User') }
    let(:tenant) { Tenant.create(id: 1) }
    let(:bulk_job) {
      BulkJob.create(entity: LEAD, operation: UPDATE_OPERATION, filters: { jsonRule: [{id: 1}] } , user: user, tenant: tenant)
    }

    let(:record) { Record.new(tenant: tenant, bulk_job: bulk_job, entity_id: 1) }

    describe '#validations' do
      context 'when attributes are correct' do
        it 'should be valid' do
          expect(record).to be_valid
        end
      end

      context 'when attributes are not correct' do
        context 'when tenant_id is blank' do
          it 'should be invalid' do
            record.tenant_id = nil
            expect(record).to be_invalid
          end
        end

        context 'when bulk_job_id is invalid' do
          it 'should be invalid' do
            record.bulk_job_id = nil
            expect(record).to be_invalid
          end
        end

        context 'when status is invalid' do
          it 'should be invalid' do
            record.status = nil
            expect(record).to be_invalid
          end
        end

        context 'when entity_id is invalid' do
          it 'should be invalid' do
            record.entity_id = nil
            expect(record).to be_invalid
          end
        end
      end
    end

    describe '#Associations' do
      it 'has many phone_number_wise_record_details' do
        phone_number_wise_record_detail_relation = Record.reflect_on_association(:phone_number_wise_record_details)
        expect(phone_number_wise_record_detail_relation.macro).to eq(:has_many)
        expect(phone_number_wise_record_detail_relation.options[:dependent]).to eq(:destroy)
      end
    end
  end
end
