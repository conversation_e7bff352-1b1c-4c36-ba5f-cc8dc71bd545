require 'rails_helper'

RSpec.describe User,type: :model do
  describe 'validations' do
    let(:user) {
      User.new(id: 11, name: '<PERSON><PERSON><PERSON> User', tenant: Tenant.create)
    }

    context '#valid' do
      it 'when mandatory fields are provided' do
        expect(user).to be_valid
      end
    end

    context '#invalid' do
      context 'when tenant_id is blank' do
        it 'should be invalid' do
          user.tenant_id = nil
          expect(user).to be_invalid
        end
      end

      context 'when name is blank' do
        it 'should be invalid' do
          user.name = ''
          expect(user).to be_invalid
        end
      end
    end
  end
end
