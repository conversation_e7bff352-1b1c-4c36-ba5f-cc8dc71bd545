FactoryBot.define do
  factory :bulk_job do
    user
    tenant
    entity { LEAD }
    operation { UPDATE_OPERATION }
    category { BULK_ACTION }
    filters { {page: 0, size: 10, "sort": "updatedAt,desc","jsonRule"=>{"rules"=>[{"id"=>"ownerId", "type"=>"long", "field"=>"ownerId", "value"=>11, "operator"=>"equal"}] }}}
    payload {{"ownerId"=>11}}
    status { BulkJob.statuses[:completed] }
    started_at { Time.now.ago(1.hour)}
    completed_at { Time.now.ago(10.minutes)}
    number_of_records { rand(20) }
    execute_workflow { false }
    execute_score_rule { false }
  end
end
