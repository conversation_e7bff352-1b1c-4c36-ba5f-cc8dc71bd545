{"country": null, "updatedViaId": "WF_4735", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "Webhook and Marketplace action log", "utmContent": "Test 5", "source": 387685, "ownerId": 7990, "convertedAt": null, "createdAt": "2024-12-30T09:02:13.025Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513753, "state": null, "dnd": null, "updatedAt": "2024-12-30T09:02:13.475Z", "createdViaId": "universal-app-key", "companyCity": null, "taskDueOn": null, "utmSource": "Test 2", "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Universal API Key", "linkedIn": null, "latestActivityCreatedAt": "2024-12-30T09:02:13.478Z", "version": 1, "utmCampaign": "Test 3", "zipcode": null, "firstName": "MaskedPhone312", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "MaskedPhone312 MaskedPhone312", "campaign": 387681, "utmMedium": "Test 4", "salutation": null, "designation": null, "lastName": "MaskedPhone312", "requirementName": null, "photoUrls": null, "subSource": "Test 1", "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 216106, "type": "WORK", "value": "8899223312", "primary": true}, {"code": "IN", "dialCode": "+91", "id": 216106, "type": "WORK", "value": "8899223313", "primary": true}, {"code": "IN", "dialCode": "+91", "id": 216106, "type": "WORK", "value": "8899223314", "primary": true}], "products": [{"name": "new product", "tenantId": 3440, "id": 22628}], "importedBy": null, "emails": null, "updatedViaType": "Workflow", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": "Test 6", "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "API Key", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null, "metaData": {"idNameStore": {"cfTestWorkflow": {}, "country": {}, "convertedBy": {}, "updatedBy": {"7638": "<PERSON><PERSON><PERSON>"}, "timezone": {}, "requirementCurrency": {}, "source": {"387684": "LinkedIn", "387685": "Exhibition", "387683": "Facebook"}, "ownerId": {"7990": "mayur p", "8011": "st dp", "8913": "karan sharma", "8724": "Addi user", "7638": "<PERSON><PERSON><PERSON>"}, "companyEmployees": {"387673": "5-9-updated", "387674": "10-19"}, "companyBusinessType": {}, "importedBy": {}, "pipeline": {"5963": "start", "5965": "kak"}, "cfMultiPickListTest": {"505369": "2"}, "createdBy": {"8011": "st dp", "7638": "<PERSON><PERSON><PERSON>"}, "cfPicklistFieldTest": {"505350": "Tuesday"}, "companyCountry": {}, "campaign": {"419408": "new-updated", "387681": "Organic"}, "salutation": {"387669": "Mr", "387671": "Miss-updated", "387670": "Mrs"}, "pipelineStage": {"37310": "Open", "37302": "Open"}, "companyIndustry": {}}}}