{"content": [{"ownerId": 7127, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "estimatedClosureOn": "2022-04-05T06:30:00.000Z", "products": [{"quantity": 2.0, "price": {"currencyId": 431, "value": 2345678.0}, "name": "Another New Product", "discount": {"type": "PERCENTAGE", "value": 12.0}, "id": 15112}, {"quantity": 4.0, "price": {"currencyId": 431, "value": 2345.0}, "name": "New Product", "discount": {"type": "FIXED", "value": 234.0}, "id": 15111}], "estimatedValue": {"currencyId": 431, "value": 4700736.0}, "customFieldValues": {"customUrl": "https://kylas.io", "customCheckbox": true, "customPicklist": {"name": "def", "id": 8415}, "customTextField": "This is a custom text field", "customDateTime": "2022-04-07T07:00:00.000Z", "customDate": "2022-04-15T06:30:00.000Z", "customNumer": 12345, "customParagraph": "This is a custom paragraph text"}, "associatedContacts": null, "name": "Test Deal Name", "company": {"name": "Test Company", "id": 5257}, "id": 23956, "pipelineStage": {"name": "Proposal Sent", "id": 24020}, "ownedBy": {"name": "Another User", "id": 7127}}, {"estimatedValue": {"currencyId": 431, "value": 12345678.0}, "associatedContacts": [{"name": "New Contact To Check Call Logs", "id": 72577}, {"name": "<PERSON><PERSON><PERSON>", "id": 113751}], "name": "Yet Another deal with contact 72577", "company": null, "id": 25188, "ownerId": 4167, "pipelineStage": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": null, "products": []}, {"estimatedValue": {"currencyId": 431, "value": 23454.0}, "associatedContacts": [], "name": "New Deal", "company": null, "id": 36682, "ownerId": 4167, "pipelineStage": {"name": "Open", "id": 24019}, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": null, "products": []}, {"estimatedValue": {"currencyId": 431, "value": 1234576543.0}, "associatedContacts": [{"name": "<PERSON><PERSON><PERSON>", "id": 113751}], "name": "Deal to be shared with Quote & Doc", "company": null, "id": 36680, "ownerId": 4167, "pipelineStage": {"name": "Open", "id": 24019}, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": null, "products": []}, {"estimatedValue": {"currencyId": 431, "value": 765432.0}, "associatedContacts": [{"name": "New Contact", "id": 10682}], "name": "Quotation shared on Deal", "company": {"name": "All Data", "id": 6555}, "id": 36672, "ownerId": 4167, "pipelineStage": {"name": "Open", "id": 24019}, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": null, "products": []}, {"estimatedValue": {"currencyId": 431, "value": 12345678.0}, "associatedContacts": [{"name": "Quotation Share", "id": 120584}], "name": "Quotation Shared on Contact Deal", "company": null, "id": 36671, "ownerId": 4167, "pipelineStage": {"name": "Open", "id": 24019}, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": "2022-12-31T06:30:00.000Z", "products": []}, {"estimatedValue": {"currencyId": 431, "value": 2345678.0}, "associatedContacts": [{"name": "<PERSON><PERSON><PERSON>", "id": 113751}], "name": "new deal", "company": null, "id": 26243, "ownerId": 6781, "pipelineStage": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "Low Ewa", "id": 6781}, "estimatedClosureOn": null, "products": [{"quantity": 1.0, "price": {"currencyId": 431, "value": 2345678.0}, "name": "Another New Product", "discount": {"type": "PERCENTAGE", "value": 0.0}, "id": 15112}]}, {"estimatedValue": {"currencyId": 431, "value": 3940739.04}, "associatedContacts": [{"name": "<PERSON><PERSON><PERSON>", "id": 113751}], "name": "naya deal 2", "company": null, "id": 26248, "ownerId": 4167, "pipelineStage": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": null, "products": [{"quantity": 1.68, "price": {"currencyId": 431, "value": 2345678.0}, "name": "Another New Product", "discount": {"type": "PERCENTAGE", "value": 0.0}, "id": 15112}]}, {"estimatedValue": {"currencyId": 431, "value": 3940739.04}, "associatedContacts": [{"name": "<PERSON><PERSON><PERSON>", "id": 113751}], "name": "naya dea", "company": null, "id": 26247, "ownerId": 4167, "pipelineStage": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": null, "products": [{"quantity": 1.68, "price": {"currencyId": 431, "value": 2345678.0}, "name": "Another New Product", "discount": {"type": "PERCENTAGE", "value": 0.0}, "id": 15112}]}, {"estimatedValue": {"currencyId": 431, "value": 3940739.04}, "associatedContacts": [{"name": "<PERSON><PERSON><PERSON>", "id": 113751}], "name": "new deal", "company": {"name": "Test Company", "id": 5257}, "id": 26245, "ownerId": 4167, "pipelineStage": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true}, "ownedBy": {"name": "<PERSON><PERSON>", "id": 4167}, "estimatedClosureOn": null, "products": [{"quantity": 1.68, "price": {"currencyId": 431, "value": 2345678.0}, "name": "Another New Product", "discount": {"type": "PERCENTAGE", "value": 0.0}, "id": 15112}]}], "metaData": {"idNameStore": {}}, "last": false, "totalPages": 2, "totalElements": 17, "first": true, "sort": [{"direction": "DESC", "property": "updatedAt", "ignoreCase": false, "nullHandling": "NATIVE", "descending": true, "ascending": false}], "numberOfElements": 10, "size": 10, "number": 0}