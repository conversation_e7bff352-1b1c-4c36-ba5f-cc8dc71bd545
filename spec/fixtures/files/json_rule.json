{"json_rule": {"condition": "AND", "rules": [{"operator": "equal", "id": "status", "field": "status", "type": "long", "value": "IN_PROGRESS"}, {"operator": "in", "id": "id", "field": "id", "type": "double", "value": "1,2,3,4464,4567"}, {"operator": "equal", "id": "submittedBy", "field": "submittedBy", "type": "long", "value": 1}, {"operator": "between", "id": "submittedAt", "field": "submittedAt", "type": "date", "value": ["2022-09-09T09:30:00.000Z", "2022-09-10T09:30:00.000Z"]}], "valid": true}}