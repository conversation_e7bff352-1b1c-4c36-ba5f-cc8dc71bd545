{"content": [{"country": null, "updatedViaId": "WF_4735", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "Webhook and Marketplace action log", "utmContent": "Test 5", "source": 387685, "ownerId": 7990, "convertedAt": null, "createdAt": "2024-12-30T09:02:13.025Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513753, "state": null, "dnd": null, "updatedAt": "2024-12-30T09:02:13.475Z", "createdViaId": "universal-app-key", "companyCity": null, "taskDueOn": null, "utmSource": "Test 2", "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Universal API Key", "linkedIn": null, "latestActivityCreatedAt": "2024-12-30T09:02:13.478Z", "version": 1, "utmCampaign": "Test 3", "zipcode": null, "firstName": "MaskedPhone312", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "MaskedPhone312 MaskedPhone312", "campaign": 387681, "utmMedium": "Test 4", "salutation": null, "designation": null, "lastName": "MaskedPhone312", "requirementName": null, "photoUrls": null, "subSource": "Test 1", "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 216106, "type": "WORK", "value": "8899223312", "primary": true}], "products": [{"name": "new product", "tenantId": 3440, "id": 22628}], "importedBy": null, "emails": null, "updatedViaType": "Workflow", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": "Test 6", "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "API Key", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": "WF_4735", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "Webhook and Marketplace action log", "utmContent": null, "source": null, "ownerId": 8011, "convertedAt": null, "createdAt": "2024-12-30T08:54:19.546Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513752, "state": null, "dnd": null, "updatedAt": "2024-12-30T08:54:19.903Z", "createdViaId": "8011", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "User", "linkedIn": null, "latestActivityCreatedAt": "2024-12-30T08:54:19.904Z", "version": 1, "utmCampaign": null, "zipcode": null, "firstName": "test", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "test test", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": "test", "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 216105, "type": "MOBILE", "value": "8308429939", "primary": true}], "products": [], "importedBy": null, "emails": null, "updatedViaType": "Workflow", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 8011, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "Web", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": "WF_4735", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "Webhook and Marketplace action log", "utmContent": "Test 5", "source": 387685, "ownerId": 7990, "convertedAt": null, "createdAt": "2024-12-30T08:51:38.423Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513751, "state": null, "dnd": null, "updatedAt": "2024-12-30T08:51:39.251Z", "createdViaId": "universal-app-key", "companyCity": null, "taskDueOn": null, "utmSource": "Test 2", "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Universal API Key", "linkedIn": null, "latestActivityCreatedAt": "2024-12-30T08:51:39.252Z", "version": 1, "utmCampaign": "Test 3", "zipcode": null, "firstName": "MaskedPhone311", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "MaskedPhone311 MaskedPhone311", "campaign": 387681, "utmMedium": "Test 4", "salutation": null, "designation": null, "lastName": "MaskedPhone311", "requirementName": null, "photoUrls": null, "subSource": "Test 1", "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 216104, "type": "WORK", "value": "8899223311", "primary": true}], "products": [{"name": "new product", "tenantId": 3440, "id": 22628}], "importedBy": null, "emails": null, "updatedViaType": "Workflow", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": "Test 6", "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "API Key", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": [{"code": "US", "dialCode": "+1", "id": 36050, "type": "MOBILE", "value": "2345678901", "primary": true}], "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-30T07:54:15.992Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513750, "state": null, "dnd": null, "updatedAt": "2024-12-30T07:54:15.992Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": null, "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": [{"code": "IN", "dialCode": "+91", "id": 36049, "type": "MOBILE", "value": "1234567890", "primary": true}], "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-30T07:53:23.140Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513749, "state": null, "dnd": null, "updatedAt": "2024-12-30T07:53:23.140Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": "123", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "123", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": [{"code": "IN", "dialCode": "+91", "id": 36048, "type": "MOBILE", "value": "1234567890", "primary": true}], "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-30T07:51:29.475Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513748, "state": null, "dnd": null, "updatedAt": "2024-12-30T07:51:29.475Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": "ttt", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "ttt", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-30T07:10:06.593Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513747, "state": null, "dnd": null, "updatedAt": "2024-12-30T07:10:06.593Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": "123", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "123", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-30T07:09:55.703Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513746, "state": null, "dnd": null, "updatedAt": "2024-12-30T07:09:55.703Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": null, "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-30T07:09:10.831Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513745, "state": null, "dnd": null, "updatedAt": "2024-12-30T07:09:10.831Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": null, "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-30T07:08:42.827Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513744, "state": null, "dnd": null, "updatedAt": "2024-12-30T07:08:42.827Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": null, "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": "AX", "updatedViaId": "7638", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "User", "utmContent": "Utm content", "source": 387683, "ownerId": 8724, "convertedAt": null, "createdAt": "2024-12-03T10:19:30.715Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 512887, "state": null, "dnd": true, "updatedAt": "2024-12-18T08:10:10.273Z", "createdViaId": "57f00aea-7888-4370-99fd-7893875ec12f", "companyCity": null, "taskDueOn": null, "utmSource": "Utm source", "updatedBy": 7638, "companyAnnualRevenue": 23432.0, "createdViaName": "Facebook Lead Integration", "linkedIn": null, "latestActivityCreatedAt": "2024-12-30T07:07:55.104Z", "version": 12, "utmCampaign": "Utm campaign", "zipcode": "232", "firstName": "dfdfg", "addressCoordinate": null, "expectedClosureOn": "2024-12-18T08:30:00.000Z", "companyZipcode": null, "name": "dfdfg dfgfdf", "campaign": 387681, "utmMedium": "Utm medium", "salutation": 387670, "designation": null, "lastName": "dfgfdf", "requirementName": null, "photoUrls": null, "subSource": "Sub source", "city": "dfdsfsd", "timezone": "Pacific/Honolulu", "companyEmployees": 387674, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 216065, "type": "MOBILE", "value": "9988223311", "primary": true}], "products": [{"name": "1 BHK", "tenantId": 3440, "id": 23317}], "importedBy": null, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}], "updatedViaType": "Web", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": "OPEN", "facebook": null, "requirementCurrency": null, "utmTerm": "Utm term", "isNew": false, "companyBusinessType": "integrator", "pipeline": 5963, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": 37302, "companyWebsite": null, "requirementBudget": null, "createdViaType": "Marketplace", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": "ALTERNATIVE_DISPUTE_RESOLUTION"}, {"country": null, "updatedViaId": "7638", "companyPhones": null, "companyName": "sdfsdfsdf", "companyState": null, "updatedViaName": "User", "utmContent": "Test 5", "source": 387685, "ownerId": 7990, "convertedAt": null, "createdAt": "2024-12-12T01:14:48.480Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513620, "state": null, "dnd": null, "updatedAt": "2024-12-18T08:08:56.777Z", "createdViaId": "universal-app-key", "companyCity": null, "taskDueOn": null, "utmSource": "Test 2", "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Universal API Key", "linkedIn": null, "latestActivityCreatedAt": "2024-12-18T08:08:56.778Z", "version": 24, "utmCampaign": "Test 3", "zipcode": null, "firstName": "ssdfCAPI dsfsdfsdfsddfg", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "ssdfCAPI dsfsdfsdfsddfg MaskedPhone033", "campaign": 387681, "utmMedium": "Test 4", "salutation": 387670, "designation": null, "lastName": "MaskedPhone033", "requirementName": null, "photoUrls": null, "subSource": "Test 1", "city": "sdfsdf", "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 216055, "type": "WORK", "value": "9082408033", "primary": true}], "products": [{"name": "new product", "tenantId": 3440, "id": 22628}], "importedBy": null, "emails": null, "updatedViaType": "Web", "actualClosureDate": null, "department": "sdfdsf", "convertedBy": null, "address": "sdfsdf", "forecastingType": "OPEN", "facebook": null, "requirementCurrency": null, "utmTerm": "Test 6", "isNew": false, "companyBusinessType": null, "pipeline": 5965, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": 37310, "companyWebsite": null, "requirementBudget": null, "createdViaType": "API Key", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": "IN", "updatedViaId": "7638", "companyPhones": null, "companyName": "https://app-qa.sling-dev.com/sales/leads/list", "companyState": "sdfd", "updatedViaName": "User", "utmContent": "sdf", "source": 387684, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-16T08:08:56.850Z", "score": 12351.0, "twitter": "https://app-qa.sling-dev.com/sales/leads/list", "companyCountry": "DZ", "id": 513636, "state": "<PERSON><PERSON><PERSON>", "dnd": true, "updatedAt": "2024-12-16T08:57:31.264Z", "createdViaId": "7638", "companyCity": "teee", "taskDueOn": null, "utmSource": "sdf", "updatedBy": 7638, "companyAnnualRevenue": 12312.0, "createdViaName": "User", "linkedIn": "https://app-qa.sling-dev.com/sales/leads/list", "latestActivityCreatedAt": "2024-12-16T08:57:31.390Z", "version": 7, "utmCampaign": "sdf", "zipcode": null, "firstName": "CAPI", "customFieldValues": {"cfMultiPickListTest": [505369], "cfPicklistFieldTest": 505350, "cfCustomDateTimeField": "2024-12-16T08:30:00.000Z", "cfAssd": "sdf"}, "addressCoordinate": null, "expectedClosureOn": "2024-12-16T08:30:00.000Z", "companyZipcode": "23423", "name": "CAPI CAPI rert", "campaign": 419408, "utmMedium": "sdf", "salutation": 387671, "designation": "new", "lastName": "CAPI rert", "requirementName": "2344", "photoUrls": null, "subSource": "sdfd", "city": "pune", "timezone": "Pacific/Honolulu", "companyEmployees": 387673, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 216061, "type": "MOBILE", "value": "8833992211", "primary": false}, {"code": "IN", "dialCode": "+91", "id": 216060, "type": "MOBILE", "value": "8308429939", "primary": true}], "products": [{"name": "2BHK", "tenantId": 3440, "id": 23318}], "importedBy": null, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}, {"type": "OFFICE", "value": "<EMAIL>", "primary": false}], "updatedViaType": "Web", "actualClosureDate": null, "department": "test", "convertedBy": null, "address": "test", "forecastingType": "OPEN", "facebook": "https://app-qa.sling-dev.com/sales/leads/list", "requirementCurrency": "USD", "utmTerm": "sdf", "isNew": false, "companyBusinessType": "customer", "pipeline": 5963, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": "dsfsdfsdtest", "tenantId": 3440, "pipelineStage": 37302, "companyWebsite": "https://app-qa.sling-dev.com/sales/leads/list", "requirementBudget": 2342.0, "createdViaType": "Web", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": "AIRLINES_AVIATION"}, {"country": null, "updatedViaId": "7638", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "User", "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-13T11:13:29.096Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513632, "state": null, "dnd": null, "updatedAt": "2024-12-16T07:11:24.261Z", "createdViaId": "universal-app-key", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Universal API Key", "linkedIn": null, "latestActivityCreatedAt": "2024-12-16T07:11:24.262Z", "version": 6, "utmCampaign": null, "zipcode": null, "firstName": "tes 23t", "addressCoordinate": null, "expectedClosureOn": "2024-12-16T07:30:00.000Z", "companyZipcode": null, "name": "tes 23t tes 23t", "campaign": null, "utmMedium": null, "salutation": 387669, "designation": null, "lastName": "tes 23t", "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": "Pacific/Midway", "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": [], "importedBy": null, "emails": null, "updatedViaType": "Web", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": "OPEN", "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": false, "companyBusinessType": null, "pipeline": 5963, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": 37302, "companyWebsite": null, "requirementBudget": null, "createdViaType": "API Key", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": "WF_4735", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "Webhook and Marketplace action log", "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-16T07:07:51.367Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513635, "state": null, "dnd": null, "updatedAt": "2024-12-16T07:07:51.616Z", "createdViaId": "7638", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "User", "linkedIn": null, "latestActivityCreatedAt": "2024-12-16T07:07:51.617Z", "version": 1, "utmCampaign": null, "zipcode": null, "firstName": "test", "addressCoordinate": null, "expectedClosureOn": "2024-12-16T07:30:00.000Z", "companyZipcode": null, "name": "test test", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": "test", "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": "America/Los_Angeles", "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": [], "importedBy": null, "emails": null, "updatedViaType": "Workflow", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "Web", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": "WF_4735", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "Webhook and Marketplace action log", "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-13T11:11:31.868Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513631, "state": null, "dnd": null, "updatedAt": "2024-12-13T11:11:33.223Z", "createdViaId": "7638", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "User", "linkedIn": null, "latestActivityCreatedAt": "2024-12-13T11:11:33.225Z", "version": 1, "utmCampaign": null, "zipcode": null, "firstName": "test", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "test test", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": "test", "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": [], "importedBy": null, "emails": null, "updatedViaType": "Workflow", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "Web", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": "WF_4735", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "Webhook and Marketplace action log", "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-13T11:11:01.480Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 513630, "state": null, "dnd": null, "updatedAt": "2024-12-13T11:11:02.233Z", "createdViaId": "7638", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "User", "linkedIn": null, "latestActivityCreatedAt": "2024-12-13T11:11:02.241Z", "version": 1, "utmCampaign": null, "zipcode": null, "firstName": "test", "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "test test", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": "test", "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": [], "importedBy": null, "emails": null, "updatedViaType": "Workflow", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "Web", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": null, "source": null, "ownerId": 7638, "convertedAt": null, "createdAt": "2024-12-04T09:14:53.170Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 512893, "state": null, "dnd": null, "updatedAt": "2024-12-04T09:14:53.170Z", "createdViaId": "6c1126ad-08a2-4599-b545-53205717d11d", "companyCity": null, "taskDueOn": null, "utmSource": null, "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Phone Number With Default COuntry Code", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": null, "zipcode": null, "firstName": null, "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "", "campaign": null, "utmMedium": null, "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": null, "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": null, "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": null, "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "LCF", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": "IN", "updatedViaId": "7638", "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": "User", "utmContent": "utm-cont", "source": 387684, "ownerId": 8913, "convertedAt": null, "createdAt": "2024-12-03T10:16:34.489Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 512884, "state": null, "dnd": null, "updatedAt": "2024-12-03T12:26:38.765Z", "createdViaId": "universal-app-key", "companyCity": null, "taskDueOn": null, "utmSource": "utm-source", "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Universal API Key", "linkedIn": null, "latestActivityCreatedAt": "2024-12-04T11:24:00.114Z", "version": 7, "utmCampaign": "utm-cam", "zipcode": null, "firstName": null, "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "test email conversation", "campaign": 387681, "utmMedium": "utm-med", "salutation": null, "designation": null, "lastName": "test email conversation", "requirementName": null, "photoUrls": null, "subSource": "sub-source", "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": [{"code": "IN", "dialCode": "+91", "id": 215345, "type": "MOBILE", "value": "8308429939", "primary": true}], "products": [{"name": "wearplane 4", "tenantId": 3440, "id": 23902}], "importedBy": null, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}], "updatedViaType": "Web", "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": "utm-ter", "isNew": false, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "API Key", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}, {"country": null, "updatedViaId": null, "companyPhones": null, "companyName": null, "companyState": null, "updatedViaName": null, "utmContent": "Utm content", "source": 387683, "ownerId": 8724, "convertedAt": null, "createdAt": "2024-12-03T11:19:47.587Z", "score": 0.0, "twitter": null, "companyCountry": null, "id": 512892, "state": null, "dnd": null, "updatedAt": "2024-12-03T11:19:47.587Z", "createdViaId": "57f00aea-7888-4370-99fd-7893875ec12f", "companyCity": null, "taskDueOn": null, "utmSource": "Utm source", "updatedBy": 7638, "companyAnnualRevenue": null, "createdViaName": "Facebook Lead Integration", "linkedIn": null, "latestActivityCreatedAt": null, "version": 0, "utmCampaign": "Utm campaign", "zipcode": null, "firstName": null, "addressCoordinate": null, "expectedClosureOn": null, "companyZipcode": null, "name": "", "campaign": 387681, "utmMedium": "Utm medium", "salutation": null, "designation": null, "lastName": null, "requirementName": null, "photoUrls": null, "subSource": "Sub source", "city": null, "timezone": null, "companyEmployees": null, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}, "phoneNumbers": null, "products": [{"name": "1 BHK", "tenantId": 3440, "id": 23317}], "importedBy": null, "emails": null, "updatedViaType": null, "actualClosureDate": null, "department": null, "convertedBy": null, "address": null, "forecastingType": null, "facebook": null, "requirementCurrency": null, "utmTerm": "Utm term", "isNew": true, "companyBusinessType": null, "pipeline": null, "meetingScheduledOn": null, "deleted": false, "createdBy": 7638, "companyAddress": null, "tenantId": 3440, "pipelineStage": null, "companyWebsite": null, "requirementBudget": null, "createdViaType": "Marketplace", "pipelineStageReason": null, "companyAddressCoordinate": null, "companyIndustry": null}], "metaData": {"idNameStore": {"cfTestWorkflow": {}, "country": {}, "convertedBy": {}, "updatedBy": {"7638": "<PERSON><PERSON><PERSON>"}, "timezone": {}, "requirementCurrency": {}, "source": {"387684": "LinkedIn", "387685": "Exhibition", "387683": "Facebook"}, "ownerId": {"7990": "mayur p", "8011": "st dp", "8913": "karan sharma", "8724": "Addi user", "7638": "<PERSON><PERSON><PERSON>"}, "companyEmployees": {"387673": "5-9-updated", "387674": "10-19"}, "companyBusinessType": {}, "importedBy": {}, "pipeline": {"5963": "start", "5965": "kak"}, "cfMultiPickListTest": {"505369": "2"}, "createdBy": {"8011": "st dp", "7638": "<PERSON><PERSON><PERSON>"}, "cfPicklistFieldTest": {"505350": "Tuesday"}, "companyCountry": {}, "campaign": {"419408": "new-updated", "387681": "Organic"}, "salutation": {"387669": "Mr", "387671": "Miss-updated", "387670": "Mrs"}, "pipelineStage": {"37310": "Open", "37302": "Open"}, "companyIndustry": {}}}, "last": false, "totalPages": 247, "totalElements": 20, "sort": [{"direction": "DESC", "property": "updatedAt", "ignoreCase": false, "nullHandling": "NATIVE", "descending": true, "ascending": false}], "first": true, "numberOfElements": 20, "size": 20, "number": 0}