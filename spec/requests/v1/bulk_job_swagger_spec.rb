require 'swagger_helper'

RSpec.describe 'Bulk Job API', type: :request do
  let(:user){ Tenant.create(id: 99) ; User.create(id: 1, tenant_id: 99, name: '<PERSON>')}
  let(:Authorization) {get_test_jwt(user.id, user.tenant_id)}
  let(:tenant_data) { file_fixture('tenant-data.json').read }
  let(:job_params) {
    {
      entity: LEAD,
      operation: UPDATE_OPERATION,
      filters: {
        "jsonRule": {
          "rules": [
            {
              "operator": "equal",
              "id": "ownerId",
              "field": "ownerId",
              "type": "long",
              "value": 11
            }
          ],
          "condition": "AND",
          "valid": true
        }
      },
      executeWorkflow: true,
      executeScoreRule: true,
      payload: { ownerId: 11 }
    }.with_indifferent_access
  }

  let(:page) { '1' }
  let(:size) { '2' }
  let(:sort) {'updatedAt,desc' }

  path '/v1/bulk-action-jobs' do
    post 'Creates Bulk Job' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :body
      parameter name: :job_params, in: :body, schema: {
        type: :object,
        required: %w[entity operation filters],
        properties: {
          entity: {
            type: :string
          },
          operation: {
            type: :string
          },
          filters: {
            type: :object
          },
          payload: {
            type: :object
          },
        }
      }
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      before do
        Thread.current[:user] = user
        Thread.current[:token] = send(:Authorization)
        stub_request(:get, SERVICE_IAM + "/v1/tenants").
          with(
            headers: {
              'Accept' => 'application/json',
              'Authorization' => "Bearer #{Thread.current[:token]}"
            }
          ).
          to_return(status: 200, body: tenant_data, headers: {})
        allow(EntityAction::Fetch).to receive_message_chain(:new, :call).and_return(true)
      end
      response '201', 'Bulk Job created' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs' do
    get 'List Bulk Jobs' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string
      parameter name: :sort, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Bulk Jobs List' do
        schema type: :object,
          properties: {
          content:{
            type: :array,
            properties:{
              id: {type: :string},
              numberOfRecords: {type: :number},
              operation: {type: :string},
              entity: {type: :string},
              status: {type: :string},
              submittedBy: {type: :hash},
              createdAt: {type: :string},
              submitedAt: {type: :string}
            }

          },
          totalElements: {type: :number},
          totalPages: {type: :number},
          last: {type: :boolean},
          numberOfElements: {type: :number},
          first: {type: :boolean},
          size: {type: :number},
          number: {type: :number}
        }
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/search' do
    post 'List Bulk Jobs' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string
      parameter name: :sort, in: :query, type: :string
      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Bulk Jobs List' do
        let(:jsonRule) do
          {
            jsonRule: {
              condition: 'AND',
              rules: [
                {
                  "operator": 'equal',
                  "id": 'status',
                  "field": 'status',
                  "type": 'string',
                  "value": 'COMPLETED'
                }
              ]
            }
          }
        end

        schema type: :object,
          properties: {
          content:{
            type: :array,
            properties:{
              id: {type: :string},
              numberOfRecords: {type: :number},
              operation: {type: :string},
              entity: {type: :string},
              status: {type: :string},
              submittedBy: {type: :hash},
              createdAt: {type: :string},
              submitedAt: {type: :string}
            }

          },
          totalElements: {type: :number},
          totalPages: {type: :number},
          last: {type: :boolean},
          numberOfElements: {type: :number},
          first: {type: :boolean},
          size: {type: :number},
          number: {type: :number}
        }
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:jsonRule) do
          {
            jsonRule: {
              condition: 'AND',
              rules: [
                {
                  "operator": 'equal',
                  "id": 'status',
                  "field": 'status',
                  "type": 'string',
                  "value": 'COMPLETED'
                }
              ]
            }
          }
        end

        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/{id}/summary-file' do
    get 'Returns url of summary file' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      let(:bulk_job){ create(:bulk_job, user: user)}
      let!(:summary_file) { create(:summary_file, bulk_job: bulk_job, file_type: 'success')}
      let(:id) { bulk_job.id }
      parameter name: :id, in: :path, type: :string

      before do
        allow(GetPresignedS3Url).to receive(:remote_url).and_return("http//www.aws.com/test.csv")
      end

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Summary file' do
        schema type: :object,
        properties: {
          url: {type: :string}
        }
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/{id}/error-file' do
    get 'Returns url of error file' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      let(:bulk_job){ create(:bulk_job, user: user)}
      let!(:summary_file) { create(:summary_file, bulk_job: bulk_job, file_type: 'error')}
      let(:id) { bulk_job.id }
      parameter name: :id, in: :path, type: :string

      before do
        allow(GetPresignedS3Url).to receive(:remote_url).and_return("http//www.aws.com/test.csv")
      end

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Summary file' do
        schema type: :object,
        properties: {
          url: {type: :string}
        }
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/{id}/attachment' do
    get 'Returns url of attachment file' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      let(:bulk_job){ create(:bulk_job, user: user, operation: WHATSAPP_MESSAGE_OPERATION, payload: {
        'whatsappTemplate' => {
          'dynamicTemplateMediaName' => 'test.jpg'
        }
      })}
      let(:id) { bulk_job.id }
      parameter name: :id, in: :path, type: :string

      before do
        allow(GetPresignedS3Url).to receive(:remote_url).and_return("http//www.aws.com/test.jpg")
      end

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Attachment file' do
        schema type: :object,
        properties: {
          url: {type: :string}
        }
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end

      response '404', 'Attachment not found' do
        let(:bulk_job) { create(:bulk_job, user: user, operation: EMAIL_OPERATION) }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/{id}/abort' do
    post 'Aborts the bulk job' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      let(:bulk_job){ create(:bulk_job, user: user, status: 'queued')}
      let(:id) { bulk_job.id }
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Bulk Job aborted' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/{id}/pause' do
    post 'Pauses the bulk job' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      let(:bulk_job){ create(:bulk_job, user: user, status: 'queued')}
      let(:id) { bulk_job.id }
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Bulk Job Paused' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/{id}/resume' do
    post 'Resumes the bulk job' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      let(:bulk_job){ create(:bulk_job, user: user, status: 'paused')}
      let(:id) { bulk_job.id }
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Bulk Job Resumed' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end

  path '/v1/bulk-action-jobs/{id}' do
    delete 'Deletes the bulk job' do
      tags 'Bulk Job'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      let(:bulk_job){ create(:bulk_job, user: user, status: 'paused', tenant_id: user.tenant_id)}
      let(:id) { bulk_job.id }
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Bulk Job Deleted' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { invalid_jwt }
        run_test!
      end
    end
  end
end
