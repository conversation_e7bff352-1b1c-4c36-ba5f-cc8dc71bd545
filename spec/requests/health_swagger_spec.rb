# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Health API', type: :request do
  path '/v0e8136370948463e/bulk-action-jobs/health' do
    let(:user) { create(:user) }

    before do
      expect(ENV).to receive(:[]).with('TENANT_ID').and_return(user.tenant_id)
    end

    get 'Bulk job from database' do
      tags 'Bulk job'

      response '200', 'Database is up' do
        before { create(:bulk_job, tenant_id: user.tenant_id) }

        run_test!
      end

      response '404', 'Entity not present' do
        run_test!
      end

      response '503', 'Database is down' do
        before { expect(BulkJob).to receive(:find_by).and_raise(PG::ConnectionBad) }

        run_test!
      end
    end
  end
end
