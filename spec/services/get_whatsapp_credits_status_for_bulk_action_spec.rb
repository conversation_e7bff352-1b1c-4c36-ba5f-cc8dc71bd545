# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetWhatsappCreditsStatusForBulkAction do
  describe "#call" do
    let(:user) { User.create(id: 1, name: '<PERSON>', tenant: Tenant.create(id: 99)) }
    before do
      Thread.current[:user] = user
      Thread.current[:token] = get_test_jwt(user.id, user.tenant_id)
      user.update(token: Thread.current[:token])
    end

    context 'with valid input' do
      before do
        stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-credits/status").with(
          headers: {
            'Authorization' => "Bearer #{ user.token }"
          }
        ).to_return(
          status: 200,
          body: {
            "creditsAvailableForBulkStatus": true
          }.to_json,
          headers: {}
        )
      end

      it 'returns true' do
        result = GetWhatsappCreditsStatusForBulkAction.new(user.token).call
        expect(result['creditsAvailableForBulkStatus']).to eq(true)
      end
    end

    context 'when token is not present in thread' do
      before do
        Thread.current[:token] = nil
        user.update(token: nil)
      end

      it 'raises unauthorized error' do
        expect { GetWhatsappCreditsStatusForBulkAction.new(user.token).call }.to raise_error(ExceptionHandler::AuthenticationError, '025002')
      end
    end

    context 'when something went wrong' do
      before do
        stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-credits/status").with(
          headers: {
            'Authorization' => "Bearer #{ user.token }"
          }
        ).to_return(
          status: 500,
          body: {}.to_json,
          headers: {}
        )
      end

      it 'raises invalid data error' do
        expect { GetWhatsappCreditsStatusForBulkAction.new(user.token).call }.to raise_error(ExceptionHandler::InternalServerError, '025004')
      end
    end
  end
end
