require 'rails_helper'

RSpec.describe EntityAction::Delete do

  describe "#perform" do
    let(:tenant){ create(:tenant) }
    let(:user){ create(:user, name: '<PERSON>', token: 'some-token', tenant: tenant) }
    let(:bulk_job){ create(:bulk_job, user_id: user.id, tenant_id: tenant.id, status: 'queued') }
    let(:deal_bulk_job){ create(:bulk_job, entity: 'deal', user_id: user.id, tenant_id: tenant.id, status: 'queued')}

    before do
      allow_any_instance_of(Sidekiq::Queue).to receive(:count).and_return(0)
    end

    context 'with successful lead deletion' do

      before(:each) do
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3)
        [1,2].each do |id|
          stub_request(:delete, "http://localhost:8086/v1/leads/#{id}?publishUsage=false").
            with(
              headers: {
                'authorization'=> 'Bearer some-token'
              }).
              to_return(status: 200)
        end
        stub_request(:delete, "http://localhost:8086/v1/leads/3?publishUsage=true").
          with(
            headers: {
              'authorization'=> 'Bearer some-token'
            }).
            to_return(status: 200)
        allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records' do
        EntityAction::Delete.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(bulk_job.successful).to be(3)
        expect(bulk_job.failure).to be(0)
      end
    end

    context 'with successful deal deletion' do

      before(:each) do
        create(:record, bulk_job: deal_bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: deal_bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: deal_bulk_job, tenant_id: tenant.id, entity_id: 3)
        [1, 2].each do |id|
          stub_request(:delete, "http://localhost:8090/v1/deals/#{id}?publishUsage=false").
            with(
              headers: {
                'authorization'=> 'Bearer some-token'
              }).
              to_return(status: 200)
        end
        stub_request(:delete, "http://localhost:8090/v1/deals/3?publishUsage=true").
        with(
          headers: {
            'authorization'=> 'Bearer some-token'
          }).
          to_return(status: 200)

        allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records' do
        EntityAction::Delete.new(deal_bulk_job).perform
        expect(deal_bulk_job.reload.status).to eq('completed')
        expect(deal_bulk_job.completed_at).to be < (Time.now)
        expect(deal_bulk_job.started_at).to be < (deal_bulk_job.completed_at)
        expect(deal_bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(deal_bulk_job.successful).to be(3)
        expect(deal_bulk_job.failure).to be(0)
      end
    end

    context 'with successful task deletion' do
      before(:each) do
        bulk_job.update(entity: 'task')
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3)
        [1, 2].each do |id|
          stub_request(:delete, "http://localhost:8087/v1/tasks/#{id}?publishUsage=false")
            .with(
              headers: { 'authorization' => 'Bearer some-token' }
            ).to_return(status: 200)
        end
        stub_request(:delete, "http://localhost:8087/v1/tasks/3?publishUsage=true")
        .with(
          headers: { 'authorization' => 'Bearer some-token' }
        ).to_return(status: 200)

        allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records' do
        EntityAction::Delete.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(bulk_job.successful).to be(3)
        expect(bulk_job.failure).to be(0)
      end
    end

    context 'with successful call_log deletion' do
      before(:each) do
        bulk_job.update(entity: 'call', operation: 'delete')
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3)
        stub_request(:delete, "http://localhost:3000/v1/call-logs/1?publishUsage=false")
        .with(
          headers: { 'authorization' => 'Bearer some-token' }
        ).to_return(status: 404, body: {
          "errorCode": "02002001",
          "message": "Call log not found.",
          "timestamp": "2023-03-31T07:32:36.007Z"
        }.to_json)

        stub_request(:delete, "http://localhost:3000/v1/call-logs/2?publishUsage=false")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

        stub_request(:delete, "http://localhost:3000/v1/call-logs/3?publishUsage=true")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

          allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records and add proper error message on record if failed' do
        EntityAction::Delete.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.map(&:status)).to eq(%w[failed succeeded succeeded])
        expect(bulk_job.successful).to be(2)
        expect(bulk_job.failure).to be(1)
        expect(Record.find_by(bulk_job_id: bulk_job.id, status: 'failed').error_message).to eq('Call log not found.')
      end
    end

    context 'with successful company deletion' do
      before(:each) do
        bulk_job.update(entity: 'company', operation: 'delete')
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3)
        stub_request(:delete, "http://localhost:3000/v1/companies/1?publishUsage=false")
        .with(
          headers: { 'authorization' => 'Bearer some-token' }
        ).to_return(status: 404, body: {
          "errorCode": "02801025"
        }.to_json)

        stub_request(:delete, "http://localhost:3000/v1/companies/2?publishUsage=false")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

        stub_request(:delete, "http://localhost:3000/v1/companies/3?publishUsage=true")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

          allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records and add proper error message on record if failed' do
        EntityAction::Delete.new(bulk_job.reload).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.map(&:status)).to eq(%w[failed succeeded succeeded])
        expect(bulk_job.successful).to be(2)
        expect(bulk_job.failure).to be(1)
      end
    end

    context 'with successful meeting deletion' do
      before(:each) do
        bulk_job.update(entity: 'meeting', operation: 'delete')
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3)
        stub_request(:delete, "http://localhost:3000/v1/meetings/1?publishUsage=false")
        .with(
          headers: { 'authorization' => 'Bearer some-token' }
        ).to_return(status: 404, body: {
          "errorCode": "02801025"
        }.to_json)

        stub_request(:delete, "http://localhost:3000/v1/meetings/2?publishUsage=false")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

        stub_request(:delete, "http://localhost:3000/v1/meetings/3?publishUsage=true")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

          allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records and add proper error message on record if failed' do
        EntityAction::Delete.new(bulk_job.reload).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.map(&:status)).to eq(%w[failed succeeded succeeded])
        expect(bulk_job.successful).to be(2)
        expect(bulk_job.failure).to be(1)
      end
    end

    context 'with successful email deletion' do
      before(:each) do
        bulk_job.update(entity: 'email', operation: 'delete')
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3)
        stub_request(:delete, "http://localhost:3000/v1/email-threads/1?publishUsage=false")
        .with(
          headers: { 'authorization' => 'Bearer some-token' }
        ).to_return(status: 404, body: {
          "errorCode": "01602001",
          "message": "email not found.",
          "timestamp": "2023-03-31T07:32:36.007Z"
        }.to_json)

        stub_request(:delete, "http://localhost:3000/v1/email-threads/2?publishUsage=false")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

        stub_request(:delete, "http://localhost:3000/v1/email-threads/3?publishUsage=true")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)

          allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records and add proper error message on record if failed' do
        EntityAction::Delete.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.map(&:status)).to eq(%w[failed succeeded succeeded])
        expect(bulk_job.successful).to be(2)
        expect(bulk_job.failure).to be(1)
        expect(Record.find_by(bulk_job_id: bulk_job.id, status: 'failed').error_message).to eq('email not found.')
      end
    end

    context 'with successful note deletion' do
      def stub_note_delete(note_id, publish_usage)
        stub_request(:delete, "http://localhost:8087/v1/notes/#{note_id}/deleteNoteWithAllRelations?publishUsage=#{publish_usage}")
          .with(
            headers: { 'authorization' => 'Bearer some-token' }
          ).to_return(status: 200)
      end

      before(:each) do
        bulk_job.update(entity: 'note', operation: 'delete')
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1, parent_entity_service: 'productivity', relations: { lead_id: 1 })
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2, parent_entity_service: 'productivity', relations: { deal_id: 1 })
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3, parent_entity_service: 'productivity', relations: { contact_id: 1 })
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 4, parent_entity_service: 'productivity', relations: { task_id: 1 })
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 5, parent_entity_service: 'call', relations: { call_id: 1 })
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 6, parent_entity_service: 'meeting', relations: { meeting_id: 1 })
        
        stub_request(:delete, "http://localhost:8087/v1/notes/1/deleteNoteWithAllRelations?publishUsage=false")
        .with(
          headers: { 'authorization' => 'Bearer some-token' }
        ).to_return(status: 404, body: {
          "errorCode": "01602001",
          "message": "note not found.",
          "timestamp": "2023-03-31T07:32:36.007Z"
        }.to_json)

        stub_note_delete(2, false)
        stub_note_delete(3, false)
        stub_note_delete(4, false)
        stub_note_delete(5, false)
        stub_note_delete(6, true)

        allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records and add proper error message on record if failed' do
        EntityAction::Delete.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.map(&:status)).to eq(%w[failed succeeded succeeded succeeded succeeded succeeded])
        expect(bulk_job.successful).to be(5)
        expect(bulk_job.failure).to be(1)
        expect(Record.find_by(bulk_job_id: bulk_job.id, status: 'failed').error_message).to eq('note not found.')
      end
    end
    
    context 'with failed record operation' do
      before(:each) do
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 3)
        stub_request(:delete, "http://localhost:8086/v1/leads/1?publishUsage=false").
          with(
            headers: {
              'authorization'=> 'Bearer some-token'
            }).
            to_return(status: 200)

        stub_request(:delete, "http://localhost:8086/v1/leads/3?publishUsage=true").
          with(
            headers: {
              'authorization'=> 'Bearer some-token'
            }).
            to_return(status: 200)


        stub_request(:delete, "http://localhost:8086/v1/leads/2?publishUsage=false").
          with(
            headers: {
              'authorization'=> 'Bearer some-token'
            }).
            to_return(status: 404, body: {
              "code": "000010",
              "message": "Uhoh! Resource doesnt seem to exists or you dont have enough permissions to access it.",
              "errorDetails": []
            }.to_json, headers: {})
            allow(FileUploader).to receive(:upload).and_return(nil)
      end

      it 'should delete the records' do
        EntityAction::Delete.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be_present
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.where(entity_id: [1,3]).map(&:status).all?('succeeded')).to be_truthy
        err_record = bulk_job.records.where(entity_id: 2).first
        expect(err_record.status).to eq('failed')
        expect(err_record.error_message).to eq('Uhoh! Resource doesnt seem to exists or you dont have enough permissions to access it.')
        expect(bulk_job.successful).to be(2)
        expect(bulk_job.failure).to be(1)
      end
    end

    context 'with completed or aborted job' do
      context 'when entity is other than note' do
        before(:each) do
          create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1)
          stub_request(:delete, "http://localhost:8086/v1/leads/1?publishUsage=false").
            with(
              headers: {
                'authorization'=> 'Bearer some-token'
              }).
              to_return(status: 200)
        end

        it 'should not delete records if job completed' do
          bulk_job.completed!
          EntityAction::Delete.new(bulk_job).perform
          expect(WebMock).not_to have_requested(:delete, "http://localhost:8086/v1/leads/1?publishUsage=false")
        end

        it 'should not delete records if job aborted' do
          bulk_job.aborted!
          EntityAction::Delete.new(bulk_job).perform
          expect(WebMock).not_to have_requested(:delete, "http://localhost:8086/v1/leads/1?publishUsage=false")
        end
      end

      context 'when entity is note' do
        let(:tenant){ create(:tenant) }
        before(:each) do
          bulk_job.update(entity: 'note', operation: 'delete')
          create(:record, bulk_job: bulk_job, tenant_id: tenant.id, entity_id: 1, parent_entity_service: 'productivity', relations: { lead_id: 1 })
        end

        it 'should not delete records if job is completed' do
          bulk_job.completed!
          expect(EntityAction::Delete.new(bulk_job).perform).to be nil
          expect(WebMock).not_to have_requested(:delete, "http://localhost:8086/v1/notes/1/deleteNoteWithAllRelations?publishUsage=true")
        end

        it 'should not delete records if job is aborted' do
          bulk_job.aborted!
          expect(EntityAction::Delete.new(bulk_job).perform).to be nil
          expect(WebMock).not_to have_requested(:delete, "http://localhost:8086/v1/notes/1/deleteNoteWithAllRelations?publishUsage=true")
        end

        it 'should not delete records if job paused' do
          bulk_job.paused!
          expect(EntityAction::Delete.new(bulk_job).perform).to eq({ paused: true })
          expect(WebMock).not_to have_requested(:delete, "http://localhost:8086/v1/leads/1/deleteNoteWithAllRelations?publishUsage=true")
        end
      end
    end
  end
end
