# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe EntityAction::Email do
  describe '#perform' do
    before { allow(FileUploader).to receive(:upload).and_return(nil) }

    def stub_lead(entity_id, token, emails: true, email_permission: true)
      lead_response = JSON.parse(file_fixture('sample-lead.json').read)
      lead_response.delete('emails') if emails.blank?
      lead_response['recordActions'].merge!('email' => email_permission)
      stub_request(:get, "#{SERVICE_SALES}/v1/leads/#{entity_id}")
        .with(
          headers: {
            Authorization: "Bearer #{token}"
          }
        )
        .to_return(status: 200, body: lead_response.to_json)
    end

    def stub_contact(entity_id, token, emails: true, email_permission: true)
      contact_response = JSON.parse(file_fixture('sample-contact.json').read)
      contact_response.delete('emails') if emails.blank?
      contact_response['recordActions'].merge!('email' => email_permission)
      stub_request(:get, "#{SERVICE_SALES}/v1/contacts/#{entity_id}")
        .with(
          headers: {
            Authorization: "Bearer #{token}"
          }
        )
        .to_return(status: 200, body: contact_response.to_json)
    end

    let(:payload) {
      {
        "subject":"Test subject",
        "body":"Test Email Body",
        "emailTemplateId":"123",
        "cc":[
          {
            "entity":"user",
            "id":"1",
            "name":"User Name",
            "email":"<EMAIL>"
          }
        ],
        "bcc":[
          {
            "entity":"user",
            "id":"2",
            "name":"Other User Name",
            "email":"<EMAIL>"
          }
        ],
        "attachments":[
          {
            "id":nil,
            "data": nil,
            "fileName":"somefilename.pdf"
          },
          {
            "id": nil,
            "data": nil,
            "fileName":"anotherFileName.json"
          }
        ]
      }
    }

    [LEAD, CONTACT].each do |entity|
      context "with successful #{entity} email operation" do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: entity) }

        before do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)

          send("stub_#{entity}", 1, bulk_job.user.token)
          send("stub_#{entity}", 2, bulk_job.user.token)
          send("stub_#{entity}", 3, bulk_job.user.token)
          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')
          stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
            .with(
              headers: {
                Authorization: "Bearer some-token"
              }
              # TODO: WebMock does not support matching body for multipart/form-data requests yet :(
              # body: payload.merge(
              #   attachments: payload[:attachments].map { |attachment| attachment.merge(data: instance_of(File)) },
              #   to: [{ entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }],
              #   relatedTo: { entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }
              # )
            )
            .to_return(status: 200, body: '')
        end

        context 'with attachments' do
          it 'should send email' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).to be_blank
            expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
            expect(bulk_job.successful).to be(3)
            expect(bulk_job.failure).to be(0)
          end
        end

        context 'when job is aborted' do
          it 'should stop sending emails' do
            allow(bulk_job).to receive(:aborting?).and_return(false, true)

            payload = bulk_job.payload
            payload.delete('attachments')
            bulk_job.update!(payload: payload)
            described_class.new(bulk_job).perform
            expect(bulk_job.records.pluck(:status)).to match_array(%w[queued queued succeeded])
            expect(bulk_job.reload.status).to eq('aborted')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).to be_blank
            expect(bulk_job.successful).to be(1)
            expect(bulk_job.failure).to be(0)
          end
        end

        context 'when job is paused' do
          before { bulk_job.paused! }

          it 'should stop sending emails' do
            payload = bulk_job.payload
            payload.delete('attachments')
            bulk_job.update!(payload: payload)
            response = described_class.new(bulk_job).perform
            expect(bulk_job.records.pluck(:status)).to match(%w[queued queued queued])
            expect(response).to eq({ paused: true })
          end
        end

        context 'without attachments' do
          it 'should send email' do
            payload = bulk_job.payload
            payload.delete('attachments')
            bulk_job.update!(payload: payload)
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).to be_blank
            expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
            expect(bulk_job.successful).to be(3)
            expect(bulk_job.failure).to be(0)
          end
        end
      end

      context 'with failed record operation' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: entity) }

        before(:each) do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)
          send("stub_#{entity}", 1, bulk_job.user.token)
          send("stub_#{entity}", 2, bulk_job.user.token)
          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')
        end

        context "when primary email not found for #{entity}" do
          before do
            send("stub_#{entity}", 3, bulk_job.user.token, emails: false)
            stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
                # TODO: WebMock does not support matching body for multipart/form-data requests yet :(
                # body: payload.merge(
                #   attachments: payload[:attachments].map { |attachment| attachment.merge(data: instance_of(File)) },
                #   to: [{ entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }],
                #   relatedTo: { entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }
                # )
              )
              .to_return(status: 200, body: '')
          end

          it 'should send email for successful and log error for failure' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.records.where(entity_id: [1, 2]).map(&:status).all?('succeeded')).to be_truthy
            err_record = bulk_job.records.where(entity_id: 3).first
            expect(err_record.status).to eq('failed')
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).not_to be_blank
            expect(err_record.error_message).to eq('No email available')
            expect(bulk_job.successful).to be(2)
            expect(bulk_job.failure).to be(1)
          end
        end

        context 'when email permission is false on entity' do
          before do
            send("stub_#{entity}", 3, bulk_job.user.token, email_permission: false)
            stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
                # TODO: WebMock does not support matching body for multipart/form-data requests yet :(
                # body: payload.merge(
                #   attachments: payload[:attachments].map { |attachment| attachment.merge(data: instance_of(File)) },
                #   to: [{ entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }],
                #   relatedTo: { entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }
                # )
              )
              .to_return(status: 200, body: '')
          end

          it 'should send email for successful and log error for failure' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.records.where(entity_id: [1, 2]).map(&:status).all?('succeeded')).to be_truthy
            err_record = bulk_job.records.where(entity_id: 3).first
            expect(err_record.status).to eq('failed')
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).not_to be_blank
            expect(err_record.error_message).to eq("Uhoh! You don't have enough permissions to send email.")
            expect(bulk_job.successful).to be(2)
            expect(bulk_job.failure).to be(1)
          end
        end

        context 'when email request fails' do
          context 'when connected account not found' do
            before do
              send("stub_#{entity}", 3, bulk_job.user.token)
              stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email").to_return(status: 404, body: { errorCode: '********' }.to_json)
            end

            it 'should pause the bulk job' do
              described_class.new(bulk_job).perform
              expect(bulk_job.reload.status).to eq('paused')
              expect(bulk_job.summary_file).to be_blank
              expect(bulk_job.error_summary_file).to be_blank
              expect(bulk_job.payload['errorMessage']).to eq("Your email account seems to be disconnected. Please connect again. If the issue persists, please contact our support team.")
            end
          end

          context 'when email template read permission is not present' do
            before do
              send("stub_#{entity}", 3, bulk_job.user.token)
              stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email").to_return(status: 404, body: { errorCode: '********' }.to_json)
              expect_any_instance_of(described_class).to receive(:abort_job?).exactly(1).times.and_return(true)
            end

            it 'should send email for successfuly, log error for failure & abort consequent processing' do
              described_class.new(bulk_job).perform
              expect(bulk_job.reload.status).to eq('completed')
              expect(bulk_job.completed_at).to be < (Time.now)
              expect(bulk_job.started_at).to be < (bulk_job.completed_at)
              expect(bulk_job.records.map(&:status).all?('failed')).to be_truthy
              expect(bulk_job.summary_file).to be_blank
              expect(bulk_job.error_summary_file).not_to be_blank
              expect(bulk_job.records.map(&:error_message).uniq).to match_array(["Uhoh! You do not have the necessary permission to view Email Templates"])
              expect(bulk_job.successful).to be(0)
              expect(bulk_job.failure).to be(3)
            end
          end

          context 'when unmapped error occurs' do
            before do
              send("stub_#{entity}", 3, bulk_job.user.token)
              stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email").to_return(status: 400, body: { errorCode: '********' }.to_json)
              expect_any_instance_of(described_class).to receive(:abort_job?).with('********').exactly(3).times.and_return(false)
            end

            it 'should send email for successfuly and log error for failure' do
              described_class.new(bulk_job).perform
              expect(bulk_job.reload.status).to eq('completed')
              expect(bulk_job.completed_at).to be < (Time.now)
              expect(bulk_job.started_at).to be < (bulk_job.completed_at)
              expect(bulk_job.records.map(&:status).all?('failed')).to be_truthy
              expect(bulk_job.summary_file).to be_blank
              expect(bulk_job.error_summary_file).not_to be_blank
              expect(bulk_job.records.map(&:error_message).uniq).to match_array(["400 Bad Request"])
              expect(bulk_job.successful).to be(0)
              expect(bulk_job.failure).to be(3)
            end
          end
        end
      end

      context 'with error like no email or no email permission' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: entity) }

        before do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)

          send("stub_#{entity}", 1, bulk_job.user.token, emails: false)
          send("stub_#{entity}", 2, bulk_job.user.token, emails: false)
          send("stub_#{entity}", 3, bulk_job.user.token, email_permission: false)
          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')
        end

        it 'should not call sleep method & mark record failed' do
          expect_any_instance_of(Kernel).not_to receive(:sleep).with(instance_of(Integer))
          described_class.new(bulk_job).perform
          expect(bulk_job.reload.status).to eq('completed')
          expect(bulk_job.completed_at).to be < (Time.now)
          expect(bulk_job.started_at).to be < (bulk_job.completed_at)
          expect(bulk_job.summary_file).to be_blank
          expect(bulk_job.error_summary_file).not_to be_blank
          expect(bulk_job.records.map(&:status).all?('failed')).to be_truthy
          expect(bulk_job.successful).to be(0)
          expect(bulk_job.failure).to be(3)
        end
      end

      context 'with completed or aborted job' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: entity) }

        before(:each) do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
        end

        it 'should should not update records if job completed' do
          bulk_job.completed!
          described_class.new(bulk_job).perform
          expect_any_instance_of(described_class).not_to receive(:execute_api_call)
        end

        it 'should should not update records if job aborted' do
          bulk_job.aborted!
          described_class.new(bulk_job).perform
          expect_any_instance_of(described_class).not_to receive(:execute_api_call)
        end
      end
    end

    context 'when entity is deal' do
      context "with successful deal email operation" do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: DEAL) }

        before do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1, parent_entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2, parent_entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3, parent_entity_id: 3)

          stub_contact(1, bulk_job.user.token)
          stub_contact(2, bulk_job.user.token)
          stub_contact(3, bulk_job.user.token)
          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')
          stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
            .with(
              headers: {
                Authorization: "Bearer some-token"
              }
              # TODO: WebMock does not support matching body for multipart/form-data requests yet :(
              # body: payload.merge(
              #   attachments: payload[:attachments].map { |attachment| attachment.merge(data: instance_of(File)) },
              #   to: [{ entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }],
              #   relatedTo: { entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }
              # )
            )
            .to_return(status: 200, body: '')
        end

        context 'with attachments' do
          it 'should send email' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).to be_blank
            expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
            expect(bulk_job.successful).to be(3)
            expect(bulk_job.failure).to be(0)
          end
        end

        context 'without attachments' do
          it 'should send email' do
            payload = bulk_job.payload
            payload.delete('attachments')
            bulk_job.update!(payload: payload)
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).to be_blank
            expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
            expect(bulk_job.successful).to be(3)
            expect(bulk_job.failure).to be(0)
          end
        end
      end

      context 'with failed record operation' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: DEAL) }

        before(:each) do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1, parent_entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2, parent_entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3, parent_entity_id: 3)
          stub_contact(1, bulk_job.user.token)
          stub_contact(2, bulk_job.user.token)
          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')
        end

        context "when primary email not found for contact" do
          before do
            stub_contact(3, bulk_job.user.token, emails: false)
            stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
                # TODO: WebMock does not support matching body for multipart/form-data requests yet :(
                # body: payload.merge(
                #   attachments: payload[:attachments].map { |attachment| attachment.merge(data: instance_of(File)) },
                #   to: [{ entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }],
                #   relatedTo: { entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }
                # )
              )
              .to_return(status: 200, body: '')
          end

          it 'should send email for successful and log error for failure' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.records.where(entity_id: [1, 2]).map(&:status).all?('succeeded')).to be_truthy
            err_record = bulk_job.records.where(entity_id: 3).first
            expect(err_record.status).to eq('failed')
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).not_to be_blank
            expect(err_record.error_message).to eq('No email available')
            expect(bulk_job.successful).to be(2)
            expect(bulk_job.failure).to be(1)
          end
        end

        context 'when email permission is false on entity' do
          before do
            stub_contact(3, bulk_job.user.token, email_permission: false)
            stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
                # TODO: WebMock does not support matching body for multipart/form-data requests yet :(
                # body: payload.merge(
                #   attachments: payload[:attachments].map { |attachment| attachment.merge(data: instance_of(File)) },
                #   to: [{ entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }],
                #   relatedTo: { entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }
                # )
              )
              .to_return(status: 200, body: '')
          end

          it 'should send email for successful and log error for failure' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.records.where(entity_id: [1, 2]).map(&:status).all?('succeeded')).to be_truthy
            err_record = bulk_job.records.where(entity_id: 3).first
            expect(err_record.status).to eq('failed')
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).not_to be_blank
            expect(err_record.error_message).to eq("Uhoh! You don't have enough permissions to send email.")
            expect(bulk_job.successful).to be(2)
            expect(bulk_job.failure).to be(1)
          end
        end

        context 'when email request fails' do
          context 'when connected account not found' do
            before do
              stub_contact(3, bulk_job.user.token)
              stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email").to_return(status: 404, body: { errorCode: '********' }.to_json)
            end

            it 'should pause the job' do
              described_class.new(bulk_job).perform
              expect(bulk_job.reload.status).to eq('paused')
              expect(bulk_job.summary_file).to be_blank
              expect(bulk_job.error_summary_file).to be_blank
              expect(bulk_job.payload['errorMessage']).to eq("Your email account seems to be disconnected. Please connect again. If the issue persists, please contact our support team.")
            end
          end

          context 'when unmapped error occurs' do
            before do
              stub_contact(3, bulk_job.user.token)
              stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email").to_return(status: 400, body: { errorCode: '********' }.to_json)
              expect_any_instance_of(described_class).to receive(:abort_job?).with('********').exactly(3).times.and_return(false)
            end

            it 'should send email for successfuly and log error for failure' do
              described_class.new(bulk_job).perform
              expect(bulk_job.reload.status).to eq('completed')
              expect(bulk_job.completed_at).to be < (Time.now)
              expect(bulk_job.started_at).to be < (bulk_job.completed_at)
              expect(bulk_job.records.map(&:status).all?('failed')).to be_truthy
              expect(bulk_job.summary_file).to be_blank
              expect(bulk_job.error_summary_file).not_to be_blank
              expect(bulk_job.records.map(&:error_message).uniq).to match_array(["400 Bad Request"])
              expect(bulk_job.successful).to be(0)
              expect(bulk_job.failure).to be(3)
            end
          end
        end

        context 'when no associated contact on deal' do
          before do
            Record.find_by(bulk_job_id: bulk_job.id, entity_id: 3).update!(entity_id: 0)
            stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
                # TODO: WebMock does not support matching body for multipart/form-data requests yet :(
                # body: payload.merge(
                #   attachments: payload[:attachments].map { |attachment| attachment.merge(data: instance_of(File)) },
                #   to: [{ entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }],
                #   relatedTo: { entity: 'lead', id: entity_id, name: 'Lead        with all custom        fields', email: '<EMAIL>' }
                # )
              )
              .to_return(status: 200, body: '')
          end

          it 'should send email for successful and log error for failure' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.records.where(entity_id: [1, 2]).map(&:status).all?('succeeded')).to be_truthy
            err_record = bulk_job.records.where(status: 'failed').first
            expect(err_record.status).to eq('failed')
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).not_to be_blank
            expect(err_record.error_message).to eq("Uhoh! No Contact associated with Deal")
            expect(bulk_job.successful).to be(2)
            expect(bulk_job.failure).to be(1)
          end
        end
      end

      context 'with error like no email or no email permission or no associated contact' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: DEAL) }

        before do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1, parent_entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 0, parent_entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3, parent_entity_id: 3)

          stub_contact(1, bulk_job.user.token, emails: false)
          stub_contact(3, bulk_job.user.token, email_permission: false)
          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')
        end

        it 'should not call sleep method & mark record failed' do
          expect_any_instance_of(Kernel).not_to receive(:sleep).with(instance_of(Integer))
          described_class.new(bulk_job).perform
          expect(bulk_job.reload.status).to eq('completed')
          expect(bulk_job.completed_at).to be < (Time.now)
          expect(bulk_job.started_at).to be < (bulk_job.completed_at)
          expect(bulk_job.summary_file).to be_blank
          expect(bulk_job.error_summary_file).not_to be_blank
          expect(bulk_job.records.map(&:status).all?('failed')).to be_truthy
          expect(bulk_job.successful).to be(0)
          expect(bulk_job.failure).to be(3)
        end
      end

      context 'with completed or aborted job' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: EMAIL_OPERATION, payload: payload, entity: DEAL) }

        before(:each) do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1, parent_entity_id: 1)
        end

        it 'should should not update records if job completed' do
          bulk_job.completed!
          described_class.new(bulk_job).perform
          expect_any_instance_of(described_class).not_to receive(:execute_api_call)
        end

        it 'should should not update records if job aborted' do
          bulk_job.aborted!
          described_class.new(bulk_job).perform
          expect_any_instance_of(described_class).not_to receive(:execute_api_call)
        end
      end
    end

    context 'with campaign action email operations' do
      let(:campaign_payload) do
        {
          "subject" => "Campaign Email Subject",
          "body" => "Campaign Email Body",
          "emailTemplateId" => "456",
          "campaign" => {
            "id" => 101,
            "name" => "Summer Sale Campaign"
          },
          "activity" => {
            "id" => 1,
            "name" => "Email Activity"
          }
        }
      end

      [LEAD, CONTACT].each do |entity|
        context "with successful #{entity} campaign email operation" do
          let!(:test_tenant) { Tenant.create }
          let!(:test_user) { User.create(name: 'Tony Stark', token: 'test-token-123', tenant: test_tenant) }
          let!(:bulk_job) do
            create(:bulk_job,
              user_id: test_user.id,
              tenant_id: test_tenant.id,
              status: 'queued',
              operation: EMAIL_OPERATION,
              category: CAMPAIGN_ACTION,
              payload: campaign_payload,
              entity: entity
            )
          end

          before do
            create(:record, bulk_job: bulk_job, tenant_id: test_tenant.id, entity_id: 1)
            create(:record, bulk_job: bulk_job, tenant_id: test_tenant.id, entity_id: 2)

            send("stub_#{entity}", 1, test_user.token)
            send("stub_#{entity}", 2, test_user.token)

            stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
              .with(headers: { Authorization: "Bearer #{test_user.token}" })
              .to_return(status: 200, body: '')
          end

          it 'should publish CampaignActivityBulkJobStatus event when job starts' do
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
              .with(bulk_job: bulk_job, status: 'IN_PROGRESS')
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
              .with(bulk_job: bulk_job, status: 'COMPLETED')
            allow(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call)

            described_class.new(bulk_job).perform
          end

          it 'should publish CampaignEntityActivityStatus events for each record' do
            allow(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
            expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call)
              .with(hash_including(
                campaignId: 101,
                activityId: 1,
                userId: test_user.id,
                tenantId: test_tenant.id,
                entity: entity.upcase,
                entityId: 1,
                status: 'SUCCESS'
              ))
            expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call)
              .with(hash_including(
                campaignId: 101,
                activityId: 1,
                userId: test_user.id,
                tenantId: test_tenant.id,
                entity: entity.upcase,
                entityId: 2,
                status: 'SUCCESS'
              ))

            described_class.new(bulk_job).perform
          end

          it 'should complete the job successfully' do
            allow(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
            allow(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call)

            described_class.new(bulk_job).perform

            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.records.reload.all? { |r| r.status == 'succeeded' }).to be_truthy
            expect(bulk_job.successful).to be(2)
            expect(bulk_job.failure).to be(0)
          end
        end

        context "with failed #{entity} campaign email operation" do
          let!(:test_tenant) { Tenant.create }
          let!(:test_user) { User.create(name: 'Tony Stark', token: 'test-token-456', tenant: test_tenant) }
          let!(:bulk_job) do
            create(:bulk_job,
              user_id: test_user.id,
              tenant_id: test_tenant.id,
              status: 'queued',
              operation: EMAIL_OPERATION,
              category: CAMPAIGN_ACTION,
              payload: campaign_payload,
              entity: entity
            )
          end

          before do
            create(:record, bulk_job: bulk_job, tenant_id: test_tenant.id, entity_id: 1)
            send("stub_#{entity}", 1, test_user.token)

            stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
              .with(headers: { Authorization: "Bearer #{test_user.token}" })
              .to_return(status: 400, body: { errorCode: '********', message: "Uhoh! You do not have the necessary permission to view Email Templates" }.to_json)
          end

          it 'should publish CampaignEntityActivityStatus event with error status' do
            allow(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
            expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call)
              .with(hash_including(
                campaignId: 101,
                activityId: 1,
                userId: test_user.id,
                tenantId: test_tenant.id,
                entity: entity.upcase,
                entityId: 1,
                status: 'ERROR'
              ))

            described_class.new(bulk_job).perform
          end

          it 'should mark record as failed' do
            allow(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
            allow(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call)

            described_class.new(bulk_job).perform

            expect(bulk_job.reload.records.first.status).to eq('failed')
            expect(bulk_job.failure).to be(1)
            expect(bulk_job.successful).to be(0)
          end
        end

        context "with pausable error during #{entity} campaign email operation" do
          let!(:test_tenant) { Tenant.create }
          let!(:test_user) { User.create(name: 'Tony Stark', token: 'test-token-pause', tenant: test_tenant) }
          let!(:bulk_job) do
            create(:bulk_job,
              user_id: test_user.id,
              tenant_id: test_tenant.id,
              status: 'queued',
              operation: EMAIL_OPERATION,
              category: CAMPAIGN_ACTION,
              payload: campaign_payload,
              entity: entity
            )
          end

          before do
            create(:record, bulk_job: bulk_job, tenant_id: test_tenant.id, entity_id: 1)
            create(:record, bulk_job: bulk_job, tenant_id: test_tenant.id, entity_id: 2)

            send("stub_#{entity}", 1, test_user.token)
            send("stub_#{entity}", 2, test_user.token)

            allow_any_instance_of(EntityAction::Email).to receive(:execute_api_call) do |instance, record|
              if record.entity_id == 1
                double('response', code: 200, body: '')
              else
                exception = RestClient::BadRequest.new
                allow(exception).to receive(:http_body).and_return('{"errorCode": "********", "message": "Email template is inactive"}')
                raise exception
              end
            end

            connection = BunnyMock.new
            @channel = connection.start.channel
            @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
            @queue = @channel.queue BULK_JOB_PAUSED
            @queue.bind @exchange, routing_key: BULK_JOB_PAUSED
            allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
          end

          it 'should pause the job and publish campaign status event with paused status' do
            allow(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
              .with(bulk_job: bulk_job, status: 'IN_PROGRESS')
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call)
              .with(hash_including(bulk_job: bulk_job, status: 'PAUSED'))

            result = described_class.new(bulk_job).perform

            expect(result).to eq({ paused: true })
            expect(bulk_job.reload.status).to eq('paused')
            expect(bulk_job.sidekiq_job_id).to be_nil
          end
        end
      end

      context 'with non-campaign action email operations' do
        let!(:test_tenant) { Tenant.create }
        let!(:test_user) { User.create(name: 'Tony Stark', token: 'test-token-non-campaign', tenant: test_tenant) }
        let!(:bulk_job) do
          create(:bulk_job,
            user_id: test_user.id,
            tenant_id: test_tenant.id,
            status: 'queued',
            operation: EMAIL_OPERATION,
            category: BULK_ACTION,
            payload: payload,
            entity: LEAD
          )
        end

        before do
          create(:record, bulk_job: bulk_job, tenant_id: test_tenant.id, entity_id: 1)
          stub_lead(1, test_user.token)

          stub_request(:post, "#{SERVICE_EMAIL}/v1/emails/send-email")
            .with(headers: { Authorization: "Bearer #{test_user.token}" })
            .to_return(status: 200, body: '')
          
          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')
        end

        it 'should not publish campaign-specific events for non-campaign operations' do
          expect(Publishers::CampaignActivityBulkJobStatusPublisher).not_to receive(:call)
          expect(Publishers::CampaignEntityActivityStatusPublisher).not_to receive(:call)

          described_class.new(bulk_job).perform

          expect(bulk_job.reload.status).to eq('completed')
        end

        context 'with error in non-campaign operation' do
          before do
            create(:record, bulk_job: bulk_job, tenant_id: test_tenant.id, entity_id: 2)
            stub_lead(2, test_user.token)

            allow_any_instance_of(EntityAction::Email).to receive(:execute_api_call) do |instance, record|
              if record.entity_id == 2
                exception = RestClient::BadRequest.new
                allow(exception).to receive(:http_body).and_return('{"errorCode": "01601001", "message": "Authorization error"}')
                raise exception
              else
                double('response', code: 200, body: '')
              end
            end
          
            allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
            allow(URI).to receive_message_chain(:open, :read).and_return('file content')
          end

          it 'should handle errors without campaign-specific logic' do
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).not_to receive(:call)
            expect(Publishers::CampaignEntityActivityStatusPublisher).not_to receive(:call)
            expect(Publishers::SendBulkWhatsappJobPausedEmailPublisher).not_to receive(:call)

            described_class.new(bulk_job).perform

            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.records.find_by(entity_id: 2).status).to eq('failed')
          end
        end
      end
    end
  end
end
