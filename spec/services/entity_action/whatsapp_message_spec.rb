# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe EntityAction::WhatsappMessage do
  describe '#perform' do
    before { allow(FileUploader).to receive(:upload).and_return(nil) }

    context 'when bulk job category is bulk_action' do
      let(:payload) {
        {
          "connectedAccount": {
            "id": 123,
            "name": "Whatsapp Account 1"
          },
          "whatsappTemplate": {
            "id": 123,
            "name": "Whatsapp Template 1"
          },
          "messageSendTo": [
            {
              "name": "Record's primary phone number",
              "type": "PRIMARY_PHONE_NUMBER"
            }
          ]
        }
      }

      [LEAD, CONTACT].each do |entity|
        context "with successful #{entity} whatsapp message operation" do
          let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: '<PERSON>', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload, entity: entity) }

          before do
            first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
            second_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
            third_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)

            create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
            create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
            create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

            allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
            allow(URI).to receive_message_chain(:open, :read).and_return('file content')

            stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: '')
          end

          it 'should send message' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
            expect(PhoneNumberWiseRecordDetail.pluck(:status).all?('succeeded')).to be_truthy
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).to be_blank
            expect(bulk_job.successful).to be(3)
            expect(bulk_job.failure).to be(0)
          end

          context 'when job is aborted' do
            it 'should stop sending messages' do
              allow(bulk_job).to receive(:aborting?).and_return(false, true)

              payload = bulk_job.payload
              bulk_job.update!(payload: payload)
              described_class.new(bulk_job).perform
              expect(bulk_job.records.pluck(:status)).to match_array(%w[queued queued succeeded])
              expect(bulk_job.reload.status).to eq('aborted')
              expect(bulk_job.completed_at).to be < (Time.now)
              expect(bulk_job.started_at).to be < (bulk_job.completed_at)
              expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued succeeded])
              expect(bulk_job.summary_file).not_to be_blank
              expect(bulk_job.error_summary_file).to be_blank
              expect(bulk_job.successful).to be(1)
              expect(bulk_job.failure).to be(0)
            end
          end

          context 'when job is paused' do
            before { bulk_job.paused! }

            it 'should stop sending messages' do
              response = described_class.new(bulk_job).perform
              expect(bulk_job.records.pluck(:status)).to match(%w[queued queued queued])
              expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued queued])
              expect(response).to eq({ paused: true })
            end
          end
        end

        context 'with failed record operation' do
          let(:second_bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload, entity: entity) }

          before do
            allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
            allow(URI).to receive_message_chain(:open, :read).and_return('file content')
          end

          context 'when no phone numbers are available on some records' do
            before(:each) do
              first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
              create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
              create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

              create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
              stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: '')
            end

            it 'sends message to available record phone numbers and log error for failure' do
              described_class.new(second_bulk_job).perform
              expect(second_bulk_job.reload.status).to eq('completed')
              expect(second_bulk_job.completed_at).to be < (Time.now)
              expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
              expect(second_bulk_job.records.where(entity_id: [4]).map(&:status).all?('succeeded')).to be_truthy
              err_record = second_bulk_job.records.where(entity_id: 5).first
              expect(err_record.status).to eq('failed')
              expect(err_record.error_message).to eq('No phone numbers are present on the record')
              expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[succeeded])
              expect(second_bulk_job.records.where(entity_id: [4, 5, 6]).map(&:status)).to match(%w[succeeded failed failed])
              expect(second_bulk_job.summary_file).not_to be_blank
              expect(second_bulk_job.error_summary_file).not_to be_blank
              expect(second_bulk_job.successful).to be(1)
              expect(second_bulk_job.failure).to be(2)
            end
          end

          context 'when send message request fails' do
            context 'when there is third party api error' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 422, body: { errorCode: '022017' }.to_json)
              end

              it 'should not send message and logs error for failures' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('completed')
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
                expect(second_bulk_job.records.map(&:status).all?('failed')).to be_truthy
                expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[failed failed failed])
                err_record = second_bulk_job.records.where(entity_id: 5).first
                expect(err_record.status).to eq('failed')
                expect(err_record.error_message).to eq('Message is not delivered successfully on following phone numbers: +91*********3')
                expect(err_record.phone_number_wise_record_details.pluck(:status)).to eq(['failed'])
                expect(err_record.phone_number_wise_record_details.pluck(:error_message)).to eq(["Third party api error"])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?('Third party api error')).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).not_to be_blank
                expect(second_bulk_job.successful).to be(0)
                expect(second_bulk_job.failure).to be(3)
              end
            end

            context 'when whatsapp template is not found' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 404, body: { errorCode: '022006' }.to_json)
              end

              it 'should not send message and logs error for failure' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('completed')
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
                expect(second_bulk_job.records.map(&:status).all?('failed')).to be_truthy
                expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[failed failed failed])
                err_record = second_bulk_job.records.where(entity_id: 5).first
                expect(err_record.status).to eq('failed')
                expect(err_record.error_message).to eq('Whatsapp template not found')
                expect(err_record.phone_number_wise_record_details.pluck(:status)).to eq(['failed'])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?('Whatsapp template not found')).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).not_to be_blank
                expect(second_bulk_job.successful).to be(0)
                expect(second_bulk_job.failure).to be(3)
              end
            end

            context 'when there are insufficient whatsapp credits for bulk operation' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 422, body: { errorCode: '022028' }.to_json)
                connection = BunnyMock.new
                @channel = connection.start.channel
                @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
                @queue = @channel.queue BULK_JOB_PAUSED
                @queue.bind @exchange, routing_key: BULK_JOB_PAUSED
                allow(PublishEvent).to receive(:call).with(instance_of(Event::BulkWhatsappJobPaused))
                allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
              end

              it 'should not send message and logs error for failure' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (Time.now)
                expect(second_bulk_job.records.map(&:status).all?('queued')).to be_truthy
                expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued queued])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?(nil)).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).to be_blank
              end

              it 'pause the bulk job execution' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('paused')
              end
            end

            context 'when whatsapp template is not approved' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                    .to_return(status: 422, body: { errorCode: '022029' }.to_json)
              end

              context 'when bulk job paused email is not sent' do
                before(:each) do
                  connection = BunnyMock.new
                  @channel = connection.start.channel
                  @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
                  @queue = @channel.queue BULK_JOB_PAUSED
                  @queue.bind @exchange, routing_key: BULK_JOB_PAUSED
                  allow(PublishEvent).to receive(:call).with(instance_of(Event::BulkWhatsappJobPaused))
                  allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
                end

                it 'sends email and should not send message and logs error for failure' do
                  described_class.new(second_bulk_job).perform
                  expect(second_bulk_job.completed_at).to be < (Time.now)
                  expect(second_bulk_job.started_at).to be < (Time.now)
                  expect(second_bulk_job.records.map(&:status).all?('queued')).to be_truthy
                  expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued queued])
                  expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?(nil)).to be_truthy
                  expect(second_bulk_job.summary_file).to be_blank
                  expect(second_bulk_job.error_summary_file).to be_blank
                end

                it 'pause the bulk job execution' do
                  described_class.new(second_bulk_job).perform
                  expect(second_bulk_job.reload.status).to eq('paused')
                end
              end
            end

            context 'when unmapped error occurs' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 422, body: { errorCode: '022012' }.to_json)
              end

              it 'log error for failures' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('completed')
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
                expect(second_bulk_job.records.map(&:status).all?('failed')).to be_truthy
                expect(second_bulk_job.records.map(&:error_message).uniq).to match_array(["Message is not delivered successfully on following phone numbers: +************", "Message is not delivered successfully on following phone numbers: +91*********3", "Message is not delivered successfully on following phone numbers: +91*********4"])

                err_record = second_bulk_job.records.where(entity_id: 5).first
                expect(err_record.status).to eq('failed')
                expect(err_record.error_message).to eq('Message is not delivered successfully on following phone numbers: +91*********3')
                expect(err_record.phone_number_wise_record_details.pluck(:status)).to eq(['failed'])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?('Something went wrong.')).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).not_to be_blank
                expect(second_bulk_job.successful).to be(0)
                expect(second_bulk_job.failure).to be(3)
              end
            end
          end

          context 'with completed or aborted job' do
            before(:each) do
              create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 1)
            end

            it 'should not update records if job completed' do
              second_bulk_job.completed!
              described_class.new(second_bulk_job).perform
              expect_any_instance_of(described_class).not_to receive(:execute_api_call)
            end

            it 'should not update records if job aborted' do
              second_bulk_job.aborted!
              described_class.new(second_bulk_job).perform
              expect_any_instance_of(described_class).not_to receive(:execute_api_call)
            end
          end
        end

        context 'when dynamic media is present' do
          let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload, entity: entity) }

          before do
            bulk_job.update!(payload: payload.merge('whatsappTemplate' => { 'id' => 123, 'name' => 'Whatsapp Template 1', 'dynamicTemplateMediaId' => 'media-123' }))
            s3_resource = instance_double(Aws::S3::Resource)
            s3_bucket = instance_double(Aws::S3::Bucket)
            s3_object = instance_double(Aws::S3::Object)
            
            allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
            allow(s3_resource).to receive(:bucket).and_return(s3_bucket)
            allow(s3_bucket).to receive(:object).and_return(s3_object)
            allow(s3_object).to receive(:upload_file)

            stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/123/template-media/media-123")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: {
                id: 'media-123',
                fileName: 'media-123.jpg',
                fileSize: 12345,
                fileType: 'image/jpeg',
                mediaUrl: {
                  url: 'https://dummy.aws.com/media-123',
                  fileName: 'media-123.jpg'
                }
              }.to_json)

            stub_request(:get, 'https://dummy.aws.com/media-123')
              .to_return(status: 200, body: 'dummy media content')

            first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 4)
            create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))

            stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: '')
          end

          it 'processes dynamic media before sending messages' do
            described_class.new(bulk_job).perform

            expect(bulk_job.reload.payload['whatsappTemplate']['dynamicTemplateMediaName']).to eq('media-123.jpg')
          end
        end
      end
    end

    context 'when bulk job category is campaign_action' do
      let(:payload) {
        {
          "connectedAccount": {
            "id": 123,
            "name": "Whatsapp Account 1"
          },
          "whatsappTemplate": {
            "id": 123,
            "name": "Whatsapp Template 1"
          },
          "messageSendTo": [
            {
              "name": "Record's primary phone number",
              "type": "PRIMARY_PHONE_NUMBER"
            }
          ],
          "campaign": {
            "id": 456,
            "name": "Campaign 1"
          },
          "activity": {
            "id": 789,
            "name": "Activity 1"
          }
        }
      }

      let(:payload_with_retry_config) {
        {
          "connectedAccount": {
            "id": 123,
            "name": "Whatsapp Account 1"
          },
          "whatsappTemplate": {
            "id": 123,
            "name": "Whatsapp Template 1"
          },
          "messageSendTo": [
            {
              "name": "Record's primary phone number",
              "type": "PRIMARY_PHONE_NUMBER"
            }
          ],
          "campaign": {
            "id": 456,
            "name": "Campaign 1"
          },
          "activity": {
            "id": 789,
            "name": "Activity 1"
          },
          "retryConfig": {
            "noOfTimes": 3,
            "timesRetried": 0
          }
        }
      }

      [LEAD, CONTACT].each do |entity|
        context "with successful #{entity} whatsapp message operation" do
          let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload, entity: entity, category: CAMPAIGN_ACTION) }

          before do
            first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
            second_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
            third_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)

            create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
            create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
            create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

            allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
            allow(URI).to receive_message_chain(:open, :read).and_return('file content')

            stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: '')
          end

          context 'when job is completed' do
            before do
              expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_IN_PROGRESS.upcase)
              expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                campaignId: 456,
                activityId: 789,
                userId: 1,
                tenantId: 99,
                entity: entity.upcase,
                entityId: 1,
                phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                status: 'SUCCESS'
              })

              expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                campaignId: 456,
                activityId: 789,
                userId: 1,
                tenantId: 99,
                entity: entity.upcase,
                entityId: 2,
                phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********3', 'primary'=>true, 'dialCode'=>'+91'},
                status: 'SUCCESS'
              })

              expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                campaignId: 456,
                activityId: 789,
                userId: 1,
                tenantId: 99,
                entity: entity.upcase,
                entityId: 3,
                phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********4', 'primary'=>true, 'dialCode'=>'+91'},
                status: 'SUCCESS'
              })

              expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_COMPLETED.upcase)
            end

            it 'should send message and publishes campaign activity status and entity status events' do
              described_class.new(bulk_job).perform
              expect(bulk_job.reload.status).to eq('completed')
              expect(bulk_job.completed_at).to be < (Time.now)
              expect(bulk_job.started_at).to be < (bulk_job.completed_at)
              expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
              expect(PhoneNumberWiseRecordDetail.pluck(:status).all?('succeeded')).to be_truthy
              expect(bulk_job.summary_file).not_to be_blank
              expect(bulk_job.error_summary_file).to be_blank
              expect(bulk_job.successful).to be(3)
              expect(bulk_job.failure).to be(0)
            end
          end

          context 'when job is aborted' do
            before do
              expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_IN_PROGRESS.upcase)
              expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                campaignId: 456,
                activityId: 789,
                userId: 1,
                tenantId: 99,
                entity: entity.upcase,
                entityId: 1,
                phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                status: 'SUCCESS'
              })

              expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_COMPLETED.upcase)
            end

            it 'should stop sending messages' do
              allow(bulk_job).to receive(:aborting?).and_return(false, true)

              payload = bulk_job.payload
              bulk_job.update!(payload: payload, status: 'aborting')
              described_class.new(bulk_job).perform
              expect(bulk_job.records.pluck(:status)).to match_array(%w[queued queued failed])
              expect(bulk_job.reload.status).to eq('aborted')
              expect(bulk_job.completed_at).to be < (Time.now)
              expect(bulk_job.started_at).to be < (bulk_job.completed_at)
              expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued failed])
              expect(bulk_job.summary_file).to be_blank
              expect(bulk_job.error_summary_file).not_to be_blank
              expect(bulk_job.successful).to be(0)
              expect(bulk_job.failure).to be(1)
            end
          end

          context 'when job is paused' do
            before { bulk_job.paused! }

            it 'should stop sending messages' do
              response = described_class.new(bulk_job).perform
              expect(bulk_job.records.pluck(:status)).to match(%w[queued queued queued])
              expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued queued])
              expect(response).to eq({ paused: true })
            end
          end
        end

        context 'with failed record operation' do
          let(:second_bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload, entity: entity, category: CAMPAIGN_ACTION) }

          before do
            allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
            allow(URI).to receive_message_chain(:open, :read).and_return('file content')
          end

          context 'when no phone numbers are available on some records' do
            before(:each) do
              first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
              create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
              create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

              create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
              stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: { 'id': 321 }.to_json)

              expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_IN_PROGRESS.upcase)
              expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                campaignId: 456,
                activityId: 789,
                userId: 1,
                tenantId: 99,
                entity: entity.upcase,
                entityId: 4,
                phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                status: 'SUCCESS'
              })

              expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_COMPLETED.upcase)
            end

            it 'sends message to available record phone numbers and log error for failure' do
              described_class.new(second_bulk_job).perform
              expect(second_bulk_job.reload.status).to eq('completed')
              expect(second_bulk_job.completed_at).to be < (Time.now)
              expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
              expect(second_bulk_job.records.where(entity_id: [4]).map(&:status).all?('succeeded')).to be_truthy
              err_record = second_bulk_job.records.where(entity_id: 5).first
              expect(err_record.status).to eq('failed')
              expect(err_record.error_message).to eq('No phone numbers are present on the record')
              expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[succeeded])
              expect(second_bulk_job.records.where(entity_id: [4, 5, 6]).map(&:status)).to match(%w[succeeded failed failed])
              expect(second_bulk_job.summary_file).not_to be_blank
              expect(second_bulk_job.error_summary_file).not_to be_blank
              expect(second_bulk_job.successful).to be(1)
              expect(second_bulk_job.failure).to be(2)
            end
          end

          context 'when send message request fails' do
            context 'when there is third party api error' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 422, body: { errorCode: '022017' }.to_json)

                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: entity.upcase,
                  entityId: 4,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'ERROR',
                  error: 'Third party api error'
                })

                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: entity.upcase,
                  entityId: 5,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********3', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'ERROR',
                  error: 'Third party api error'
                })

                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: entity.upcase,
                  entityId: 6,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********4', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'ERROR',
                  error: 'Third party api error'
                })
  
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_COMPLETED.upcase)
              end

              it 'should not send message and logs error for failures' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('completed')
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
                expect(second_bulk_job.records.map(&:status).all?('failed')).to be_truthy
                expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[failed failed failed])
                err_record = second_bulk_job.records.where(entity_id: 5).first
                expect(err_record.status).to eq('failed')
                expect(err_record.error_message).to eq('Message is not delivered successfully on following phone numbers: +91*********3')
                expect(err_record.phone_number_wise_record_details.pluck(:status)).to eq(['failed'])
                expect(err_record.phone_number_wise_record_details.pluck(:error_message)).to eq(["Third party api error"])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?('Third party api error')).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).not_to be_blank
                expect(second_bulk_job.successful).to be(0)
                expect(second_bulk_job.failure).to be(3)
              end
            end

            context 'when whatsapp template is not found' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 404, body: { errorCode: '022006' }.to_json)
                
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: entity.upcase,
                  entityId: 4,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'ERROR',
                  error: 'Whatsapp template not found'
                })
  
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_COMPLETED.upcase)
              end

              it 'should not send message and logs error for failure' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('completed')
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
                expect(second_bulk_job.records.map(&:status).all?('failed')).to be_truthy
                expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[failed failed failed])
                err_record = second_bulk_job.records.where(entity_id: 5).first
                expect(err_record.status).to eq('failed')
                expect(err_record.error_message).to eq('Whatsapp template not found')
                expect(err_record.phone_number_wise_record_details.pluck(:status)).to eq(['failed'])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?('Whatsapp template not found')).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).not_to be_blank
                expect(second_bulk_job.successful).to be(0)
                expect(second_bulk_job.failure).to be(3)
              end
            end

            context 'when there are insufficient whatsapp credits for bulk operation' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 422, body: { errorCode: '022028' }.to_json)
                connection = BunnyMock.new
                @channel = connection.start.channel
                @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
                @queue = @channel.queue BULK_JOB_PAUSED
                @queue.bind @exchange, routing_key: BULK_JOB_PAUSED
                allow(PublishEvent).to receive(:call).with(instance_of(Event::BulkWhatsappJobPaused))
                allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)

                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_PAUSED.upcase, status_reason: 'Insufficient whatsapp credits balance for bulk action')
              end

              it 'should not send message and logs error for failure' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (Time.now)
                expect(second_bulk_job.records.map(&:status).all?('queued')).to be_truthy
                expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued queued])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?(nil)).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).to be_blank
              end

              it 'pause the bulk job execution' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('paused')
              end
            end

            context 'when whatsapp template is not approved' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                    .to_return(status: 422, body: { errorCode: '022029' }.to_json)
                
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_PAUSED.upcase, status_reason: 'Whatsapp Template is not Approved')
              end

              context 'when bulk job paused email is not sent' do
                before(:each) do
                  connection = BunnyMock.new
                  @channel = connection.start.channel
                  @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
                  @queue = @channel.queue BULK_JOB_PAUSED
                  @queue.bind @exchange, routing_key: BULK_JOB_PAUSED
                  allow(PublishEvent).to receive(:call).with(instance_of(Event::BulkWhatsappJobPaused))
                  allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
                end

                it 'sends email and should not send message and logs error for failure' do
                  described_class.new(second_bulk_job).perform
                  expect(second_bulk_job.completed_at).to be < (Time.now)
                  expect(second_bulk_job.started_at).to be < (Time.now)
                  expect(second_bulk_job.records.map(&:status).all?('queued')).to be_truthy
                  expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued queued])
                  expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?(nil)).to be_truthy
                  expect(second_bulk_job.summary_file).to be_blank
                  expect(second_bulk_job.error_summary_file).to be_blank
                end

                it 'pause the bulk job execution' do
                  described_class.new(second_bulk_job).perform
                  expect(second_bulk_job.reload.status).to eq('paused')
                end
              end
            end

            context 'when template entity is mismatched or template variables are not mapped' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                    .to_return(status: 422, body: { errorCode: '022003' }.to_json)
                
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_PAUSED.upcase, status_reason: 'Template entity mismatch or All template variables are not mapped')
              end

              context 'when bulk job paused email is not sent' do
                before(:each) do
                  connection = BunnyMock.new
                  @channel = connection.start.channel
                  @exchange = @channel.topic BULK_ACTIONS_EXCHANGE
                  @queue = @channel.queue BULK_JOB_PAUSED
                  @queue.bind @exchange, routing_key: BULK_JOB_PAUSED
                  allow(PublishEvent).to receive(:call).with(instance_of(Event::BulkWhatsappJobPaused))
                  allow(RabbitmqConnection).to receive(:get_exchange).with(BULK_ACTIONS_EXCHANGE).and_return(@queue)
                end

                it 'sends email and should not send message and logs error for failure' do
                  described_class.new(second_bulk_job).perform
                  expect(second_bulk_job.completed_at).to be < (Time.now)
                  expect(second_bulk_job.started_at).to be < (Time.now)
                  expect(second_bulk_job.records.map(&:status).all?('queued')).to be_truthy
                  expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued queued queued])
                  expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?(nil)).to be_truthy
                  expect(second_bulk_job.summary_file).to be_blank
                  expect(second_bulk_job.error_summary_file).to be_blank
                  expect(second_bulk_job.payload['errorMessage']).to eq('Template entity mismatch or All template variables are not mapped')
                end

                it 'pause the bulk job execution' do
                  described_class.new(second_bulk_job).perform
                  expect(second_bulk_job.reload.status).to eq('paused')
                end
              end
            end

            context 'when unmapped error occurs' do
              before(:each) do
                first_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 4)
                second_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 5)
                third_record = create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 6)

                create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: JSON.parse(file_fixture('entity_details_response.json').read))
                create(:phone_number_wise_record_detail, record_id: third_record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: JSON.parse(file_fixture('entity_details_response.json').read))

                stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
                  .with(
                    headers: {
                      Authorization: "Bearer some-token"
                    }
                  )
                  .to_return(status: 422, body: { errorCode: '022012' }.to_json)
                
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: entity.upcase,
                  entityId: 4,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'ERROR',
                  error: 'Something went wrong.'
                })

                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: entity.upcase,
                  entityId: 5,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********3', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'ERROR',
                  error: 'Something went wrong.'
                })

                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: entity.upcase,
                  entityId: 6,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********4', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'ERROR',
                  error: 'Something went wrong.'
                })
  
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: second_bulk_job, status: STATUS_COMPLETED.upcase)
              end

              it 'log error for failures' do
                described_class.new(second_bulk_job).perform
                expect(second_bulk_job.reload.status).to eq('completed')
                expect(second_bulk_job.completed_at).to be < (Time.now)
                expect(second_bulk_job.started_at).to be < (second_bulk_job.completed_at)
                expect(second_bulk_job.records.map(&:status).all?('failed')).to be_truthy
                expect(second_bulk_job.records.map(&:error_message).uniq).to match_array(["Message is not delivered successfully on following phone numbers: +************", "Message is not delivered successfully on following phone numbers: +91*********3", "Message is not delivered successfully on following phone numbers: +91*********4"])

                err_record = second_bulk_job.records.where(entity_id: 5).first
                expect(err_record.status).to eq('failed')
                expect(err_record.error_message).to eq('Message is not delivered successfully on following phone numbers: +91*********3')
                expect(err_record.phone_number_wise_record_details.pluck(:status)).to eq(['failed'])
                expect(PhoneNumberWiseRecordDetail.pluck(:error_message).all?('Something went wrong.')).to be_truthy
                expect(second_bulk_job.summary_file).to be_blank
                expect(second_bulk_job.error_summary_file).not_to be_blank
                expect(second_bulk_job.successful).to be(0)
                expect(second_bulk_job.failure).to be(3)
              end
            end
          end

          context 'with completed or aborted job' do
            before(:each) do
              create(:record, bulk_job: second_bulk_job, tenant_id: 99, entity_id: 1)
            end

            it 'should not update records if job completed' do
              second_bulk_job.completed!
              described_class.new(second_bulk_job).perform
              expect_any_instance_of(described_class).not_to receive(:execute_api_call)
            end

            it 'should not update records if job aborted' do
              second_bulk_job.aborted!
              described_class.new(second_bulk_job).perform
              expect_any_instance_of(described_class).not_to receive(:execute_api_call)
            end
          end
        end

        context 'when dynamic media is present' do
          let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload, entity: entity) }

          before do
            bulk_job.update!(payload: payload.merge('whatsappTemplate' => { 'id' => 123, 'name' => 'Whatsapp Template 1', 'dynamicTemplateMediaId' => 'media-123' }))
            s3_resource = instance_double(Aws::S3::Resource)
            s3_bucket = instance_double(Aws::S3::Bucket)
            s3_object = instance_double(Aws::S3::Object)
            
            allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
            allow(s3_resource).to receive(:bucket).and_return(s3_bucket)
            allow(s3_bucket).to receive(:object).and_return(s3_object)
            allow(s3_object).to receive(:upload_file)

            stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/123/template-media/media-123")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: {
                id: 'media-123',
                fileName: 'media-123.jpg',
                fileSize: 12345,
                fileType: 'image/jpeg',
                mediaUrl: {
                  url: 'https://dummy.aws.com/media-123',
                  fileName: 'media-123.jpg'
                }
              }.to_json)

            stub_request(:get, 'https://dummy.aws.com/media-123')
              .to_return(status: 200, body: 'dummy media content')

            first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 4)
            create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read))

            stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
              .with(
                headers: {
                  Authorization: "Bearer some-token"
                }
              )
              .to_return(status: 200, body: '')
          end

          it 'processes dynamic media before sending messages' do
            described_class.new(bulk_job).perform

            expect(bulk_job.reload.payload['whatsappTemplate']['dynamicTemplateMediaName']).to eq('media-123.jpg')
          end
        end
      end

      context "when retry config is present on the bulk job" do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload_with_retry_config, entity: 'lead', category: CAMPAIGN_ACTION) }

        before do
          first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)

          create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read, message_id: 123, status: STATUS_RETRYING, message_status: SENDING))

          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')

          stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
            .with(
              headers: {
                Authorization: "Bearer some-token"
              },
              body: {
                "activityId"=>"789", 
                "bulkJob"=>"true", 
                "campaignId"=>"456", 
                "dynamicTemplateMediaId"=>nil, 
                "entityData"=>{"id"=>"513753", "dnd"=>nil, "city"=>nil, "name"=>"MaskedPhone312 MaskedPhone312", "isNew"=>"false", "score"=>"0.0", "state"=>nil, "emails"=>nil, "source"=>"387685", "address"=>nil, "country"=>nil, "deleted"=>"false", "ownerId"=>"7990", "twitter"=>nil, "utmTerm"=>"Test 6", "version"=>"1", "zipcode"=>nil, "campaign"=>"387681", "facebook"=>nil, "lastName"=>"MaskedPhone312", "linkedIn"=>nil, "metaData"=>{"idNameStore"=>{"source"=>["Facebook", "LinkedIn", "Exhibition"], "country"=>nil, "ownerId"=>["Pansare Aditya", "mayur p", "st dp", "Addi user", "karan sharma"], "campaign"=>["Organic", "new-updated"], "pipeline"=>["start", "kak"], "timezone"=>nil, "createdBy"=>["Pansare Aditya", "st dp"], "updatedBy"=>["Pansare Aditya"], "importedBy"=>nil, "salutation"=>["Mr", "Mrs", "Miss-updated"], "convertedBy"=>nil, "pipelineStage"=>["Open", "Open"], "cfTestWorkflow"=>nil, "companyCountry"=>nil, "companyIndustry"=>nil, "companyEmployees"=>["5-9-updated", "10-19"], "cfMultiPickListTest"=>["2"], "cfPicklistFieldTest"=>["Tuesday"], "companyBusinessType"=>nil, "requirementCurrency"=>nil}}, "pipeline"=>nil, "products"=>[{"id"=>"22628", "name"=>"new product", "tenantId"=>"3440"}], "tenantId"=>"3440", "timezone"=>nil, "createdAt"=>"2024-12-30T09:02:13.025Z", "createdBy"=>"7638", "firstName"=>"MaskedPhone312", "photoUrls"=>nil, "subSource"=>"Test 1", "taskDueOn"=>nil, "updatedAt"=>"2024-12-30T09:02:13.475Z", "updatedBy"=>"7638", "utmMedium"=>"Test 4", "utmSource"=>"Test 2", "department"=>nil, "importedBy"=>nil, "salutation"=>nil, "utmContent"=>"Test 5", "companyCity"=>nil, "companyName"=>nil, "convertedAt"=>nil, "convertedBy"=>nil, "designation"=>nil, "utmCampaign"=>"Test 3", "companyState"=>nil, "createdViaId"=>"universal-app-key", "phoneNumbers"=>[{"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********2", "primary"=>"true", "dialCode"=>"+91"}, {"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********3", "primary"=>"true", "dialCode"=>"+91"}, {"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********4", "primary"=>"true", "dialCode"=>"+91"}], "updatedViaId"=>"WF_4735", "companyPhones"=>nil, "pipelineStage"=>nil, "recordActions"=>{"sms"=>"true", "call"=>"true", "note"=>"true", "read"=>"true", "task"=>"true", "email"=>"true", "write"=>"true", "delete"=>"true", "update"=>"true", "meeting"=>"true", "readAll"=>"true", "reshare"=>"true", "document"=>"true", "reassign"=>"true", "deleteAll"=>"true", "quotation"=>"false", "updateAll"=>"true"}, "companyAddress"=>nil, "companyCountry"=>nil, "companyWebsite"=>nil, "companyZipcode"=>nil, "createdViaName"=>"Universal API Key", "createdViaType"=>"API Key", "updatedViaName"=>"Webhook and Marketplace action log", "updatedViaType"=>"Workflow", "companyIndustry"=>nil, "forecastingType"=>nil, "requirementName"=>nil, "companyEmployees"=>nil, "actualClosureDate"=>nil, "addressCoordinate"=>nil, "expectedClosureOn"=>nil, "requirementBudget"=>nil, "meetingScheduledOn"=>nil, "companyBusinessType"=>nil, "pipelineStageReason"=>nil, "requirementCurrency"=>nil, "companyAnnualRevenue"=>nil, "latestActivityCreatedAt"=>"2024-12-30T09:02:13.478Z", "companyAddressCoordinate"=>nil},
                "entityId"=>"1", 
                "entityType"=>"lead", 
                "phoneNumber"=>{"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********2", "primary"=>"true", "dialCode"=>"+91"}, 
                "retryConfig"=>{"noOfTimes"=>"3", "timesRetried"=>"0"}
              }
            ).to_return(status: 200, body: '')
        end

        context 'success' do
          before do
            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_IN_PROGRESS.upcase)
            expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
              campaignId: 456,
              activityId: 789,
              userId: 1,
              tenantId: 99,
              entity: 'LEAD',
              entityId: 1,
              phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
              status: 'SUCCESS'
            })

            allow(PhoneNumberWiseRecordDetail).to receive(:where).and_return(PhoneNumberWiseRecordDetail.none)

            expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_COMPLETED.upcase)
          end

          it 'should send message by passing retry config and publishes campaign activity status and entity status events' do
            described_class.new(bulk_job).perform
            expect(bulk_job.reload.status).to eq('completed')
            expect(bulk_job.completed_at).to be < (Time.now)
            expect(bulk_job.started_at).to be < (bulk_job.completed_at)
            expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
            expect(PhoneNumberWiseRecordDetail.pluck(:status).all?('succeeded')).to be_truthy
            expect(bulk_job.summary_file).not_to be_blank
            expect(bulk_job.error_summary_file).to be_blank
            expect(bulk_job.successful).to be(1)
            expect(bulk_job.failure).to be(0)
          end
        end

        context 'when we get retryable message error codes from meta' do
          context 'when retry config retryCount is less than noOfTimes' do
            context 'when phone number wise record detail are present with sending message status' do
              before do
                bulk_job.update(completed_at: nil)
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: 'LEAD',
                  entityId: 1,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'SUCCESS'
                })

                expect(Publishers::CampaignActivityBulkJobStatusPublisher).not_to receive(:call).with(bulk_job: bulk_job, status: STATUS_COMPLETED.upcase)
              end

              it 'updates the bulk job and respective phone_number_wise_record_detail to retrying status' do
                first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
                phone_number_wise_record_detail = create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'succeeded', message_status: SENDING)

                described_class.new(bulk_job).perform

                expect(phone_number_wise_record_detail.reload.status).to eq('retrying')
                expect(bulk_job.reload.status).to eq('retrying')
              end

              it 'updates the retryable messages count on the bulk job' do
                first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
                phone_number_wise_record_detail = create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'succeeded', message_status: SENDING)

                described_class.new(bulk_job).perform

                expect(phone_number_wise_record_detail.reload.status).to eq('retrying')
                expect(bulk_job.reload.payload['retryConfig']['retryableMessages']).to eq(2)
              end

              it 'does not mark bulk job as completed or aborted' do
                first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
                phone_number_wise_record_detail = create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'succeeded', message_status: SENDING)

                described_class.new(bulk_job).perform

                expect(bulk_job.reload.status).to eq('retrying')
                expect(bulk_job.completed_at).to be_nil
              end
            end

            context 'when phone number wise record detail are not present with sending message status' do
              before do
                bulk_job.update(completed_at: nil)
                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_IN_PROGRESS.upcase)
                expect(Publishers::CampaignEntityActivityStatusPublisher).to receive(:call).with({
                  campaignId: 456,
                  activityId: 789,
                  userId: 1,
                  tenantId: 99,
                  entity: 'LEAD',
                  entityId: 1,
                  phoneNumber: {'id'=>216106, 'code'=>'IN', 'type'=>'WORK', 'value'=>'*********2', 'primary'=>true, 'dialCode'=>'+91'},
                  status: 'SUCCESS'
                })

                allow(PhoneNumberWiseRecordDetail).to receive(:where).and_return(PhoneNumberWiseRecordDetail.none)

                expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_COMPLETED.upcase)
              end

              it 'updates the bulk job and respective phone_number_wise_record_detail to retrying state' do
                first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
                phone_number_wise_record_detail = create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'succeeded', message_status: SENT)

                described_class.new(bulk_job).perform

                expect(phone_number_wise_record_detail.reload.status).to eq('succeeded')
                expect(bulk_job.reload.status).to eq('completed')
              end

              it 'should mark bulk job as completed' do
                first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
                phone_number_wise_record_detail = create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'succeeded', message_status: SENT)

                described_class.new(bulk_job).perform

                expect(bulk_job.reload.status).to eq('completed')
                expect(bulk_job.completed_at).not_to be_nil
              end
            end
          end
        end
      end

      context 'when job is in aborting status' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'retrying', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload_with_retry_config, entity: 'lead', category: CAMPAIGN_ACTION) }

        before do
          first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)

          create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read, message_id: 123, status: STATUS_RETRYING, message_status: SENDING))

          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')

          stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
            .with(
              headers: {
                Authorization: "Bearer some-token"
              },
              body: {
                "activityId"=>"789", 
                "bulkJob"=>"true", 
                "campaignId"=>"456", 
                "dynamicTemplateMediaId"=>nil, 
                "entityData"=>{"id"=>"513753", "dnd"=>nil, "city"=>nil, "name"=>"MaskedPhone312 MaskedPhone312", "isNew"=>"false", "score"=>"0.0", "state"=>nil, "emails"=>nil, "source"=>"387685", "address"=>nil, "country"=>nil, "deleted"=>"false", "ownerId"=>"7990", "twitter"=>nil, "utmTerm"=>"Test 6", "version"=>"1", "zipcode"=>nil, "campaign"=>"387681", "facebook"=>nil, "lastName"=>"MaskedPhone312", "linkedIn"=>nil, "metaData"=>{"idNameStore"=>{"source"=>["Facebook", "LinkedIn", "Exhibition"], "country"=>nil, "ownerId"=>["Pansare Aditya", "mayur p", "st dp", "Addi user", "karan sharma"], "campaign"=>["Organic", "new-updated"], "pipeline"=>["start", "kak"], "timezone"=>nil, "createdBy"=>["Pansare Aditya", "st dp"], "updatedBy"=>["Pansare Aditya"], "importedBy"=>nil, "salutation"=>["Mr", "Mrs", "Miss-updated"], "convertedBy"=>nil, "pipelineStage"=>["Open", "Open"], "cfTestWorkflow"=>nil, "companyCountry"=>nil, "companyIndustry"=>nil, "companyEmployees"=>["5-9-updated", "10-19"], "cfMultiPickListTest"=>["2"], "cfPicklistFieldTest"=>["Tuesday"], "companyBusinessType"=>nil, "requirementCurrency"=>nil}}, "pipeline"=>nil, "products"=>[{"id"=>"22628", "name"=>"new product", "tenantId"=>"3440"}], "tenantId"=>"3440", "timezone"=>nil, "createdAt"=>"2024-12-30T09:02:13.025Z", "createdBy"=>"7638", "firstName"=>"MaskedPhone312", "photoUrls"=>nil, "subSource"=>"Test 1", "taskDueOn"=>nil, "updatedAt"=>"2024-12-30T09:02:13.475Z", "updatedBy"=>"7638", "utmMedium"=>"Test 4", "utmSource"=>"Test 2", "department"=>nil, "importedBy"=>nil, "salutation"=>nil, "utmContent"=>"Test 5", "companyCity"=>nil, "companyName"=>nil, "convertedAt"=>nil, "convertedBy"=>nil, "designation"=>nil, "utmCampaign"=>"Test 3", "companyState"=>nil, "createdViaId"=>"universal-app-key", "phoneNumbers"=>[{"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********2", "primary"=>"true", "dialCode"=>"+91"}, {"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********3", "primary"=>"true", "dialCode"=>"+91"}, {"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********4", "primary"=>"true", "dialCode"=>"+91"}], "updatedViaId"=>"WF_4735", "companyPhones"=>nil, "pipelineStage"=>nil, "recordActions"=>{"sms"=>"true", "call"=>"true", "note"=>"true", "read"=>"true", "task"=>"true", "email"=>"true", "write"=>"true", "delete"=>"true", "update"=>"true", "meeting"=>"true", "readAll"=>"true", "reshare"=>"true", "document"=>"true", "reassign"=>"true", "deleteAll"=>"true", "quotation"=>"false", "updateAll"=>"true"}, "companyAddress"=>nil, "companyCountry"=>nil, "companyWebsite"=>nil, "companyZipcode"=>nil, "createdViaName"=>"Universal API Key", "createdViaType"=>"API Key", "updatedViaName"=>"Webhook and Marketplace action log", "updatedViaType"=>"Workflow", "companyIndustry"=>nil, "forecastingType"=>nil, "requirementName"=>nil, "companyEmployees"=>nil, "actualClosureDate"=>nil, "addressCoordinate"=>nil, "expectedClosureOn"=>nil, "requirementBudget"=>nil, "meetingScheduledOn"=>nil, "companyBusinessType"=>nil, "pipelineStageReason"=>nil, "requirementCurrency"=>nil, "companyAnnualRevenue"=>nil, "latestActivityCreatedAt"=>"2024-12-30T09:02:13.478Z", "companyAddressCoordinate"=>nil},
                "entityId"=>"1", 
                "entityType"=>"lead", 
                "phoneNumber"=>{"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********2", "primary"=>"true", "dialCode"=>"+91"}, 
                "retryConfig"=>{"noOfTimes"=>"3", "timesRetried"=>"0"}
              }
            ).to_return(status: 200, body: '')
          
          expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_IN_PROGRESS.upcase)
          expect(Publishers::WhatsappCampaignActivityAbortedPublisher).to receive(:call).with(bulk_job, [321])
        end

        it 'should stop sending messages and should publish bulk job aborted event' do
          first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'retrying', message_status: SENDING, message_id: 321)

          allow(bulk_job).to receive(:aborting?).and_return(true)
          allow(bulk_job).to receive(:status).and_return('aborting')
          
          described_class.new(bulk_job).perform
          expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued failed])
        end

        it 'should update retrying record status to failed after aborting' do
          first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'retrying', message_status: SENDING, message_id: 321)

          allow(bulk_job).to receive(:aborting?).and_return(true)
          allow(bulk_job).to receive(:status).and_return('aborting')
          
          described_class.new(bulk_job).perform
          expect(bulk_job.records.pluck(:status)).to match_array(%w[failed queued])          
          expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued failed])
        end

        it 'should update bulk job retryable messages count after aborting' do
          first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'retrying', message_status: SENDING, message_id: 321)

          allow(bulk_job).to receive(:aborting?).and_return(true)
          allow(bulk_job).to receive(:status).and_return('aborting')

          described_class.new(bulk_job).perform
          expect(bulk_job.payload['retryConfig']['retryableMessages']).to eq(0)
        end
      end

      context 'when job is in retrying status' do
        let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'retrying', operation: WHATSAPP_MESSAGE_OPERATION, payload: payload_with_retry_config, entity: 'lead', category: CAMPAIGN_ACTION) }

        before do
          second_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1, status: STATUS_SUCCEEDED)

          create(:phone_number_wise_record_detail, record_id: second_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read, message_id: 123, status: STATUS_RETRYING, message_status: SENDING))

          allow(GetPresignedS3Url).to receive(:remote_url).and_return('https://dummy.aws.com/file.txt')
          allow(URI).to receive_message_chain(:open, :read).and_return('file content')

          stub_request(:put, "#{SERVICE_MESSAGE}/v1/messages/whatsapp/321/retry")
            .with(
              headers: {
                Authorization: "Bearer some-token"
              },
              body: {
                "activityId"=>"789", 
                "bulkJob"=>"true", 
                "campaignId"=>"456", 
                "dynamicTemplateMediaId"=>nil, 
                "entityData"=>{"id"=>"513753", "dnd"=>nil, "city"=>nil, "name"=>"MaskedPhone312 MaskedPhone312", "isNew"=>"false", "score"=>"0.0", "state"=>nil, "emails"=>nil, "source"=>"387685", "address"=>nil, "country"=>nil, "deleted"=>"false", "ownerId"=>"7990", "twitter"=>nil, "utmTerm"=>"Test 6", "version"=>"1", "zipcode"=>nil, "campaign"=>"387681", "facebook"=>nil, "lastName"=>"MaskedPhone312", "linkedIn"=>nil, "metaData"=>{"idNameStore"=>{"source"=>["Facebook", "LinkedIn", "Exhibition"], "country"=>nil, "ownerId"=>["Pansare Aditya", "mayur p", "st dp", "Addi user", "karan sharma"], "campaign"=>["Organic", "new-updated"], "pipeline"=>["start", "kak"], "timezone"=>nil, "createdBy"=>["Pansare Aditya", "st dp"], "updatedBy"=>["Pansare Aditya"], "importedBy"=>nil, "salutation"=>["Mr", "Mrs", "Miss-updated"], "convertedBy"=>nil, "pipelineStage"=>["Open", "Open"], "cfTestWorkflow"=>nil, "companyCountry"=>nil, "companyIndustry"=>nil, "companyEmployees"=>["5-9-updated", "10-19"], "cfMultiPickListTest"=>["2"], "cfPicklistFieldTest"=>["Tuesday"], "companyBusinessType"=>nil, "requirementCurrency"=>nil}}, "pipeline"=>nil, "products"=>[{"id"=>"22628", "name"=>"new product", "tenantId"=>"3440"}], "tenantId"=>"3440", "timezone"=>nil, "createdAt"=>"2024-12-30T09:02:13.025Z", "createdBy"=>"7638", "firstName"=>"MaskedPhone312", "photoUrls"=>nil, "subSource"=>"Test 1", "taskDueOn"=>nil, "updatedAt"=>"2024-12-30T09:02:13.475Z", "updatedBy"=>"7638", "utmMedium"=>"Test 4", "utmSource"=>"Test 2", "department"=>nil, "importedBy"=>nil, "salutation"=>nil, "utmContent"=>"Test 5", "companyCity"=>nil, "companyName"=>nil, "convertedAt"=>nil, "convertedBy"=>nil, "designation"=>nil, "utmCampaign"=>"Test 3", "companyState"=>nil, "createdViaId"=>"universal-app-key", "phoneNumbers"=>[{"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********2", "primary"=>"true", "dialCode"=>"+91"}, {"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********3", "primary"=>"true", "dialCode"=>"+91"}, {"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********4", "primary"=>"true", "dialCode"=>"+91"}], "updatedViaId"=>"WF_4735", "companyPhones"=>nil, "pipelineStage"=>nil, "recordActions"=>{"sms"=>"true", "call"=>"true", "note"=>"true", "read"=>"true", "task"=>"true", "email"=>"true", "write"=>"true", "delete"=>"true", "update"=>"true", "meeting"=>"true", "readAll"=>"true", "reshare"=>"true", "document"=>"true", "reassign"=>"true", "deleteAll"=>"true", "quotation"=>"false", "updateAll"=>"true"}, "companyAddress"=>nil, "companyCountry"=>nil, "companyWebsite"=>nil, "companyZipcode"=>nil, "createdViaName"=>"Universal API Key", "createdViaType"=>"API Key", "updatedViaName"=>"Webhook and Marketplace action log", "updatedViaType"=>"Workflow", "companyIndustry"=>nil, "forecastingType"=>nil, "requirementName"=>nil, "companyEmployees"=>nil, "actualClosureDate"=>nil, "addressCoordinate"=>nil, "expectedClosureOn"=>nil, "requirementBudget"=>nil, "meetingScheduledOn"=>nil, "companyBusinessType"=>nil, "pipelineStageReason"=>nil, "requirementCurrency"=>nil, "companyAnnualRevenue"=>nil, "latestActivityCreatedAt"=>"2024-12-30T09:02:13.478Z", "companyAddressCoordinate"=>nil},
                "entityId"=>"1", 
                "entityType"=>"lead", 
                "phoneNumber"=>{"id"=>"216106", "code"=>"IN", "type"=>"WORK", "value"=>"*********2", "primary"=>"true", "dialCode"=>"+91"}, 
                "retryConfig"=>{"noOfTimes"=>"3", "timesRetried"=>"0"},
                "whatsappTemplateId"=>"123"
              }
            ).to_return(status: 200, body: '')
          
          expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_IN_PROGRESS.upcase)

          expect(Publishers::CampaignActivityBulkJobStatusPublisher).to receive(:call).with(bulk_job: bulk_job, status: STATUS_RETRYING.upcase)
        end

        it 'should use retry whatsapp message api for individual message sending' do
          first_record = create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1, status: 'succeeded')
          create(:phone_number_wise_record_detail, record_id: first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: JSON.parse(file_fixture('entity_details_response.json').read), status: 'retrying', message_status: SENDING, message_id: 321)
          
          described_class.new(bulk_job).perform
          expect(bulk_job.records.pluck(:status)).to match_array(%w[succeeded succeeded])          
          expect(PhoneNumberWiseRecordDetail.pluck(:status)).to match_array(%w[queued retrying])
        end
      end
    end

    context 'with deal whatsapp message operation' do
      let(:deal_payload) {
        {
          "connectedAccount": {
            "id": 123,
            "name": "Whatsapp Account 1"
          },
          "whatsappTemplate": {
            "id": 123,
            "name": "Whatsapp Template 1"
          },
          "messageSendTo": [
            {
              "name": "Record's primary phone number",
              "type": "PRIMARY_PHONE_NUMBER"
            }
          ]
        }
      }

      let(:all_phone_numbers_payload) {
        {
          "connectedAccount": {
            "id": 123,
            "name": "Whatsapp Account 1"
          },
          "whatsappTemplate": {
            "id": 123,
            "name": "Whatsapp Template 1"
          },
          "messageSendTo": [
            {
              "name": "All phone numbers",
              "type": "ALL_PHONE_NUMBERS"
            }
          ]
        }
      }

      let(:user) { User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)) }
      let(:deal_bulk_job) { create(:bulk_job, user_id: user.id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: deal_payload, entity: DEAL) }

      context 'with successful message sending' do
        before do
          # Create records representing deal-contact relationships
          # parent_entity_id = deal_id, entity_id = contact_id
          @first_record = create(:record, bulk_job: deal_bulk_job, tenant_id: 99, parent_entity_id: 1001, entity_id: 2001)
          @second_record = create(:record, bulk_job: deal_bulk_job, tenant_id: 99, parent_entity_id: 1002, entity_id: 2002)

          # Create phone number wise record details with deal data as payload
          @deal_data_1 = {
            'id' => 1001,
            'name' => 'Test Deal 1',
            'estimatedValue' => { 'value' => 50000, 'currencyId' => 431 },
            'ownerId' => 123,
            'pipelineStage' => { 'id' => 1, 'name' => 'Open' },
            'company' => { 'id' => 5001, 'name' => 'Test Company 1' },
            'associatedContacts' => [{ 'id' => 2001, 'name' => 'Contact One' }]
          }

          @deal_data_2 = {
            'id' => 1002,
            'name' => 'Test Deal 2',
            'estimatedValue' => { 'value' => 75000, 'currencyId' => 431 },
            'ownerId' => 124,
            'pipelineStage' => { 'id' => 2, 'name' => 'Proposal' },
            'company' => { 'id' => 5002, 'name' => 'Test Company 2' },
            'associatedContacts' => [{ 'id' => 2002, 'name' => 'Contact Two' }]
          }

          create(:phone_number_wise_record_detail, record_id: @first_record.id, phone_number: '+************', phone_number_for_log: '+************', payload: @deal_data_1, phone_number_details: { 'id' => 216106, 'code' => 'IN', 'type' => 'WORK', 'value' => '*********2', 'primary' => true, 'dialCode' => '+91' })
          create(:phone_number_wise_record_detail, record_id: @second_record.id, phone_number: '+91*********3', phone_number_for_log: '+91*********3', payload: @deal_data_2, phone_number_details: { 'id' => 216107, 'code' => 'IN', 'type' => 'WORK', 'value' => '*********3', 'primary' => true, 'dialCode' => '+91' })

          stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
            .with(
              headers: {
                Authorization: "Bearer some-token"
              }
            )
            .to_return(status: 200, body: { 'id': 321 }.to_json, headers: {})
        end

        it 'should send whatsapp messages with complete deal data' do
          EntityAction::WhatsappMessage.new(deal_bulk_job).perform
          expect(deal_bulk_job.reload.status).to eq('completed')
          expect(deal_bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        end

        it 'should use contact as entityType and contact ID as entityId in the payload' do
          expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:execute_api_call) do |instance, phone_number_wise_entity_detail|
            payload = instance.send(:record_specific_payload, phone_number_wise_entity_detail)
            expect(payload[:entityType]).to eq(CONTACT) # Contact, not deal
            expect(payload[:templateEntity]).to eq(DEAL) # Deal type for template
            double(code: 200, body: { 'id': 321 }.to_json)
          end.at_least(:once)

          EntityAction::WhatsappMessage.new(deal_bulk_job).perform
        end

        it 'should pass complete deal data as entityData' do
          expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:execute_api_call) do |instance, phone_number_wise_entity_detail|
            payload = instance.send(:record_specific_payload, phone_number_wise_entity_detail)
            entity_data = payload[:entityData]

            expect(entity_data['name']).to include('Test Deal')
            expect(entity_data['ownerId']).to be_present
            expect(entity_data['estimatedValue']['value']).to be_present
            expect(entity_data['pipelineStage']['name']).to be_present
            expect(entity_data['company']['name']).to be_present
            expect(entity_data['associatedContacts']).to be_present
            double(code: 200, body: { 'id': 321 }.to_json)
          end.at_least(:once)

          EntityAction::WhatsappMessage.new(deal_bulk_job).perform
        end

        it 'should construct phone number details correctly for deals' do
          expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:execute_api_call) do |instance, phone_number_wise_entity_detail|
            payload = instance.send(:record_specific_payload, phone_number_wise_entity_detail)
            expect(payload[:phoneNumber]['dialCode']).to eq('+91')
            expect(payload[:phoneNumber]['value']).to include('*********')
            expect(payload[:phoneNumber]['type']).to eq('WORK')
            expect(payload[:phoneNumber]['primary']).to eq(true)
            double(code: 200, body: { 'id': 321 }.to_json)
          end.at_least(:once)

          EntityAction::WhatsappMessage.new(deal_bulk_job).perform
        end

        it 'should handle multiple deals with different data correctly' do
          call_count = 0
          expected_deals = [@deal_data_1, @deal_data_2]

          expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:execute_api_call) do |instance, phone_number_wise_entity_detail|
            payload = instance.send(:record_specific_payload, phone_number_wise_entity_detail)
            entity_data = payload[:entityData]

            expected_deal = expected_deals[call_count]
            expect(entity_data['name']).to eq(expected_deal['name'])
            expect(entity_data['estimatedValue']['value']).to eq(expected_deal['estimatedValue']['value'])
            expect(entity_data['ownerId']).to eq(expected_deal['ownerId'])

            call_count += 1
            double(code: 200, body: { 'id': 321 }.to_json)
          end.exactly(2).times

          EntityAction::WhatsappMessage.new(deal_bulk_job).perform
        end
      end

      context 'with primary vs all phone numbers option' do
        let(:all_phone_bulk_job) { create(:bulk_job, user_id: user.id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: all_phone_numbers_payload, entity: DEAL) }

        before do
          # Create a deal-contact relationship
          @record = create(:record, bulk_job: all_phone_bulk_job, tenant_id: 99, parent_entity_id: 1003, entity_id: 2003)

          @deal_data = {
            'id' => 1003,
            'name' => 'Multi-Phone Deal',
            'estimatedValue' => { 'value' => 100000, 'currencyId' => 431 },
            'ownerId' => 125,
            'associatedContacts' => [{ 'id' => 2003, 'name' => 'Multi-Phone Contact' }]
          }

          # Create multiple phone number records for the same deal-contact pair
          create(:phone_number_wise_record_detail, record_id: @record.id, phone_number: '+91*********4', phone_number_for_log: '+91*********4', payload: @deal_data, phone_number_details: { 'id' => 216108, 'code' => 'IN', 'type' => 'WORK', 'value' => '*********4', 'primary' => true, 'dialCode' => '+91' })
          create(:phone_number_wise_record_detail, record_id: @record.id, phone_number: '+91*********5', phone_number_for_log: '+91*********5', payload: @deal_data, phone_number_details: { 'id' => 216109, 'code' => 'IN', 'type' => 'WORK', 'value' => '*********5', 'primary' => true, 'dialCode' => '+91' })

          stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
            .to_return(status: 200, body: {}.to_json, headers: {})
        end

        it 'should send messages to all phone numbers when ALL_PHONE_NUMBERS is selected' do
          expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:execute_api_call).exactly(2).times.and_return(double(code: 200, body: { 'id': 321 }.to_json))

          EntityAction::WhatsappMessage.new(all_phone_bulk_job).perform
          expect(all_phone_bulk_job.reload.status).to eq('completed')
        end

        it 'should use same deal data for all phone numbers of the same deal' do
          call_count = 0
          expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:execute_api_call) do |instance, phone_number_wise_entity_detail|
            payload = instance.send(:record_specific_payload, phone_number_wise_entity_detail)
            entity_data = payload[:entityData]

            expect(entity_data['name']).to eq('Multi-Phone Deal')
            expect(entity_data['id']).to eq(1003)
            expect(payload[:templateEntityId]).to eq(1003)
            expect(payload[:templateEntity]).to eq(DEAL)

            call_count += 1
            double(code: 200, body: { 'id': 321 }.to_json)
          end.exactly(2).times

          EntityAction::WhatsappMessage.new(all_phone_bulk_job).perform
        end
      end

      context 'with error handling' do
        before do
          @error_record = create(:record, bulk_job: deal_bulk_job, tenant_id: 99, parent_entity_id: 1004, entity_id: 2004)
          @deal_data = {
            'id' => 1004,
            'name' => 'Error Deal',
            'estimatedValue' => { 'value' => 25000, 'currencyId' => 431 }
          }
          create(:phone_number_wise_record_detail, record_id: @error_record.id, phone_number: '+91*********6', phone_number_for_log: '+91*********6', payload: @deal_data)
        end

        it 'should handle API errors gracefully' do
          stub_request(:post, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-templates/123/send-bulk-message")
            .to_return(status: 400, body: { errorCode: '022017', message: 'API Error' }.to_json, headers: {})

          EntityAction::WhatsappMessage.new(deal_bulk_job).perform

          expect(deal_bulk_job.reload.status).to eq('completed')
          failed_record = deal_bulk_job.records.find_by(entity_id: 2004)
          expect(failed_record.status).to eq('failed')
        end
      end

      context 'with deals having no associated contacts' do
        let(:no_contact_bulk_job) { create(:bulk_job, user_id: user.id, tenant_id: 99, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, payload: deal_payload, entity: DEAL) }

        before do
          # Create a record with entity_id = 0 (no contact associated)
          @no_contact_record = create(:record, bulk_job: no_contact_bulk_job, tenant_id: 99, parent_entity_id: 1006, entity_id: 0)

          @deal_data = {
            'id' => 1006,
            'name' => 'No Contact Deal',
            'estimatedValue' => { 'value' => 30000, 'currencyId' => 431 },
            'associatedContacts' => []
          }
        end

        it 'should handle deals with no associated contacts' do
          # No phone number wise record details should be created for deals with no contacts
          expect(PhoneNumberWiseRecordDetail.where(record_id: @no_contact_record.id).count).to eq(0)

          EntityAction::WhatsappMessage.new(no_contact_bulk_job).perform
          expect(no_contact_bulk_job.reload.status).to eq('completed')
        end
      end
    end
  end
end
