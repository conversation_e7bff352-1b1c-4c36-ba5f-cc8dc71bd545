# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EntityAction::Update do
  describe '#perform' do
    before do
      allow_any_instance_of(Sidekiq::Queue).to receive(:count).and_return(0)
      allow(FileUploader).to receive(:upload).and_return(nil)
    end

    let(:bulk_job){ create(:bulk_job, user_id: User.create(id: 1, name: '<PERSON>', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued')}

    context 'with successful lead update operation' do
      before(:each) do
        create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)

        [1, 2, 3].each do |id|
          stub_request(:patch, "http://localhost:8086/v1/leads/bulk-patch/#{id}")
            .with(
              body: {
                'lead' => { 'ownerId' => 11 },
                'executeWorkflow' => false,
                'sendNotification' => false,
                'executeScoreRule' => false
              }.to_json,
              headers: {
                'content_type' => 'application/json',
                'authorization' => 'Bearer some-token'
              }
            )
            .to_return(status: 200, body: {
              "ownerId": 11
            }.to_json, headers: {})
        end
      end

      it 'should update the records' do
        EntityAction::Update.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.summary_file).not_to be_blank
        expect(bulk_job.error_summary_file).to be_blank
        expect(bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
      end
    end

    context 'with successful lead update operation' do
      context 'and the bulk job is aborted after some records' do
        before(:each) do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 4)

          [1, 2, 3].each do |id|
            stub_request(:patch, "http://localhost:8086/v1/leads/bulk-patch/#{id}")
              .with(
                body: {
                  'lead' => { 'ownerId' => 11 },
                  'executeWorkflow' => false,
                  'sendNotification' => false,
                  'executeScoreRule' => false
                }.to_json,
                headers: {
                  'content_type' => 'application/json',
                  'authorization' => 'Bearer some-token'
                }
              )
              .to_return(status: 200, body: {
                "ownerId": 11
              }.to_json, headers: {})
          end
        end

        it 'should update only the processed records and ignore other records' do
          bulk_job.aborting!

          EntityAction::Update.new(bulk_job.reload).perform
          expect(bulk_job.completed_at).to be < (Time.now)
          expect(bulk_job.summary_file).not_to be_blank
          expect(bulk_job.records.pluck(:status)).to match_array(%w[queued queued queued succeeded])
        end
      end

      context 'and the bulk job is paused after some records' do
        before(:each) do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 4)
        end

        it 'should update only the processed records and ignore other records' do
          bulk_job.paused!

          response = EntityAction::Update.new(bulk_job.reload).perform
          expect(bulk_job.reload.status).to eq('paused')
          expect(bulk_job.records.pluck(:status)).to match(%w[queued queued queued queued])
          expect(response).to eq({ paused: true })
        end
      end
    end

    context 'with successful contact update operation' do
      let(:contact_bulk_job) do
        create(:bulk_job, entity: CONTACT,
                          user_id: User.create(id: 11, name: 'Tony Stark', token: 'some-token',
                                               tenant: Tenant.create(id: 199)).id, tenant_id: 199, status: 'queued')
      end

      before(:each) do
        create(:record, bulk_job: contact_bulk_job, tenant_id: 199, entity_id: 1)
        create(:record, bulk_job: contact_bulk_job, tenant_id: 199, entity_id: 2)
        create(:record, bulk_job: contact_bulk_job, tenant_id: 199, entity_id: 3)

        [1, 2, 3].each do |id|
          stub_request(:patch, "http://localhost:8086/v1/contacts/#{id}")
            .with(
              body: {
                'contact' => { 'ownerId' => 11 },
                'executeWorkflow' => false,
                'sendNotification' => false,
                'executeScoreRule' => false
              }.to_json,
              headers: {
                'content_type' => 'application/json',
                'authorization' => 'Bearer some-token'
              }
            )
            .to_return(status: 200, body: {
              "ownerId": 11
            }.to_json, headers: {})
        end
      end

      it 'should update the records' do
        EntityAction::Update.new(contact_bulk_job).perform
        expect(contact_bulk_job.reload.status).to eq('completed')
        expect(contact_bulk_job.completed_at).to be < (Time.now)
        expect(contact_bulk_job.started_at).to be < (contact_bulk_job.completed_at)
        expect(contact_bulk_job.summary_file).not_to be_blank
        expect(contact_bulk_job.error_summary_file).to be_blank
        expect(contact_bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(contact_bulk_job.successful).to be(3)
        expect(contact_bulk_job.failure).to be(0)
      end
    end

    context 'with successful deal update operation' do
      let(:deal_bulk_job) do
        create(:bulk_job, entity: 'deal',
                          user_id: User.create(id: 1, name: 'Tony Stark', token: 'some-token', tenant: Tenant.create(id: 99)).id, tenant_id: 99, status: 'queued')
      end
      before(:each) do
        create(:record, bulk_job: deal_bulk_job, tenant_id: 99, entity_id: 1)
        create(:record, bulk_job: deal_bulk_job, tenant_id: 99, entity_id: 2)
        create(:record, bulk_job: deal_bulk_job, tenant_id: 99, entity_id: 3)

        [1, 2, 3].each do |id|
          stub_request(:patch, "http://localhost:8090/v1/deals/bulk-patch/#{id}")
            .with(
              body: {
                'deal' => { 'ownerId' => 11 },
                'executeWorkflow' => false,
                'sendNotification' => false
              }.to_json,
              headers: {
                'content_type' => 'application/json',
                'authorization' => 'Bearer some-token'
              }
            )
            .to_return(status: 200, body: {
              "ownerId": 11
            }.to_json, headers: {})
        end
      end

      it 'should update the records' do
        EntityAction::Update.new(deal_bulk_job).perform
        expect(deal_bulk_job.reload.status).to eq('completed')
        expect(deal_bulk_job.completed_at).to be < (Time.now)
        expect(deal_bulk_job.started_at).to be < (deal_bulk_job.completed_at)
        expect(deal_bulk_job.summary_file).not_to be_blank
        expect(deal_bulk_job.error_summary_file).to be_blank
        expect(deal_bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(deal_bulk_job.successful).to be(3)
        expect(deal_bulk_job.failure).to be(0)
      end
    end

    context 'with successful company update operation' do
      let(:company_bulk_job) do
        create(:bulk_job, entity: COMPANY,
                          user_id: User.create(id: 11, name: 'Tony Stark', token: 'some-token',
                                               tenant: Tenant.create(id: 199)).id, tenant_id: 199, status: 'queued')
      end

      before(:each) do
        create(:record, bulk_job: company_bulk_job, tenant_id: 199, entity_id: 1)
        create(:record, bulk_job: company_bulk_job, tenant_id: 199, entity_id: 2)
        create(:record, bulk_job: company_bulk_job, tenant_id: 199, entity_id: 3)

        [1, 2, 3].each do |id|
          stub_request(:patch, "http://localhost:3000/v1/companies/bulk-patch/#{id}")
            .with(
              body: {
                'company' => { 'ownerId' => 11 },
                'sendNotification' => false,
              }.to_json,
              headers: {
                'content_type' => 'application/json',
                'authorization' => 'Bearer some-token'
              }
            )
            .to_return(status: 200, body: {
              "ownerId": 11
            }.to_json, headers: {})
        end
      end

      it 'should update the records' do
        EntityAction::Update.new(company_bulk_job).perform
        expect(company_bulk_job.reload.status).to eq('completed')
        expect(company_bulk_job.completed_at).to be < (Time.now)
        expect(company_bulk_job.started_at).to be < (company_bulk_job.completed_at)
        expect(company_bulk_job.summary_file).not_to be_blank
        expect(company_bulk_job.error_summary_file).to be_blank
        expect(company_bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(company_bulk_job.successful).to be(3)
        expect(company_bulk_job.failure).to be(0)
      end
    end

    context 'with successful task update operation' do
      let(:task_bulk_job) do
        create(
          :bulk_job,
          entity: 'task', user_id: User.create(
            id: 1, name: 'Tony Stark',
            token: 'some-token', tenant: Tenant.create(id: 99)
          ).id,
          execute_workflow: true,
          tenant_id: 99, status: 'queued',
          filters: {
            page: 0, size: 10, 'sort': 'updatedAt,desc',
            'jsonRule' => {
              'rules' => [
                {
                  'id' => 'assignedTo', 'type' => 'long',
                  'field' => 'assignedTo', 'value' => 777, 'operator' => 'equal'
                }
              ]
            }
          },
          payload: { 'assignedTo' => 777 }
        )
      end
      before(:each) do
        create(:record, bulk_job: task_bulk_job, tenant_id: 99, entity_id: 1)
        create(:record, bulk_job: task_bulk_job, tenant_id: 99, entity_id: 2)
        create(:record, bulk_job: task_bulk_job, tenant_id: 99, entity_id: 3)

        [1, 2, 3].each do |id|
          stub_request(:patch, "http://localhost:8087/v1/tasks/#{id}")
            .with(
              body: {
                'task' => { 'assignedTo' => 777 },
                'executeWorkflow' => true,
                'sendNotification' => false
              }.to_json,
              headers: {
                'content_type' => 'application/json',
                'authorization' => 'Bearer some-token'
              }
            )
            .to_return(status: 200, body: {
              "assignedTo": 777
            }.to_json, headers: {})
        end
      end

      it 'should update the records' do
        EntityAction::Update.new(task_bulk_job).perform
        expect(task_bulk_job.reload.status).to eq('completed')
        expect(task_bulk_job.completed_at).to be < (Time.now)
        expect(task_bulk_job.started_at).to be < (task_bulk_job.completed_at)
        expect(task_bulk_job.summary_file).not_to be_blank
        expect(task_bulk_job.error_summary_file).to be_blank
        expect(task_bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(task_bulk_job.successful).to be(3)
        expect(task_bulk_job.failure).to be(0)
      end
    end

    context 'with successful user update operation' do
      let(:user_bulk_job) do
        create(
          :bulk_job,
          entity: 'user', user_id: User.create(
            id: 1, name: 'Tony Stark',
            token: 'some-token', tenant: Tenant.create(id: 99)
          ).id,
          execute_workflow: true,
          tenant_id: 99, status: 'queued',
          payload: { 'firstName' => "Updated" }
        )
      end
      before(:each) do
        create(:record, bulk_job: user_bulk_job, tenant_id: 99, entity_id: 1)
        create(:record, bulk_job: user_bulk_job, tenant_id: 99, entity_id: 2)
        create(:record, bulk_job: user_bulk_job, tenant_id: 99, entity_id: 3)

        [1, 2, 3].each do |id|
          stub_request(:patch, "http://localhost:8081/v1/users/#{id}")
            .with(
              body: {
                'user' => { 'firstName' => "Updated" }
              }.to_json,
              headers: {
                'content_type' => 'application/json',
                'authorization' => 'Bearer some-token'
              }
            )
            .to_return(status: 200, body: {
              "id": id
            }.to_json, headers: {})
        end
      end

      it 'should update the records' do
        EntityAction::Update.new(user_bulk_job).perform
        expect(user_bulk_job.reload.status).to eq('completed')
        expect(user_bulk_job.completed_at).to be < (Time.now)
        expect(user_bulk_job.started_at).to be < (user_bulk_job.completed_at)
        expect(user_bulk_job.summary_file).not_to be_blank
        expect(user_bulk_job.error_summary_file).to be_blank
        expect(user_bulk_job.records.map(&:status).all?('succeeded')).to be_truthy
        expect(user_bulk_job.successful).to be(3)
        expect(user_bulk_job.failure).to be(0)
      end
    end

    context 'with failed record operation' do
      before(:each) do
        bulk_job.update(payload: { 'salutation' => 11 })
        create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
        create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
        create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)
        [1, 3].each do |id|
          stub_request(:patch, "http://localhost:8086/v1/leads/bulk-patch/#{id}")
            .with(
              body: {
                'lead' => { 'salutation' => 11 },
                'executeWorkflow' => false,
                'sendNotification' => false,
                'executeScoreRule' => false
              }.to_json,
              headers: {
                'content_type' => 'application/json',
                'authorization' => 'Bearer some-token'
              }
            )
            .to_return(status: 200, body: {
              "salutation": 11
            }.to_json, headers: {})
        end
        stub_request(:patch, 'http://localhost:8086/v1/leads/bulk-patch/2')
          .with(
            body: {
              'lead' => { 'salutation' => 11 },
              'executeWorkflow' => false,
              'sendNotification' => false,
              'executeScoreRule' => false
            }.to_json,
            headers: {
              'content_type' => 'application/json',
              'authorization' => 'Bearer some-token'
            }
          )
          .to_return(status: 400, body: {
            "code": '000009',
            "message": 'Uhoh! Your data is invalid. Please check and try again.',
            "errorDetails": [
              {
                "field": 'salutation',
                "message": '#Uhoh! Looks like the entered value (100) is not valid for the Field. Please choose from the available list.'
              }
            ]
          }.to_json, headers: {})
      end

      it 'should update the records' do
        EntityAction::Update.new(bulk_job).perform
        expect(bulk_job.reload.status).to eq('completed')
        expect(bulk_job.completed_at).to be < (Time.now)
        expect(bulk_job.started_at).to be < (bulk_job.completed_at)
        expect(bulk_job.records.where(entity_id: [1, 3]).map(&:status).all?('succeeded')).to be_truthy
        err_record = bulk_job.records.where(entity_id: 2).first
        expect(err_record.status).to eq('failed')
        expect(bulk_job.summary_file).not_to be_blank
        expect(bulk_job.error_summary_file).not_to be_blank
        expect(err_record.error_message).to eq('salutation: #Uhoh! Looks like the entered value (100) is not valid for the Field. Please choose from the available list.')
        expect(bulk_job.successful).to be(2)
        expect(bulk_job.failure).to be(1)
      end
    end

    context 'with completed or aborted job' do
      before(:each) do
        create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
        stub_request(:patch, 'http://localhost:8086/v1/leads/bulk-patch/1')
          .with(
            body: {
              'lead' => { 'ownerId' => 11 },
              'executeWorkflow' => false,
              'sendNotification' => false,
              'executeScoreRule' => false
            }.to_json,
            headers: {
              'content_type' => 'application/json',
              'authorization' => 'Bearer some-token'
            }
          )
          .to_return(status: 200, body: {
            "ownerId": 11
          }.to_json, headers: {})
      end

      it 'should should not update records if job completed' do
        bulk_job.completed!
        EntityAction::Update.new(bulk_job).perform
        expect(WebMock).not_to have_requested(:patch, 'http://localhost:8086/v1/leads/bulk-patch/1')
      end

      it 'should should not update records if job aborted' do
        bulk_job.aborted!
        EntityAction::Update.new(bulk_job).perform
        expect(WebMock).not_to have_requested(:patch, 'http://localhost:8086/v1/leads/bulk-patch/1')
      end
    end
  end
end
