require 'rails_helper'

RSpec.describe EntityAction::FetchNotes do
  let(:tenant){ create(:tenant) }
  let(:user){ create(:user, name: '<PERSON>', tenant: tenant)}

  before do
    Thread.current[:user] = user
    Thread.current[:token] = get_test_jwt(user.id, user.tenant_id)
    user.update(token: Thread.current[:token])
  end

  describe '#call' do
    context 'Gets notes from productivity' do
      before do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: NOTE, operation: DELETE_OPERATION)
        filters = @bulk_job.filters
        stub_request(:post, SERVICE_PRODUCTIVITY + "/v1/notes/search?page=0&size=1000").
          with(
            body: { fields: ['id', 'relations'], jsonRule: filters['jsonRule']}.to_json,
            headers: {
              "Authorization" => "Bearer #{ user.token }"
            }).
            to_return(status: 200, body: file_fixture('productivity_notes_page_1_response.json').read, headers: {})
        stub_request(:post, SERVICE_PRODUCTIVITY + "/v1/notes/search?page=1&size=1000").
          with(
            body: { fields: ['id', 'relations'], jsonRule: filters['jsonRule']}.to_json,
            headers: {
              "Authorization" => "Bearer #{ user.token }"
            }).
            to_return(status: 200, body: file_fixture('productivity_notes_page_2_response.json').read, headers: {})

        EntityAction::FetchNotes.new(@bulk_job.id).call
      end

      it 'creates 6 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 6
      end

      it 'matches entity ids on records' do
        records = [
          [1, { "lead_id" => 1 }],
          [2, { "deal_id" => 1, "lead_id" => 2 }],
          [3, { "contact_id" => 1 }],
          [4, { "task_id" => 1 }],
          [5, { "call_id" => 1 }],
          [6, { "meeting_id" => 1 }]
        ]
        expect(Record.all.pluck(:entity_id, :relations)).to match(records)
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to eq 6
      end
    end

    context 'with invalid data' do
      it 'should throw invalid data error when id is wrong' do
        expect{ EntityAction::FetchNotes.new(111).call }.to raise_error(ExceptionHandler::InvalidDataError, "025003||Couldn't find BulkJob with 'id'=111")
      end
    end

    context 'with api errors' do
      before do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: NOTE, operation: DELETE_OPERATION)
      end

      context 'when notes api return 404' do
        before do
          stub_request(:post, SERVICE_PRODUCTIVITY + "/v1/notes/search?page=0&size=1000").
            with(
              body: { fields: ['id', 'relations'], jsonRule: @bulk_job.filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }).
            to_return(status: 404, body: { errorCode: '01603008' }.to_json)
        end

        it 'should throw invalid data error' do
          expect{ EntityAction::FetchNotes.new(@bulk_job.id).call }.to raise_error(ExceptionHandler::InvalidDataError, "025003")
        end
      end

      context 'when notes api return 500' do
        before do
          stub_request(:post, SERVICE_PRODUCTIVITY + "/v1/notes/search?page=0&size=1000").
            with(
              body: { fields: ['id', 'relations'], jsonRule: @bulk_job.filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }).
            to_return(status: 500, body: { errorCode: '01603008' }.to_json)
        end

        it 'should throw internal server error' do
          expect{ EntityAction::FetchNotes.new(@bulk_job.id).call }.to raise_error(ExceptionHandler::InternalServerError, "025004")
        end
      end

      context 'when notes api return 400' do
        before do
          stub_request(:post, SERVICE_PRODUCTIVITY + "/v1/notes/search?page=0&size=1000").
            with(
              body: { fields: ['id', 'relations'], jsonRule: @bulk_job.filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }).
            to_return(status: 400, body: { errorCode: '01603008' }.to_json)
        end

        it 'should throw invalid data error' do
          expect{ EntityAction::FetchNotes.new(@bulk_job.id).call }.to raise_error(ExceptionHandler::InvalidDataError, "025003")
        end
      end
    end
  end
end
