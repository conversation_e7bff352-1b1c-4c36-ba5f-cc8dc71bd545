require 'rails_helper'

RSpec.describe EntityAction::Fetch do
  let(:user){ User.create(id: 111, name: '<PERSON>', tenant: Tenant.create(id: 9999)) }
  let(:entity_details_list) do
    [
      {
        entityId: 513_753,
        entityName: 'MaskedPhone312 MaskedPhone312',
        phoneNumber: { 'code' => 'IN', 'dialCode' => '+91', 'id' => 216_106, 'type' => 'WORK', 'value' => '8899223312',
                      'primary' => true
        }
      },
      {
        entityId: 513_752,
        entityName: 'test test',
        phoneNumber: {
          'code' => 'IN', 'dialCode' => '+91', 'id' => 216_105, 'type' => 'MOBILE', 'value' => '8308429939', 'primary' => true
        }
      },
      {
        entityId: 513_751,
        entityName: 'MaskedPhone311 MaskedPhone311',
        phoneNumber: {
          'code' => 'IN', 'dialCode' => '+91', 'id' => 216_104, 'type' => 'WORK', 'value' => '8899223311', 'primary' => true
        }
      },
      { 
        entityId: 513_750, 
        entityName: ' ', 
        phoneNumber: nil, 
        error: 'No phone numbers found'
      },
      {
        entityId: 513_749,
        entityName: '123 ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_748,
        entityName: 'ttt ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_747,
        entityName: '123 ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_746,
        entityName: ' ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_745,
        entityName: ' ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_744,
        entityName: ' ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 512_887,
        entityName: 'dfdfg dfgfdf',
        phoneNumber: { 'code' => 'IN', 'dialCode' => '+91', 'id' => 216_065, 'type' => 'MOBILE', 'value' => '9988223311', 'primary' => true
        }
      },
      {
        entityId: 513_620,
        entityName: 'ssdfCAPI dsfsdfsdfsddfg MaskedPhone033',
        phoneNumber: { 'code' => 'IN', 'dialCode' => '+91', 'id' => 216_055, 'type' => 'WORK', 'value' => '9082408033', 'primary' => true
        }
      },
      {
        entityId: 513_636,
        entityName: 'CAPI CAPI rert',
        phoneNumber: { 'code' => 'IN', 'dialCode' => '+91', 'id' => 216_061, 'type' => 'MOBILE', 'value' => '8833992211', 'primary' => false
        }
      },
      {
        entityId: 513_636,
        entityName: 'CAPI CAPI rert',
        phoneNumber: { 'code' => 'IN', 'dialCode' => '+91', 'id' => 216_060, 'type' => 'MOBILE', 'value' => '8308429939', 'primary' => true
        }
      },
      {
        entityId: 513_632,
        entityName: 'tes 23t tes 23t',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_635,
        entityName: 'test test',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_631,
        entityName: 'test test',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 513_630,
        entityName: 'test test',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 512_893,
        entityName: ' ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      },
      {
        entityId: 512_884,
        entityName: ' test email conversation',
        phoneNumber: { 'code' => 'IN', 'dialCode' => '+91', 'id' => 215_345, 'type' => 'MOBILE', 'value' => '8308429939', 'primary' => true
        }
      },
      {
        entityId: 512_892,
        entityName: ' ',
        phoneNumber: nil,
        error: 'No phone numbers found'
      }
    ]
  end

  before do
    Thread.current[:user] = user
    Thread.current[:token] = get_test_jwt(user.id, user.tenant_id)
    user.update(token: Thread.current[:token])
  end

  describe '#call' do
    context 'Gets lead data from search service' do
      before(:each) do
        content = []
        1008.times do |i|
          content << { "id": i }
        end
        @data = {
          "content": content,
          "last": true,
          "totalPages": 1,
          "totalElements": 6,
          "sort": [
            {
              "direction": "DESC",
              "property": "updatedAt",
              "ignoreCase": false,
              "nullHandling": "NATIVE",
              "descending": true,
              "ascending": false
            }
          ],
          "first": true,
          "numberOfElements": 6,
          "size": 10,
          "number": 0
        }
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: LEAD)
        filters = @bulk_job.filters
        stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}").
          with(
            body: { fields: ['id'], jsonRule: filters['jsonRule']}.to_json,
            headers: {
              "Authorization" => "Bearer #{ user.token }"
            }).
            to_return(status: 200, body: @data.to_json, headers: {})
            EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates 1008 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 1008
      end

      it 'matches entity ids on records' do
        expect(Record.all.pluck(:entity_id)).to match(@data[:content].map{|e| e[:id]})
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to eq 1008
      end
    end

    context 'Gets contact data from search service' do
      before(:each) do
        content = []
        1008.times do |i|
          content << { "id": i }
        end
        @data = {
          "content": content,
          "last": true,
          "totalPages": 1,
          "totalElements": 6,
          "sort": [
            {
              "direction": "DESC",
              "property": "updatedAt",
              "ignoreCase": false,
              "nullHandling": "NATIVE",
              "descending": true,
              "ascending": false
            }
          ],
          "first": true,
          "numberOfElements": 6,
          "size": 10,
          "number": 0
        }

        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: CONTACT)
        filters = @bulk_job.filters
        stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}").
          with(
            body: { fields: ['id'], jsonRule: filters['jsonRule']}.to_json,
            headers: {
              "Authorization" => "Bearer #{ user.token }"
            }).
            to_return(status: 200, body: @data.to_json, headers: {})
            EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates 1008 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 1008
      end

      it 'matches entity ids on records' do
        expect(Record.all.pluck(:entity_id)).to match(@data[:content].map{|e| e[:id]})
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to eq 1008
      end
    end

    context 'when bulk job operation is whatsapp message operation' do
      context 'when entity type is lead' do
        context 'when masking is not enabled on the lead phone number field' do
          before do
            @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: LEAD, operation: WHATSAPP_MESSAGE_OPERATION)
            filters = @bulk_job.filters

            stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
              headers: {
                'Authorization' => "Bearer #{ user.token }"
              }
            ).to_return(
              status: 200,
              body: [].to_json,
              headers: {}
            )

            stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})
            EntityAction::Fetch.new(@bulk_job.id).call
          end

          it 'creates 20 records' do
            expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 20
          end

          it 'creates 8 phone_number_wise_record_details' do
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id).pluck(:id)).count).to eq 8
          end

          it 'updates number_of_records on bulk job' do
            expect(@bulk_job.reload.number_of_records).to eq 20
          end

          it 'stores entities total phone numbers count on the bulk job' do
            expect(@bulk_job.reload.payload['totalPhoneNumbers']).to eq 8
          end

          it 'stores unmasked data in the phone number wise record details' do
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number)).to eq ["+918899223312", "+918308429939", "+918899223311", "+919988223311", "+919082408033", "+918833992211", "+918308429939", "+918308429939"]
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number_for_log)).to eq ["+918899223312", "+918308429939", "+918899223311", "+919988223311", "+919082408033", "+918833992211", "+918308429939", "+918308429939"]
          end
        end

        context 'when masking is enabled on lead phone number' do
          context 'when phone numbers are present on the any of the entity' do
            before do
              @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: LEAD, operation: WHATSAPP_MESSAGE_OPERATION)
              filters = @bulk_job.filters

              stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer #{ user.token }"
                }
              ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})

              stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
                headers: {
                  'Authorization' => "Bearer #{ user.token }"
                }
              ).to_return(
                status: 200,
                body: [
                  {
                    type: 'PHONE',
                    name: 'phoneNumbers',
                    description: 'Lead Phone',
                    filterable: true,
                    sortable: true,
                    required: false,
                    important: true,
                    pickLists: nil,
                    fieldConfigurations: [
                        {
                          id: nil,
                          type:'MASKING',
                          configuration: {
                            enabled:true,
                            profileIds: [1,2,3]
                          }
                        }
                      ]
                  }
                ].to_json,
                headers: {}
              )

              allow_any_instance_of(GenerateToken).to receive(:call).and_return("admin-token-without-pid")
              stub_request(:post, SERVICE_SEARCH + "/v2/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer admin-token-without-pid"
                }
              ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})
              EntityAction::Fetch.new(@bulk_job.id).call
            end

            it 'stores entities total phone numbers count on the bulk job' do
              expect(@bulk_job.reload.payload['totalPhoneNumbers']).to eq 8
            end

            it 'stores masked data in the phone number wise record details' do
              expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number)).to eq ["+918899223312", "+918308429939", "+918899223311", "+919988223311", "+919082408033", "+918833992211", "+918308429939", "+918308429939"]
              expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number_for_log)).to eq ["+91****312", "+91****939", "+91****311", "+91****311", "+91****033", "+91****211", "+91****939", "+91****939"]
            end
          end

          context 'when no phone numbers are present on the selected entities' do
             before do
              @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: LEAD, operation: WHATSAPP_MESSAGE_OPERATION)
              filters = @bulk_job.filters

              stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer #{ user.token }"
                }
              ).to_return(status: 200, body: file_fixture('entity_details_without_phone_numbers.json').read, headers: {})

              stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
                headers: {
                  'Authorization' => "Bearer #{ user.token }"
                }
              ).to_return(
                status: 200,
                body: [
                  {
                    type: 'PHONE',
                    name: 'phoneNumbers',
                    description: 'Lead Phone',
                    filterable: true,
                    sortable: true,
                    required: false,
                    important: true,
                    pickLists: nil,
                    fieldConfigurations: [
                        {
                          id: nil,
                          type:'MASKING',
                          configuration: {
                            enabled:true,
                            profileIds: [1,2,3]
                          }
                        }
                      ]
                  }
                ].to_json,
                headers: {}
              )

              allow_any_instance_of(GenerateToken).to receive(:call).and_return("admin-token-without-pid")
              stub_request(:post, SERVICE_SEARCH + "/v2/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer admin-token-without-pid"
                }
              ).to_return(status: 200, body: file_fixture('entity_details_without_phone_numbers.json').read, headers: {})
              EntityAction::Fetch.new(@bulk_job.id).call
            end

            it 'stores entities total phone numbers count on the bulk job and it should be 0' do
              expect(@bulk_job.reload.payload['totalPhoneNumbers']).to eq 0
            end

            it 'does not stores data in the phone number wise record details' do
              expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number)).to eq []
              expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number_for_log)).to eq []
              expect(PhoneNumberWiseRecordDetail.count).to eq(0)
            end
          end
        end

        context 'when bulk job category is campaign_action' do
          before do
            @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: LEAD, operation: WHATSAPP_MESSAGE_OPERATION, category: CAMPAIGN_ACTION)
            filters = @bulk_job.filters

            stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
              headers: {
                'Authorization' => "Bearer #{ user.token }"
              }
            ).to_return(
              status: 200,
              body: [].to_json,
              headers: {}
            )

            stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?page=0&size=1")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})

            stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=20")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})
          end

          it 'should fetch all entites and publishes CampaignActivityBulkJobStarted event' do
            expect(Publishers::CampaignActivityBulkJobStartedPublisher).to receive(:call).with(@bulk_job, entity_details_list).once

            EntityAction::Fetch.new(@bulk_job.id).call

            expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 20
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id).pluck(:id)).count).to eq 8
            expect(@bulk_job.reload.payload['totalPhoneNumbers']).to eq 8
            expect(@bulk_job.reload.number_of_records).to eq 20
          end
        end

        context 'when sort value is not present in the bulk job filters' do
          before do
            @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: LEAD, operation: WHATSAPP_MESSAGE_OPERATION, category: CAMPAIGN_ACTION)
            @bulk_job.filters['sort'] = nil
            @bulk_job.save
            filters = @bulk_job.filters

            stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
              headers: {
                'Authorization' => "Bearer #{ user.token }"
              }
            ).to_return(
              status: 200,
              body: [].to_json,
              headers: {}
            )

            stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?page=0&size=1")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})

            stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=id,asc&page=#{filters['page']}&size=20")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})
          end

          it 'should apply default sorting on id with asc order' do
            expect(Publishers::CampaignActivityBulkJobStartedPublisher).to receive(:call).with(@bulk_job, entity_details_list).once

            EntityAction::Fetch.new(@bulk_job.id).call

            expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 20
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id).pluck(:id)).count).to eq 8
            expect(@bulk_job.reload.payload['totalPhoneNumbers']).to eq 8
            expect(@bulk_job.reload.number_of_records).to eq 20
          end
        end
      end

      context 'when entity type is contact' do
        context 'when masking is not enabled on the contact phone number field' do
          before do
            @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: CONTACT, operation: WHATSAPP_MESSAGE_OPERATION)
            filters = @bulk_job.filters

            stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/contact/masked-fields").with(
              headers: {
                'Authorization' => "Bearer #{ user.token }"
              }
            ).to_return(
              status: 200,
              body: [].to_json,
              headers: {}
            )

            stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})
            EntityAction::Fetch.new(@bulk_job.id).call
          end

          it 'creates 20 records' do
            expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq(20)
          end

          it 'creates 8 phone_number_wise_record_details' do
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id).pluck(:id)).count).to eq 8
          end

          it 'updates number_of_records on bulk_jon' do
            expect(@bulk_job.reload.number_of_records).to eq 20
          end

          it 'stores unmasked phone number data' do
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number)).to eq ["+918899223312", "+918308429939", "+918899223311", "+919988223311", "+919082408033", "+918833992211", "+918308429939", "+918308429939"]
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number_for_log)).to eq ["+918899223312", "+918308429939", "+918899223311", "+919988223311", "+919082408033", "+918833992211", "+918308429939", "+918308429939"]
          end
        end

        context 'when masking is enabled on the contact phone number field' do
          before do
            @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: CONTACT, operation: WHATSAPP_MESSAGE_OPERATION)
            filters = @bulk_job.filters

            stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})

            stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/contact/masked-fields").with(
              headers: {
                'Authorization' => "Bearer #{ user.token }"
              }
            ).to_return(
              status: 200,
              body: [
                {
                  type: 'PHONE',
                  name: 'phoneNumbers',
                  description: 'Contact Phone',
                  filterable: true,
                  sortable: true,
                  required: false,
                  important: true,
                  pickLists: nil,
                  fieldConfigurations: [
                      {
                        id: nil,
                        type:'MASKING',
                        configuration: {
                          enabled:true,
                          profileIds: [1,2,3]
                        }
                      }
                    ]
                }
              ].to_json,
              headers: {}
            )

            allow_any_instance_of(GenerateToken).to receive(:call).and_return("admin-token-without-pid")
            stub_request(:post, SERVICE_SEARCH + "/v2/search/contact?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer admin-token-without-pid"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})
            EntityAction::Fetch.new(@bulk_job.id).call
          end

          it 'stores entities total phone numbers count on the bulk job' do
            expect(@bulk_job.reload.payload['totalPhoneNumbers']).to eq 8
          end

          it 'stores masked data in the phone number wise record details' do
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number)).to eq ["+918899223312", "+918308429939", "+918899223311", "+919988223311", "+919082408033", "+918833992211", "+918308429939", "+918308429939"]
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id)).pluck(:phone_number_for_log)).to eq ["+91****312", "+91****939", "+91****311", "+91****311", "+91****033", "+91****211", "+91****939", "+91****939"]
          end
        end

        context 'when bulk job category is campaign_action' do
          before do
            @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: CONTACT, operation: WHATSAPP_MESSAGE_OPERATION, category: CAMPAIGN_ACTION)
            filters = @bulk_job.filters

            stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/contact/masked-fields").with(
              headers: {
                'Authorization' => "Bearer #{ user.token }"
              }
            ).to_return(
              status: 200,
              body: [].to_json,
              headers: {}
            )

            stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=1")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})

            stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=#{filters['sort']}&page=#{filters['page']}&size=20")
            .with(
              body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
              headers: {
                "Authorization" => "Bearer #{ user.token }"
              }
            ).to_return(status: 200, body: file_fixture('fetch_entity_details_response.json').read, headers: {})
          end

          it 'should fetch all entites and publishes CampaignActivityBulkJobStarted event' do
            expect(Publishers::CampaignActivityBulkJobStartedPublisher).to receive(:call).once

            EntityAction::Fetch.new(@bulk_job.id).call

            expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 20
            expect(PhoneNumberWiseRecordDetail.where(record_id: Record.where(bulk_job_id: @bulk_job.id).pluck(:id)).count).to eq 8
            expect(@bulk_job.reload.payload['totalPhoneNumbers']).to eq 8
            expect(@bulk_job.reload.number_of_records).to eq 20
          end
        end
      end

      context 'when bulk job operation is EMAIL_OPERATION and category is campaign_action' do
        let(:email_entity_details_list) do
          [
            {
              entityId: 513753,
              entityName: 'John Doe',
              email: {
                type: 'OFFICE',
                value: '<EMAIL>',
                primary: true
              }
            },
            {
              entityId: 513753,
              entityName: 'John Doe',
              email: {
                type: 'PERSONAL',
                value: '<EMAIL>',
                primary: false
              }
            },
            {
              entityId: 513752,
              entityName: 'Jane Smith',
              email: {
                type: 'OFFICE',
                value: '<EMAIL>',
                primary: true
              }
            },
            {
              entityId: 513751,
              entityName: 'Bob Wilson',
              email: nil,
              error: 'No emails found'
            },
            {
              entityId: 513750,
              entityName: 'Alice Johnson',
              email: nil,
              error: 'No emails found'
            },
            {
              entityId: 513749,
              entityName: 'Charlie Brown',
              email: {
                type: 'PERSONAL',
                value: '<EMAIL>',
                primary: false
              }
            },
            {
              entityId: 513749,
              entityName: 'Charlie Brown',
              email: {
                type: 'OFFICE',
                value: '<EMAIL>',
                primary: true
              }
            }
          ]
        end

        let(:primary_email_entity_details_list) do
          [
            {
              entityId: 513753,
              entityName: 'John Doe',
              email: {
                type: 'OFFICE',
                value: '<EMAIL>',
                primary: true
              }
            },
            {
              entityId: 513752,
              entityName: 'Jane Smith',
              email: {
                type: 'OFFICE',
                value: '<EMAIL>',
                primary: true
              }
            },
            {
              entityId: 513751,
              entityName: 'Bob Wilson',
              email: nil,
              error: 'No emails found'
            },
            {
              entityId: 513750,
              entityName: 'Alice Johnson',
              email: nil,
              error: 'No emails found'
            },
            {
              entityId: 513749,
              entityName: 'Charlie Brown',
              email: {
                type: 'OFFICE',
                value: '<EMAIL>',
                primary: true
              }
            }
          ]
        end

        context 'when entity type is lead' do
          context 'when sentTo is PRIMARY_EMAIL' do
            before do
              @bulk_job = create(:bulk_job,
                user_id: user.id,
                tenant_id: user.tenant_id,
                entity: LEAD,
                operation: EMAIL_OPERATION,
                category: CAMPAIGN_ACTION,
                payload: {
                  'sentTo' => 'PRIMARY_EMAIL',
                  'emailTemplate' => { 'id' => 47, 'name' => 'Welcome Email' },
                  'fromId' => '<EMAIL>',
                  'campaign' => { 'id' => 101, 'name' => 'Test Campaign' },
                  'activity' => { 'id' => 1, 'name' => 'Email Activity' }
                }
              )
              filters = @bulk_job.filters

              stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?page=0&size=1")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer #{ user.token }"
                }
              ).to_return(status: 200, body: file_fixture('fetch_email_entity_details_response.json').read, headers: {})

              stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=#{filters['sort']}&page=#{filters['page']}&size=5")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer #{ user.token }"
                }
              ).to_return(status: 200, body: file_fixture('fetch_email_entity_details_response.json').read, headers: {})
            end

            it 'should fetch all entities and publish CampaignActivityBulkJobStarted event with primary emails only' do
              expect(Publishers::CampaignActivityBulkJobStartedPublisher).to receive(:call).with(@bulk_job, primary_email_entity_details_list).once

              EntityAction::Fetch.new(@bulk_job.id).call

              expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 5
              expect(@bulk_job.reload.number_of_records).to eq 5
            end
          end
        end

        context 'when entity type is contact' do
          context 'when sentTo is PRIMARY_EMAIL' do
            before do
              @bulk_job = create(:bulk_job,
                user_id: user.id,
                tenant_id: user.tenant_id,
                entity: CONTACT,
                operation: EMAIL_OPERATION,
                category: CAMPAIGN_ACTION,
                payload: {
                  'sentTo' => 'PRIMARY_EMAIL',
                  'emailTemplate' => { 'id' => 47, 'name' => 'Welcome Email' },
                  'fromId' => '<EMAIL>',
                  'campaign' => { 'id' => 101, 'name' => 'Test Campaign' },
                  'activity' => { 'id' => 1, 'name' => 'Email Activity' }
                }
              )
              filters = @bulk_job.filters

              stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=1")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer #{ user.token }"
                }
              ).to_return(status: 200, body: file_fixture('fetch_email_entity_details_response.json').read, headers: {})

              stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=#{filters['sort']}&page=#{filters['page']}&size=5")
              .with(
                body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
                headers: {
                  "Authorization" => "Bearer #{ user.token }"
                }
              ).to_return(status: 200, body: file_fixture('fetch_email_entity_details_response.json').read, headers: {})
            end

            it 'should fetch all entities and publish CampaignActivityBulkJobStarted event with primary emails only' do
              expect(Publishers::CampaignActivityBulkJobStartedPublisher).to receive(:call).with(@bulk_job, primary_email_entity_details_list).once

              EntityAction::Fetch.new(@bulk_job.id).call

              expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq 5
              expect(@bulk_job.reload.number_of_records).to eq 5
            end
          end
        end
      end
    end

    context 'when entity is deal and operation is email' do
      before do
        @sample_deal_json_response = file_fixture('sample-deal-response.json').read
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: DEAL, operation: EMAIL_OPERATION)
        filters = @bulk_job.filters
        stub_request(:post, SERVICE_SEARCH + "/v1/search/deal?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}").
          with(
            body: { fields: ['id', 'associatedContacts'], jsonRule: filters['jsonRule']}.to_json,
            headers: {
              "Authorization" => "Bearer #{ user.token }"
            }).
          to_return(status: 200, body: @sample_deal_json_response, headers: {})
        EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates 11 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to be(11)
      end

      it 'matches parent ids & entity ids on records' do
        sample_deal_response = JSON(@sample_deal_json_response)
        deal_contact_mapping =
          sample_deal_response['content'].inject({}.with_indifferent_access) do |hash, deal|
            hash[deal['id']] = []
            if deal['associatedContacts'].present?
              deal['associatedContacts'].each { |contact| hash[deal['id']] << contact['id'] }
            else
              hash[deal['id']] << 0
            end

            hash
          end
        Record.where(bulk_job_id: @bulk_job.id).each do |record|
          expect(deal_contact_mapping[record.parent_entity_id]).to include(record.entity_id)
        end
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to be(11)
      end
    end

    context 'fetch task data from search service' do
      before(:each) do
        content = []
        422.times do |i|
          content << { "id": i }
        end
        @data = {
          'content': content,
          'last': true,
          'totalPages': 1,
          'totalElements': 6,
          'sort': [
            {
              'direction': 'DESC',
              'property': 'updatedAt',
              'ignoreCase': false,
              'nullHandling': 'NATIVE',
              'descending': true,
              'ascending': false
            }
          ],
          'first': true,
          'numberOfElements': 6,
          'size': 10,
          'number': 0
        }

        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: TASK)
        filters = @bulk_job.filters
        stub_request(:post, SERVICE_PRODUCTIVITY + "/v1/tasks/search?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
          .with(
            body: { fields: ['id'], jsonRule: filters['jsonRule'] }.to_json,
            headers: {
              'Authorization' => "Bearer #{user.token}"
            }
          ).to_return(status: 200, body: @data.to_json, headers: {})

        EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates 422 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq(422)
      end

      it 'matches entity ids on records' do
        expect(Record.all.pluck(:entity_id)).to match(@data[:content].map { |e| e[:id] })
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to eq(422)
      end
    end

    context 'fetch call_log data from search service' do
      before(:each) do
        content = []
        422.times do |i|
          content << { "id": i }
        end
        @data = {
          'content': content,
          'last': true,
          'totalPages': 1,
          'totalElements': 6,
          'sort': [
            {
              'direction': 'DESC',
              'property': 'updatedAt',
              'ignoreCase': false,
              'nullHandling': 'NATIVE',
              'descending': true,
              'ascending': false
            }
          ],
          'first': true,
          'numberOfElements': 6,
          'size': 10,
          'number': 0
        }

        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: CALL_LOG)
        filters = @bulk_job.filters
        stub_request(:post, "#{SERVICE_CALL}/v1/call-logs/search?sort=#{filters['sort']}&page=#{filters['page'] + 1}&size=#{filters['size']}")
          .with(
            body: { fields: ['id'], jsonRule: filters['jsonRule'] }.to_json,
            headers: {
              'Authorization' => "Bearer #{user.token}"
            }
          ).to_return(status: 200, body: @data.to_json, headers: {})

        EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates 422 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq(422)
      end

      it 'matches entity ids on records' do
        expect(Record.all.pluck(:entity_id)).to match(@data[:content].map { |e| e[:id] })
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to eq(422)
      end
    end

    context 'fetch user data from iam service' do
      before(:each) do
        content = []
        422.times do |i|
          content << { "id": i }
        end
        @data = {
          'content': content,
          'last': true,
          'totalPages': 1,
          'totalElements': 6,
          'sort': [
            {
              'direction': 'DESC',
              'property': 'updatedAt',
              'ignoreCase': false,
              'nullHandling': 'NATIVE',
              'descending': true,
              'ascending': false
            }
          ],
          'first': true,
          'numberOfElements': 6,
          'size': 10,
          'number': 0
        }

        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: USER)
        filters = @bulk_job.filters
        stub_request(:post, "#{SERVICE_IAM}/v1/users/search?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
          .with(
            body: { fields: ['id'], jsonRule: filters['jsonRule'] }.to_json,
            headers: {
              'Authorization' => "Bearer #{user.token}"
            }
          ).to_return(status: 200, body: @data.to_json, headers: {})

        EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates 422 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq(422)
      end

      it 'matches entity ids on records' do
        expect(Record.all.pluck(:entity_id)).to match(@data[:content].map { |e| e[:id] })
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to eq(422)
      end
    end

    context 'fetch email data from emails service' do
      before(:each) do
        content = []
        422.times do |i|
          content << { "id": i }
        end
        @data = {
          'content': content,
          'last': true,
          'totalPages': 1,
          'totalElements': 6,
          'sort': [
            {
              'direction': 'DESC',
              'property': 'updatedAt',
              'ignoreCase': false,
              'nullHandling': 'NATIVE',
              'descending': true,
              'ascending': false
            }
          ],
          'first': true,
          'numberOfElements': 6,
          'size': 10,
          'number': 0
        }

        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: EMAIL)
        filters = @bulk_job.filters
        stub_request(:post, "#{SERVICE_EMAIL}/v2/email-threads/search?view=bulk&sort=#{filters['sort']}&page=#{filters['page'] + 1}&size=#{filters['size']}")
          .with(
            body: { fields: ['id'], jsonRule: filters['jsonRule'] }.to_json,
            headers: {
              'Authorization' => "Bearer #{user.token}"
            }
          ).to_return(status: 200, body: @data.to_json, headers: {})

        EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates 422 records' do
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to eq(422)
      end

      it 'matches entity ids on records' do
        expect(Record.all.pluck(:entity_id)).to match(@data[:content].map { |e| e[:id] })
      end

      it 'updates number_of_records on bulk job' do
        expect(@bulk_job.reload.number_of_records).to eq(422)
      end
    end

    context 'when entity is deal and operation is whatsapp message' do
      before do
        @sample_deal_json_response = file_fixture('sample-deal-response.json').read
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, entity: DEAL, operation: WHATSAPP_MESSAGE_OPERATION)
        filters = @bulk_job.filters

        # Mock the deal search response - fetch all deal fields for WhatsApp messages
        stub_request(:post, SERVICE_SEARCH + "/v1/search/deal?sort=#{filters['sort']}&page=#{filters['page']}&size=#{filters['size']}")
          .with(
            body: { fields: [], jsonRule: filters['jsonRule']}.to_json,
            headers: {
              "Authorization" => "Bearer #{ user.token }"
            }
          )
          .to_return(status: 200, body: @sample_deal_json_response, headers: {})

        # Mock contact details fetch using search API with JSON payload
        stub_request(:post, "#{SERVICE_SEARCH}/v1/search/contact")
          .with(
            body: {
              fields: ['id', 'firstName', 'lastName', 'phoneNumbers'],
              jsonRule: {
                rules: [
                  {
                    operator: "in",
                    id: "id",
                    field: "id",
                    type: "long",
                    value: [72577, 113751, 10682, 120584]
                  }
                ],
                condition: "AND",
                valid: true
              }
            }.to_json,
            headers: {
              'Authorization' => "Bearer #{user.token}",
              'Content-Type' => 'application/json',
              'Accept' => 'application/json'
            }
          )
          .to_return(status: 200, body: {
            content: [
              {
                'id' => 72577,
                'firstName' => 'Contact',
                'lastName' => 'One',
                'phoneNumbers' => [
                  {
                    'dialCode' => '+91',
                    'value' => '9999999999',
                    'primary' => true,
                    'type' => 'MOBILE'
                  }
                ]
              },
              {
                'id' => 113751,
                'firstName' => 'Contact',
                'lastName' => 'Two',
                'phoneNumbers' => [
                  {
                    'dialCode' => '+91',
                    'value' => '8888888888',
                    'primary' => true,
                    'type' => 'MOBILE'
                  },
                  {
                    'dialCode' => '+91',
                    'value' => '7777777777',
                    'primary' => false,
                    'type' => 'MOBILE'
                  }
                ]
              }
            ]
          }.to_json, headers: {})

        # Mock masked fields check
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/contact/masked-fields")
          .with(
            headers: {
              'Authorization' => "Bearer #{ user.token }"
            }
          )
          .to_return(
            status: 200,
            body: [].to_json,
            headers: {}
          )

        EntityAction::Fetch.new(@bulk_job.id).call
      end

      it 'creates records for deal-contact relationships' do
        # Should create records for each deal-contact pair
        expect(Record.where(bulk_job_id: @bulk_job.id).count).to be > 0
      end

      it 'creates phone_number_wise_record_details with deal data as payload' do
        phone_details = PhoneNumberWiseRecordDetail.joins(:record)
                                                   .where(records: { bulk_job_id: @bulk_job.id })

        expect(phone_details.count).to be > 0

        # Check that payload contains deal data, not contact data
        phone_detail = phone_details.first
        expect(phone_detail.payload).to have_key('name') # Deal name
        expect(phone_detail.payload).to have_key('estimatedValue') # Deal field
      end

      it 'handles primary phone number option correctly' do
        @bulk_job.payload = { 'messageSendTo' => [{ 'type' => 'PRIMARY_PHONE_NUMBER' }] }
        @bulk_job.save

        # Re-run the fetch
        Record.where(bulk_job_id: @bulk_job.id).destroy_all
        PhoneNumberWiseRecordDetail.joins(:record).where(records: { bulk_job_id: @bulk_job.id }).destroy_all

        EntityAction::Fetch.new(@bulk_job.id).call

        phone_details = PhoneNumberWiseRecordDetail.joins(:record)
                                                   .where(records: { bulk_job_id: @bulk_job.id })

        # Should only create entries for primary phone numbers
        expect(phone_details.count).to be > 0
      end
 
      it 'creates correct deal-contact relationship records' do
        records = Record.where(bulk_job_id: @bulk_job.id)

        # Should have records where parent_entity_id is deal ID and entity_id is contact ID
        deal_contact_record = records.find_by(parent_entity_id: 25188, entity_id: 72577)
        expect(deal_contact_record).to be_present

        deal_contact_record_2 = records.find_by(parent_entity_id: 25188, entity_id: 113751)
        expect(deal_contact_record_2).to be_present
      end

      it 'stores complete deal data in phone_number_wise_record_details payload' do
        phone_details = PhoneNumberWiseRecordDetail.joins(:record)
                                                   .where(records: { bulk_job_id: @bulk_job.id })
                                                   .first

        expect(phone_details.payload).to have_key('name') # Deal name
        expect(phone_details.payload).to have_key('estimatedValue') # Deal specific field
        expect(phone_details.payload).to have_key('associatedContacts') # Deal relationships
        expect(phone_details.payload).to have_key('id') # Deal ID

        # Should not have contact-specific fields like firstName, lastName in the main payload
        expect(phone_details.payload).not_to have_key('firstName')
        expect(phone_details.payload).not_to have_key('lastName')
      end

      it 'handles deals with multiple contacts correctly' do
        # Find a deal with multiple contacts (deal 25188 has contacts 72577 and 113751)
        deal_records = Record.where(bulk_job_id: @bulk_job.id, parent_entity_id: 25188)
        expect(deal_records.count).to eq(2)

        # Each contact should have their phone numbers processed
        deal_records.each do |record|
          phone_details = PhoneNumberWiseRecordDetail.where(record_id: record.id)
          expect(phone_details.count).to be > 0

          # All phone details for this deal should have the same deal data
          phone_details.each do |detail|
            expect(detail.payload['id']).to eq(25188)
            expect(detail.payload['name']).to eq('Yet Another deal with contact 72577')
          end
        end
      end

      it 'handles deals with no associated contacts' do
        # Deal 23956 has no associated contacts (associatedContacts: null)
        no_contact_records = Record.where(bulk_job_id: @bulk_job.id, parent_entity_id: 23956, entity_id: 0)
        expect(no_contact_records.count).to eq(1)

        # Should not create any phone number wise record details for deals with no contacts
        phone_details = PhoneNumberWiseRecordDetail.where(record_id: no_contact_records.pluck(:id))
        expect(phone_details.count).to eq(0)
      end

      it 'updates bulk job with total phone numbers count' do
        expect(@bulk_job.reload.payload['totalPhoneNumbers']).to be > 0

        # Verify the count matches actual phone number wise record details
        actual_count = Record.where(bulk_job_id: @bulk_job.id).joins(:phone_number_wise_record_details).count
        expect(@bulk_job.payload['totalPhoneNumbers']).to eq(actual_count)
      end
    end

    context 'with invalid data' do
      it 'should throw invalid data error when id is wrong' do
        expect{ EntityAction::Fetch.new(111).call }.to raise_error(ExceptionHandler::InvalidDataError, "025003||Couldn't find BulkJob with 'id'=111")
      end
    end
  end
end
