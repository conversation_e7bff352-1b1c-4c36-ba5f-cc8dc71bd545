require 'rails_helper'

RSpec.describe WhatsappTemplateMediaService do
  let(:connected_account_id) { 123 }
  let(:template_media_id) { 456 }
  let(:tenant) { Tenant.create(id: 99) }
  let(:user) { User.create(id: 1, name: '<PERSON>', token: 'some-token', tenant_id: tenant.id) }
  let(:bulk_job) { create(:bulk_job, user_id: user.id, payload: { 'connectedAccount' => { 'id' => connected_account_id }, 'whatsappTemplate' => { 'id' => 21, 'dynamicTemplateMediaId' => template_media_id } }) }
  let(:service) { described_class.new(bulk_job) }
  let(:media_details) do
    {
      'whatsappFileHandle' => '4:U2FtcGxlUE5HSW1hZ2VfM21iLnBuZw==:aW1hZ2UvcG5n:ARbY3X5Xrv2tPjvldfuV9PO1hZ2VfM21iLnBuZw==:',
      'id' => 1,
      'fileName' => 'tenant_9/connetced_account_1/file/ab/c.png',
      'fileSize' => 30303,
      'fileType' => 'image/png',
      'mediaUrl' => {
        'url' => 'https://www.aws.com/file_url.png',
        "file_name" => 'file_url.png'
      }
    }
  end

  describe '#download_and_upload' do
    before do
      allow_any_instance_of(Aws::S3::Client).to receive(:put_object).and_return(true)

      stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/#{connected_account_id}/template-media/#{template_media_id}")
        .with(headers: { 'Authorization' => "Bearer #{user.token}" })
        .to_return(status: 200, body: media_details.to_json, headers: { 'Content-Type' => 'application/json' })
      
      stub_request(:get, media_details['mediaUrl']['url'])
        .to_return(status: 200, body: 'file_content', headers: { 'Content-Type' => 'image/png' })
      
      temp_file = instance_double(Tempfile, path: '/tmp/whatsapp_media.png', binmode: nil, write: nil, rewind: nil)
      allow(Tempfile).to receive(:new).and_return(temp_file)
      
      allow(FileUploader).to receive(:upload).and_return("whatsapp_media/#{connected_account_id}/#{template_media_id}.png")
    end

    it 'fetches media details, downloads and uploads to S3' do
      service.download_and_upload
    end

    context 'when media details raises an error' do
      before do
        stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/#{connected_account_id}/template-media/#{template_media_id}")
        .with(headers: { 'Authorization' => "Bearer #{user.token}" })
          .to_return(status: 404)
      end

      it 'raises an error' do
        expect { service.download_and_upload }.to raise_error(ExceptionHandler::InvalidDataError, "025003||404 Not Found")
      end
    end

    context 'when file download fails' do
      before do
        stub_request(:get, media_details['mediaUrl']['url'])
          .to_return(status: 404)
      end

      it 'raises an error' do
        expect { service.download_and_upload }.to raise_error(ExceptionHandler::InvalidDataError, "025003||404 Not Found")
      end
    end

    context 'when S3 upload fails' do
      before do
        allow(FileUploader).to receive(:upload).and_raise(ExceptionHandler::InvalidDataError, ErrorCode.file_upload_error)
      end

      it 'raises an error' do
        expect { service.download_and_upload }.to raise_error(ExceptionHandler::InvalidDataError, "025005")
      end
    end
  end
end 