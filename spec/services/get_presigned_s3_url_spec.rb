require 'rails_helper'

RSpec.describe GetPresignedS3Url do

  context 'Given file name' do
    before do
      @resource = instance_double(Aws::S3::Resource)
      @bucket = instance_double(Aws::S3::Bucket)
      @obj = instance_double(Aws::S3::Object)
      @remote_url = '99/11/test.csv'
      allow(Aws::S3::Resource).to receive(:new).and_return(@resource)
    end

    context 'Attachment exists' do
      it 'should get presigned url of file from s3'do
        allow(@resource).to receive(:bucket).with(S3_BULK_ACTION_BUCKET).and_return(@bucket)
        allow(@bucket).to receive(:object).with(@remote_url).and_return(@obj)
        allow(@obj).to receive(:presigned_url).and_return('https://www.aws.com/test.csv')

        url = GetPresignedS3Url.remote_url(@remote_url, 'test.csv')
        expect(url).to eq('https://www.aws.com/test.csv')
      end
    end
  end
end
