# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DeleteFileFromS3 do
  context 'When file path is passed' do
    before do
      @resource = instance_double(Aws::S3::Resource)
      @bucket = instance_double(Aws::S3::Bucket)
      @obj = instance_double(Aws::S3::Object)
      allow(Aws::S3::Resource).to receive(:new).and_return(@resource)
      @file_path = '123/12/summary-file.csv'
    end

    context 'File exists' do
      it 'deletes remote file' do
        allow(@resource).to receive(:bucket).with(S3_BULK_ACTION_BUCKET).and_return(@bucket)
        allow(@bucket).to receive(:delete_objects).and_return(file_path: '123/12/summary-file.csv')

        url = DeleteFileFromS3.new([@file_path])
        expect(url.as_json['file_paths']).to eq([@file_path])
      end
    end

    context 'when exception occurs' do
      it 'logs error' do
        allow(@resource).to receive(:bucket).with(S3_BULK_ACTION_BUCKET).and_return(@bucket)
        allow(@bucket).to receive(:delete_objects).and_raise('Error')

        expect { DeleteFileFromS3.new([@file_path]).call }.to raise_error(
          ExceptionHandler::InvalidDataError, '025003||Could not delete file.'
        )
        expect(Rails.logger.error).to eq(1)
      end
    end
  end
end
