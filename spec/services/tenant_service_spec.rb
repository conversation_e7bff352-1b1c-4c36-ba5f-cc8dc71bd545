require 'rails_helper'

RSpec.describe TenantService do
  let(:token) { 'test-token-123' }

  describe "fetch_tenant" do
    context "Success" do
      let(:tenant_data) { file_fixture('tenant-data.json').read }

      before(:each) do
        stub_request(:get, SERVICE_IAM + "/v1/tenants").
          with(
            headers: {
              "Authorization" => "Bearer #{token}",
              'Accept'=>'application/json',
              'Content-Type'=>'application/json'
            }).
            to_return(status: 200, body: tenant_data, headers: {})
          Thread.current[:token] = token
      end

      it 'should return tenant data with correct structure' do
        response = TenantService.fetch_tenant
        data = JSON.parse(tenant_data)
        
        expect(response["id"]).to eq data["id"]
        expect(response["planName"]).to eq data["planName"]
      end
    end
  end
end
