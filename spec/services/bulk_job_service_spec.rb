# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BulkJobService do
  let(:user) { User.create(id: 1, name: '<PERSON>', tenant: Tenant.create(id: 99)) }
  let(:tenant_data) { file_fixture('tenant-data.json').read }
  before do
    Thread.current[:user] = user
    Thread.current[:token] = get_test_jwt(user.id, user.tenant_id)
    stub_request(:get, SERVICE_IAM + "/v1/tenants").
      with(
        headers: {
          'Accept' => 'application/json',
          'Authorization' => "Bearer #{Thread.current[:token]}"
        }
      ).
      to_return(status: 200, body: tenant_data, headers: {})
  end

  let(:params) do
    {
      entity: LEAD,
      operation: UPDATE_OPERATION,
      filters: {
        "sort": 'updatedAt,desc',
        "page": 10,
        "size": 100,
        "jsonRule": {
          "rules": [
            {
              "operator": 'equal',
              "id": 'ownerId',
              "field": 'ownerId',
              "type": 'long',
              "value": 11
            }
          ],
          "condition": 'AND',
          "valid": true
        }
      },
      execute_workflow: true,
      payload: { ownerId: 11 }
    }.with_indifferent_access
  end

  describe '#create' do
    context 'with valid params' do
      it 'should create bulk job and return id' do
        bulk_job_id = BulkJobService.create(params)
        expect(BulkJob.count).to be(1)
        expect(bulk_job_id).to eql(BulkJob.first.id)
        bulk_job_data = BulkJob.first.as_json(
          only: %i[
            operation entity execute_workflow
            filters payload user_id status
          ]
        )
        expect(bulk_job_data).to match(
          {
            operation: params[:operation],
            entity: params[:entity],
            execute_workflow: params[:execute_workflow],
            filters: params[:filters],
            payload: params[:payload],
            user_id: user.id,
            status: 'queuing'
          }.with_indifferent_access
        )
      end

      it 'should update token on user' do
        user.update(token: nil)
        BulkJobService.create(params)
        expect(user.reload.token).to eq Thread.current[:token]
      end

      it 'should have enqueud job to for fetching and storing data' do
        expect {
          BulkJobService.create(params)
        }.to have_enqueued_job(QueueBulkJob)
      end

      context 'when entity is note' do
        let(:note_bulk_job_params) do
          {
            entity: NOTE,
            operation: DELETE_OPERATION,
            filters: {
              "sort": 'updatedAt,desc',
              "page": 10,
              "size": 100,
              "jsonRule": {
                "rules": [
                  {
                    "operator": 'equal',
                    "id": 'createdBy',
                    "field": 'createdBy',
                    "type": 'long',
                    "value": 11
                  }
                ],
                "condition": 'AND',
                "valid": true
              }
            },
            execute_workflow: false
          }.with_indifferent_access
        end

        context 'when user does not have delete permission on note' do
          it 'should throw forbidden error' do
            expect do
              BulkJobService.create(note_bulk_job_params)
            end.to raise_error(ExceptionHandler::Forbidden, '025002')
          end
        end

        context 'when user has delete permission on note' do
          before do
            allow_any_instance_of(Auth::Data).to receive(:can_access?).with('note', 'delete').and_return(true)
          end

          it 'should create bulk job and return id' do
            bulk_job_id = BulkJobService.create(note_bulk_job_params)
            expect(BulkJob.count).to be(1)
            expect(bulk_job_id).to eql(BulkJob.first.id)
            bulk_job_data = BulkJob.first.as_json(
              only: %i[
                operation entity execute_workflow
                filters payload user_id status
              ]
            )
            expect(bulk_job_data).to match(
              {
                operation: note_bulk_job_params[:operation],
                entity: note_bulk_job_params[:entity],
                execute_workflow: note_bulk_job_params[:execute_workflow],
                filters: note_bulk_job_params[:filters],
                payload: note_bulk_job_params[:payload],
                user_id: user.id,
                status: 'queuing'
              }.with_indifferent_access
            )
          end
        end
      end
    end

    context 'with invalid params' do
      it 'should throw invalid data error' do
        invalid_params = params.clone
        invalid_params[:entity] = 'dummy'
        expect do
          BulkJobService.create(invalid_params)
        end.to raise_error(ExceptionHandler::InvalidDataError, '025003||Validation failed: Entity is invalid')
      end
    end

    context 'when creating a WhatsApp message job with dynamic media' do
      let(:tenant) { create(:tenant) }
      let(:params) do
        {
          'entity' => 'lead',
          'operation' => 'whatsappMessage',
          'payload' => {
            'connectedAccount' => { 'id' => 123 },
            'whatsappTemplate' => {
              'id' => 456,
              'dynamicTemplateMediaId' => 789
            },
            'messageSendTo' => [
              {
                'name' => 'Primary phone number',
                'type' => 'PRIMARY_PHONE_NUMBER'
              }
            ]
          },
          'filters' => {
            "sort" => 'updatedAt,desc',
            "page" => 10,
            "size" => 100,
            "jsonRule" => {
              "rules" => [
                {
                  "operator" => 'equal',
                  "id" => 'ownerId',
                  "field" => 'ownerId',
                  "type" => 'long',
                  "value" => 11
                }
              ],
              "condition" => 'AND',
              "valid" => true
            }
          }
        }
      end

      before do
        Thread.current[:user] = user
        Thread.current[:token] = 'test_token'
        stub_request(:get, SERVICE_IAM + "/v1/tenants").
          with(
            headers: {
              'Accept' => 'application/json',
              'Authorization' => "Bearer #{Thread.current[:token]}",
              'Content-Type' => 'application/json'
            }
          ).
          to_return(status: 200, body: tenant_data, headers: {})

        stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-credits/status").with(
          headers: {
            'Authorization' => "Bearer #{Thread.current[:token]}"
          }
        ).to_return(
          status: 200,
          body: {
            "availableForBulkMessages": true
          }.to_json,
          headers: {}
        )
      end

      context 'when creating a WhatsApp message job with dynamic media' do
        it 'validates the dynamic template media' do
          expect(QueueBulkJob).to receive(:perform_later).once
          stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/123/template-media/789")
            .with(headers: { 'Authorization' => 'Bearer test_token' })
            .to_return(status: 200, body: {}.to_json, headers: {})
          described_class.create(params)
        end

        context 'when dynamic template media validation fails' do
          it 'raises an error' do
            stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/connected-accounts/123/template-media/789")
            .with(headers: { 'Authorization' => 'Bearer test_token' })
            .to_return(status: 404, body: {}.to_json, headers: {})

            expect {
              described_class.create(params)
            }.to raise_error(ExceptionHandler::InvalidDataError)
          end
        end
      end
    end

    context 'when user is on embark plan' do
      let(:embark_tenant_data) do
        data = JSON.parse(file_fixture('tenant-data.json').read)
        data['planName'] = 'embark' 
        data.to_json
      end
      before do
        stub_request(:get, SERVICE_IAM + "/v1/tenants").
            with(
              headers: {
                'Accept' => 'application/json',
                'Authorization' => "Bearer #{Thread.current[:token]}"
              }
            ).
            to_return(status: 200, body: embark_tenant_data, headers: {})
      end

      it 'should throw forbidden error for embark plan users' do
        expect { BulkJobService.create(params) }.to raise_error(ExceptionHandler::Forbidden, "025002||Your current plan does not support bulk actions.")
        expect(QueueBulkJob).not_to receive(:perform_later)
        expect(BulkJob.count).to be(0)
      end
    end
  end

  describe '#list' do
    before(:each) do
      create_list(:bulk_job, 2, user_id: user.id, tenant_id: user.tenant_id, successful: 2, failure: 0)
    end

    context 'with pagination params' do
      it 'should return list of bulk jobs' do
        bulk_jobs = BulkJobService.list({ page: 0, size: 10, sort: 'updatedAt,desc' })
        bulk_jobs['content'].each do |bulk_job|
          expect(bulk_job['status']).to eq('COMPLETED')
          expect(bulk_job['entity']).to eq('LEAD')
          expect(bulk_job['operation']).to eq('UPDATE')
          expect(bulk_job['submittedAt']).to be < DateTime.now.ago(1.hour).to_s
          expect(bulk_job['completedAt']).to be < DateTime.now.to_s
          expect(bulk_job['submittedBy']).to eq({ 'id' => 1, 'name' => 'Tony Stark' })
          expect(bulk_job['successful']).to be(2)
          expect(bulk_job['failure']).to be(0)
        end

        expect(bulk_jobs.except('content')).to match(
          {
            'totalElements' => 2, 'totalPages' => 1, 'last' => true,
            'numberOfElements' => 2, 'first' => true, 'size' => 10, 'number' => 0
          }
        )
      end
    end

    context 'with invalid pagination params' do
      it 'should throw invalid data error when page is invalid' do
        expect do
          BulkJobService.list(
            { page: -1, size: 0, sort: 'updatedAt,desc' }
          )
        end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
      end

      it 'should throw invalid data error when sort params are invalid' do
        expect do
          BulkJobService.list({ page: 0, sort: 'submittedAt,sesc' })
        end.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
      end
    end

    context 'when json_rule is null in params' do
      it 'should return list of bulk jobs' do
        bulk_jobs = BulkJobService.list({ page: 0, size: 10, sort: 'updatedAt,desc', json_rule: nil })
        bulk_jobs['content'].each do |bulk_job|
          expect(bulk_job['status']).to eq('COMPLETED')
          expect(bulk_job['entity']).to eq('LEAD')
          expect(bulk_job['operation']).to eq('UPDATE')
          expect(bulk_job['submittedAt']).to be < DateTime.now.ago(1.hour).to_s
          expect(bulk_job['completedAt']).to be < DateTime.now.to_s
          expect(bulk_job['submittedBy']).to eq({ 'id' => 1, 'name' => 'Tony Stark' })
          expect(bulk_job['successful']).to be(2)
          expect(bulk_job['failure']).to be(0)
        end

        expect(bulk_jobs.except('content')).to match(
          {
            'totalElements' => 2, 'totalPages' => 1, 'last' => true,
            'numberOfElements' => 2, 'first' => true, 'size' => 10, 'number' => 0
          }
        )
      end
    end

    context 'when json_rule is present in params' do
      let(:params) { { page: 0, size: 10, sort: 'updatedAt,desc' } }
      let(:parsed_json_rules) { JSON.parse(file_fixture('json_rule.json').read) }

      def json_rule_params(json_rule)
        params.merge!(json_rule).with_indifferent_access
      end

      def prepare_json_rule(field, value, operator = nil)
        rule = parsed_json_rules['json_rule']['rules'].find { |r| r['field'] == field }
        rule['value'] = value
        rule['operator'] = operator if operator
        {
          json_rule: {
            condition: 'AND',
            rules: [rule]
          }
        }.with_indifferent_access
      end

      context 'when rule is invalid' do
        it 'should throws exception' do
          expect do
            BulkJobService.list(
              json_rule_params(prepare_json_rule('submittedAt', '2022-09-09T09:30:00.000Z'))
            )
          end.to raise_error(ExceptionHandler::InvalidDataError, /025003 || Json Rule invalid/)
        end
      end

      context 'when bulk_jobs not found for json_rules' do
        it 'should returns empty list' do
          bulk_jobs = BulkJobService.list(json_rule_params(parsed_json_rules))
          expect(bulk_jobs['content']).to be_empty
          expect(bulk_jobs['totalElements']).to eq(0)
        end
      end

      context 'when bulk_jobs found for rules' do
        before(:each) do
          create(
            :bulk_job,
            id: 4464, user_id: user.id, tenant_id: user.tenant_id,
            status: :in_progress, created_at: DateTime.parse('2022-09-09T11:30:00.000Z')
          )
          create(
            :bulk_job,
            id: 4567, user_id: user.id, tenant_id: user.tenant_id,
            status: :completed, created_at: DateTime.parse('2022-09-09T11:30:00.000Z')
          )
          create(
            :bulk_job,
            id: 5764, user_id: user.id, tenant_id: user.tenant_id,
            status: :completed, created_at: DateTime.parse('2022-09-09T11:30:00.000Z')
          )
          create(
            :bulk_job,
            id: 8888, user_id: user.id, tenant_id: user.tenant_id,
            status: :completed, created_at: DateTime.parse('2022-09-09T11:30:00.000Z')
          )
          create(:summary_file, bulk_job_id: 4567, file_type: SummaryFile::ERROR)
          create(:summary_file, bulk_job_id: 5764, file_type: SummaryFile::SUCCESS)
          create(:summary_file, bulk_job_id: 8888, file_type: SummaryFile::ERROR)
          create(:summary_file, bulk_job_id: 8888, file_type: SummaryFile::SUCCESS)
        end

        it 'should returns filtered list' do
          bulk_jobs = BulkJobService.list(json_rule_params(parsed_json_rules))
          expect(bulk_jobs['content']).to be_present
          record = bulk_jobs['content'].first
          expect(record['id']).to eq(4464)
          expect(record['status']).to eq('IN_PROGRESS')
          expect(bulk_jobs['totalElements']).to eq(1)
        end

        context 'when json_rule applied for status' do
          context 'with not_equal operator and value with COMPLETED_WITH_ERROR' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', 'COMPLETED_WITH_ERROR', 'not_equal'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4567)
              expect(bulk_jobs['totalElements']).to eq(4)
            end
          end

          context 'with not_equal operator and value with COMPLETED' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', 'COMPLETED', 'not_equal'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(5764)
              expect(bulk_jobs['totalElements']).to eq(3)
            end
          end

          context 'with equal operator and value with COMPLETED_WITH_ERROR' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', 'COMPLETED_WITH_ERROR', 'equal'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(5764)
              expect(bulk_jobs['totalElements']).to eq(2)
            end
          end

          context 'with equal operator and value with COMPLETED' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', 'COMPLETED', 'equal'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4567)
              expect(bulk_jobs['totalElements']).to eq(3)
            end
          end

          context 'with in operator and value with COMPLETED' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', ['COMPLETED'], 'in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4567, 8888)
              expect(bulk_jobs['totalElements']).to eq(3)
            end
          end

          context 'with in operator and value with COMPLETED, IN_PROGRESS' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', %w[COMPLETED IN_PROGRESS], 'in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4567, 8888)
              expect(bulk_jobs['totalElements']).to eq(4)
            end
          end

          context 'with in operator and value with COMPLETED_WITH_ERROR' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', ['COMPLETED_WITH_ERROR'], 'in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(5764)
              expect(bulk_jobs['totalElements']).to eq(2)
            end
          end

          context 'with in operator and value with COMPLETED_WITH_ERROR, IN_PROGRESS' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', %w[COMPLETED_WITH_ERROR IN_PROGRESS], 'in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(5764)
              expect(bulk_jobs['totalElements']).to eq(3)
            end
          end

          context 'with in operator and value with COMPLETED_WITH_ERROR, COMPLETED' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', %w[COMPLETED_WITH_ERROR COMPLETED], 'in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4464)
              expect(bulk_jobs['totalElements']).to eq(5)
            end
          end

          context 'with not_in operator and value with COMPLETED' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', ['COMPLETED'], 'not_in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(5764)
              expect(bulk_jobs['totalElements']).to eq(3)
            end
          end

          context 'with not_in operator and value with COMPLETED, IN_PROGRESS' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', %w[COMPLETED IN_PROGRESS], 'not_in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(5764, 4464)
              expect(bulk_jobs['totalElements']).to eq(2)
            end
          end

          context 'with not_in operator and value with COMPLETED_WITH_ERROR' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', ['COMPLETED_WITH_ERROR'], 'not_in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4567)
              expect(bulk_jobs['totalElements']).to eq(4)
            end
          end

          context 'with not_in operator and value with COMPLETED_WITH_ERROR, IN_PROGRESS' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', %w[COMPLETED_WITH_ERROR IN_PROGRESS], 'not_in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4567, 4464)
              expect(bulk_jobs['totalElements']).to eq(3)
            end
          end

          context 'with not_in operator and value with COMPLETED_WITH_ERROR, COMPLETED' do
            it 'should returns filtered list' do
              bulk_jobs = BulkJobService.list(
                json_rule_params(prepare_json_rule('status', %w[COMPLETED_WITH_ERROR COMPLETED], 'not_in'))
              )
              expect(bulk_jobs['content']).to be_present
              expect(bulk_jobs['content'].map { |c| c['id'] }).not_to include(4567, 5764)
              expect(bulk_jobs['totalElements']).to eq(1)
            end
          end
        end
      end
    end
  end

  describe '#resume_halted_jobs' do
    context 'when job is halted' do
      before { @bulk_job = create(:bulk_job, sidekiq_job_id: 1) }

      it 'changes sidekiq_job_id on bulk job when its not in any of the queue' do
        expect { BulkJobService.resume_halted_jobs }.to have_enqueued_job(BulkActionJob)
        expect(@bulk_job.reload.sidekiq_job_id).not_to be_blank
        expect(@bulk_job.sidekiq_job_id).not_to eq 1
      end

      context 'when job status is queuing' do
        before { @bulk_job.queuing! }

        it 'enqueues QueueBulkJob' do
          expect { BulkJobService.resume_halted_jobs }.to have_enqueued_job(QueueBulkJob)
          expect(@bulk_job.reload.sidekiq_job_id).not_to be_blank
          expect(@bulk_job.sidekiq_job_id).not_to eq 1
        end
      end
    end

    context 'when job is not halted' do
      it 'Dont do anything when sidekiq job id is not set' do
        bulk_job = create(:bulk_job)
        BulkJobService.resume_halted_jobs
        expect(bulk_job.reload.sidekiq_job_id).to eq nil
      end

      it 'will not do anything when job is already in queue' do
        e = [{ "payload": { "args": [{ "job_id": '1' }] } }].each
        expect_any_instance_of(Sidekiq::Queue).to receive(:any?).and_return(e.first)
        bulk_job = create(:bulk_job, sidekiq_job_id: '1')
        BulkJobService.resume_halted_jobs
        expect(bulk_job.sidekiq_job_id).to eq '1'
      end
    end
  end

  describe '#abort' do
    context 'with invalid permission' do
      context 'when current user is not bulk job creator user' do
        context 'and user does not have update all on bulk job entity' do
          before do
            create(:user, tenant_id: user.tenant_id, id: 2)
            create(:bulk_job, user_id: 2, tenant_id: user.tenant_id, id: 1, status: 'in_progress')
            Thread.current[:token] = get_test_jwt(user.id, user.tenant_id, false)
          end

          it 'raises unauthorized error' do
            expect { BulkJobService.new(1).abort }.to raise_error(
              ExceptionHandler::Forbidden,
              '025002'
            )
          end
        end
      end
    end

    context 'when bulk job is already in aborted state' do
      before do
        create(:bulk_job, status: 'aborted', id: 1, user_id: user.id)
      end

      it 'raises error' do
        expect { BulkJobService.new(1).abort }.to raise_error(
          ExceptionHandler::InvalidDataError,
          '025010||Cannot abort the bulk job which is in aborted state'
        )
      end
    end

    context 'when bulk job is in completed state' do
      before do
        create(:bulk_job, status: 'completed', id: 1, user_id: user.id)
      end

      it 'raises error' do
        expect { BulkJobService.new(1).abort }.to raise_error(
          ExceptionHandler::InvalidDataError,
          '025010||Cannot abort the bulk job which is in completed state'
        )
      end
    end

    context 'when bulk job category is campaign_action and user tries to update its status from UI' do
      before do
        create(:bulk_job, status: 'in_progress', category: 'campaign_action', id: 1, user_id: user.id)
      end

      it 'raises error' do
        expect { BulkJobService.new(1).abort }.to raise_error(
          ExceptionHandler::InvalidDataError,
          '025003||Cannot change the status of the campaign action job manually'
        )
      end
    end

    context 'when bulk job is in paused state' do
      before do
        @bulk_job = create(:bulk_job, status: 'paused', id: 1, user_id: user.id, tenant_id: user.tenant_id)
        create_list(:record, 3, tenant_id: user.tenant_id, bulk_job_id: 1)
      end

      it 'moves job to aborting state and deletes all records and schedules AbortBulkJob Job' do
        expect { BulkJobService.new(1).abort }.to have_enqueued_job(AbortBulkJob)
        expect(@bulk_job.reload.status).to eq('aborting')
      end
    end

    context 'when bulk job is in state other than aborted and completed state' do
      before do
        create(:bulk_job, id: 1, status: 'queued', user_id: user.id, tenant_id: user.tenant_id)
        create(:bulk_job, id: 2, status: 'queued', user_id: user.id, tenant_id: user.tenant_id)
      end

      it 'updates status of bulk job to aborted and schedules job for futher processes' do
        BulkJobService.new(1).abort
        expect(BulkJob.find(1).status).to eq('aborting')
        expect(BulkActionJob).to have_been_enqueued.exactly(:once).with(2)
      end
    end

    context 'when bulk job is in retrying state' do
      before do
        @bulk_job = create(:bulk_job, status: 'retrying', id: 1, user_id: user.id, tenant_id: user.tenant_id)
        create_list(:record, 3, tenant_id: user.tenant_id, bulk_job_id: 1)
      end

      it 'moves job to aborting state and deletes all records and schedules AbortBulkJob Job' do
        expect { BulkJobService.new(1).abort }.to have_enqueued_job(AbortBulkJob)
        expect(@bulk_job.reload.status).to eq('aborting')
      end
    end
  end

  describe '#pause' do
    context 'with invalid permission' do
      context 'when current user is not bulk job creator user' do
        context 'and user does not have update all on bulk job entity' do
          before do
            create(:user, tenant_id: user.tenant_id, id: 2)
            create(:bulk_job, user_id: 2, tenant_id: user.tenant_id, id: 1, status: 'in_progress')
            Thread.current[:token] = get_test_jwt(user.id, user.tenant_id, false)
          end

          it 'raises unauthorized error' do
            expect { BulkJobService.new(1).pause }.to raise_error(
              ExceptionHandler::Forbidden,
              '025002'
            )
          end
        end
      end
    end

    %w[paused aborted aborting completed].each do |state|
      context "when bulk job is in #{state} state" do
        before do
          create(:bulk_job, status: state, id: 1, user_id: user.id)
        end

        it 'raises error' do
          expect { BulkJobService.new(1).pause }.to raise_error(
            ExceptionHandler::InvalidDataError,
            "025008||Cannot pause the bulk job which is in #{state} state"
          )
        end
      end
    end

    context 'when bulk job category is campaign_action and user tries to update its status from UI' do
      before do
        create(:bulk_job, status: 'in_progress', category: 'campaign_action', id: 1, user_id: user.id)
      end

      it 'raises error' do
        expect { BulkJobService.new(1).pause }.to raise_error(
          ExceptionHandler::InvalidDataError,
          '025003||Cannot change the status of the campaign action job manually'
        )
      end
    end

    context 'when bulk job is in state other states' do
      before do
        create(:bulk_job, id: 1, status: 'queued', user_id: user.id, tenant_id: user.tenant_id)
        create(:bulk_job, id: 2, status: 'queued', user_id: user.id, tenant_id: user.tenant_id)
      end

      it 'updates status of bulk job to aborted and schedules job for futher processes' do
        expect { BulkJobService.new(1).pause }.to have_enqueued_job
        expect(BulkJob.find(1).paused?).to be_truthy
      end
    end
  end

  describe '#resume' do
    context 'with invalid permission' do
      context 'when current user is not bulk job creator user' do
        context 'and user does not have update all on bulk job entity' do
          before do
            create(:user, tenant_id: user.tenant_id, id: 2)
            create(:bulk_job, user_id: 2, tenant_id: user.tenant_id, id: 1, status: 'paused')
            Thread.current[:token] = get_test_jwt(user.id, user.tenant_id, false)
          end

          it 'raises unauthorized error' do
            expect { BulkJobService.new(1).resume }.to raise_error(
              ExceptionHandler::Forbidden,
              '025002'
            )
          end
        end
      end
    end

    context "when bulk job is not in paused state" do
      before do
        create(:bulk_job, status: 'completed', id: 1, user_id: user.id)
      end

      it 'raises error' do
        expect { BulkJobService.new(1).resume }.to raise_error(
          ExceptionHandler::InvalidDataError,
          "025009||Cannot resume the bulk job which is in completed state"
        )
      end
    end

    context 'when bulk job category is campaign_action and user tries to update its status from UI' do
      before do
        create(:bulk_job, status: 'in_progress', category: 'campaign_action', id: 1, user_id: user.id)
      end

      it 'raises error' do
        expect { BulkJobService.new(1).resume }.to raise_error(
          ExceptionHandler::InvalidDataError,
          '025003||Cannot change the status of the campaign action job manually'
        )
      end
    end

    context "when bulk job is in paused state" do
      before do
        @bulk_job = create(:bulk_job, status: 'paused', id: 1, user_id: user.id)
      end

      context 'and no other job is in progress for that tenant' do
        it 'updates job status to queued and schedules job' do
          BulkJobService.new(1).resume
          expect(@bulk_job.reload.queued?).to be_truthy
          expect(BulkActionJob).to have_been_enqueued.exactly(:once).with(1)
        end
      end

      context 'and other job is in progress for that tenant' do
        before do
          Sidekiq::Queue.new.clear
          create(:bulk_job, status: 'in_progress', user_id: user.id, tenant_id: @bulk_job.tenant_id)
        end

        it 'updates job status to queued and does not schedule job' do
          expect { BulkJobService.new(1).resume }.not_to have_enqueued_job
          expect(@bulk_job.reload.queued?).to be_truthy
        end
      end
    end
  end

  describe '#delete' do
    context 'with invalid permission' do
      context 'and user does not have delete all on bulk job entity' do
        before do
          another_user = create(:user, tenant_id: user.tenant_id)
          create(:bulk_job, user_id: another_user.id, tenant_id: user.tenant_id, id: 1, entity: 'lead')
          Thread.current[:token] = get_test_jwt(user.id, user.tenant_id, false, false)
        end

        it 'raises unauthorized error' do
          expect { BulkJobService.new(1).delete }.to raise_error(
            ExceptionHandler::Forbidden,
            '025002'
          )
        end
      end
    end

    context 'when bulk job is in progress' do
      before do
        create(:bulk_job, id: 1, user_id: user.id, tenant_id: user.tenant_id, status: 'in_progress')
      end

      it 'raises error' do
        expect { BulkJobService.new(1).delete }.to raise_error(
          ExceptionHandler::InvalidDataError,
          '025011||Cannot delete the bulk job which is in in_progress state'
        )
      end
    end

    context 'when bulk job category is campaign_action and user tries to update its status from UI' do
      before do
        create(:bulk_job, status: 'in_progress', category: 'campaign_action', id: 1, user_id: user.id)
      end

      it 'raises error' do
        expect { BulkJobService.new(1).delete }.to raise_error(
          ExceptionHandler::InvalidDataError,
          '025003||Cannot change the status of the campaign action job manually'
        )
      end
    end

    context 'when bulk job is valid' do
      before do
        create(:bulk_job, id: 1, user_id: user.id, tenant_id: user.tenant_id)
        create(:summary_file, bulk_job_id: 1, file_type: SummaryFile::ERROR)
        create(:summary_file, bulk_job_id: 1, file_type: SummaryFile::SUCCESS)
        ENV['AWS_REGION'] = 'region'
        ENV['AWS_ENDPOINT'] = 'https://aws-endpont.com'
        ENV['AWS_ACCESS_KEY_ID'] = 'access-key'
        ENV['AWS_SECRET_ACCESS_KEY'] = 'secret'
      end

      it 'deletes associated files from s3 and destroys bulk job object' do
        allow_any_instance_of(DeleteFileFromS3).to receive(:call).once
        BulkJobService.new(1).delete
        expect(BulkJob.count).to eq(0)
        expect(SummaryFile.count).to eq(0)
      end

      context 'when there is some error while destroying object' do
        it 'raises error' do
          allow_any_instance_of(DeleteFileFromS3).to receive(:call).once

          allow_any_instance_of(BulkJob).to receive(:destroy!).and_raise(PG::ForeignKeyViolation)
          expect { BulkJobService.new(1).delete }.to raise_error(
            ExceptionHandler::InvalidDataError,
            '025011||PG::ForeignKeyViolation'
          )
        end
      end
    end
  end

  describe '#move_paused_jobs_to_aborted' do
    before do
      create(:bulk_job, status: 'paused', started_at: 8.days.ago, id: 1)
      create(:bulk_job, status: 'in_progress', started_at: 8.days.ago, id: 2)
      create(:bulk_job, status: 'paused', started_at: 7.days.ago, id: 3)
      create(:bulk_job, status: 'paused', started_at: 6.days.ago, id: 4)
    end

    it 'moves paused jobs that are started more than 7 days ago' do
      BulkJobService.move_paused_jobs_to_aborted
      expect(BulkJob.aborted.count).to eq(2)
      expect(BulkJob.aborted.pluck(:id)).to match_array([1,3])
    end
  end
end

describe '.enqueue_retryable_jobs' do
  let(:tenant) { create(:tenant) }
  let(:bulk_job) do
    create(
      :bulk_job,
      status: STATUS_RETRYING,
      tenant_id: tenant.id,
      payload: {
        retryConfig: {
          noOfTimes: 3,
          timesRetried: 1,
          nextRetryAt: 20.minutes.ago.to_s
        }
      }
    )
  end

  context 'when bulk job is in retrying status and next_retry_at is in past' do
    before do
      allow_any_instance_of(Tenant).to receive(:running_job?).and_return(false)
    end

    it 'updates status to queued and enqueues BulkActionJob' do
      bulk_job
      expect {
        BulkJobService.enqueue_retryable_jobs
      }.to change { bulk_job.reload.status }.from(STATUS_RETRYING).to(STATUS_QUEUED)
      expect(BulkActionJob).to have_been_enqueued.with(bulk_job.id)
    end
  end

  context 'when bulk job is in retrying status but next_retry_at is in future' do
    let(:bulk_job) do
      create(
        :bulk_job,
        status: STATUS_RETRYING,
        tenant_id: tenant.id,
        payload: {
          retryConfig: {
            next_retry_at: 30.minutes.from_now.to_s
          }
        }
      )
    end
        
    it 'does not enqueue BulkActionJob or update status' do
      bulk_job
      expect {
        BulkJobService.enqueue_retryable_jobs
      }.not_to change { bulk_job.reload.status }
    end
  end

  context 'when bulk job is not in retrying status' do
    let(:bulk_job) do
      create(
        :bulk_job,
        status: STATUS_QUEUED,
        tenant_id: tenant.id,
        payload: {
          retryConfig: {
            nextRetryAt: (Time.now + 20.minutes).iso8601
          }
        }
      )
    end

    it 'does not enqueue BulkActionJob or update status' do
      bulk_job
      expect {
        BulkJobService.enqueue_retryable_jobs
      }.not_to change { bulk_job.reload.status }
    end
  end

  context 'when bulk job has no next_retry_at in payload' do
    let(:bulk_job) do
      create(
        :bulk_job,
        status: STATUS_RETRYING,
        tenant_id: tenant.id,
        payload: {}
      )
    end

    it 'does not enqueue BulkActionJob or update status' do
      bulk_job
      expect {
        BulkJobService.enqueue_retryable_jobs
      }.not_to change { bulk_job.reload.status }
    end
  end

  context 'when tenant has a running job' do
    before do
      allow_any_instance_of(Tenant).to receive(:running_job?).and_return(true)
    end

    it 'updates status but does not enqueue BulkActionJob' do
      bulk_job
      expect {
        BulkJobService.enqueue_retryable_jobs
      }.to change { bulk_job.reload.status }.from(STATUS_RETRYING).to(STATUS_QUEUED)
      
      expect(BulkActionJob).not_to have_been_enqueued
    end
  end
end
