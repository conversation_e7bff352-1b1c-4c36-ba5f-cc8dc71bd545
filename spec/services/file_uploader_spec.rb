require 'rails_helper'

RSpec.describe FileUploader do
  describe '#upload' do
    let(:remote_path){ 'assets/app_das123asda/sample.png' }
    let(:local_path){ 'tmp/sample.png' }
    before do
      s3_client = instance_double(Aws::S3::Resource)
      bucket = instance_double(Aws::S3::Bucket)
      @object = instance_double(Aws::S3::Object)
      allow(Aws::S3::Resource).to receive(:new).and_return(s3_client)
      allow(s3_client).to receive(:bucket).with(S3_BULK_ACTION_BUCKET).and_return(bucket)
      allow(bucket).to receive(:object).with(remote_path).and_return(@object)
      allow(File).to receive(:delete).with(local_path).and_return(1)
    end
    it 'should upload file to s3' do
      allow(@object).to receive(:upload_file).with(local_path).and_return(nil)

      expect(@object).to receive(:upload_file).with(local_path)
      expect(File).to receive(:delete).with(local_path)

      FileUploader.upload(local_path, remote_path)
    end

    it 'should throw file_upload_error if not uploaded' do
      allow(@object).to receive(:upload_file).with(local_path).and_raise(StandardError)

      expect(File).to receive(:delete).with(local_path)
      expect{ FileUploader.upload(local_path, remote_path) }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.file_upload_error)
    end
  end
end