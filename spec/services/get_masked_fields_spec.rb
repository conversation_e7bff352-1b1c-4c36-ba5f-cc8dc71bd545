# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetMaskedFields do
  describe "#call" do
    let(:user) { User.create(id: 1, name: '<PERSON>', tenant: Tenant.create(id: 99)) }
    before do
      Thread.current[:user] = user
      Thread.current[:token] = get_test_jwt(user.id, user.tenant_id)
      user.update(token: Thread.current[:token])
    end

    context 'with valid input' do
      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
          headers: {
            'Authorization' => "Bearer #{ user.token }"
          }
        ).to_return(
          status: 200,
          body: [
            {
              type: 'PHONE',
              name: 'phoneNumbers',
              description: 'Lead Phone',
              filterable: true,
              sortable: true,
              required: false,
              important: true,
              pickLists: nil,
              fieldConfigurations: [
                  {
                    id: nil,
                    type:'MASKING',
                    configuration: {
                      enabled:true,
                      profileIds: [1,2,3]
                    }
                  }
                ]
            }
          ].to_json,
          headers: {}
        )
      end

      it 'returns fields that are masked' do
        result = GetMaskedFields.new(LEAD, user.token).call
        expect(result.count).to eq(1)
        expect(result[0]['type']).to eq('PHONE')
      end
    end

    context 'when token is not present in thread' do
      before do
        Thread.current[:token] = nil
        user.update(token: nil)
      end

      it 'raises unauthorized error' do
        expect { GetMaskedFields.new(LEAD, user.token).call }.to raise_error(ExceptionHandler::AuthenticationError, '025002')
      end
    end

    context 'when fields are not available' do
      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
          headers: {
            'Authorization' => "Bearer #{ user.token }"
          }
        ).to_return(
          status: 404,
          body: {}.to_json,
          headers: {}
        )
      end

      it 'raises invalid data error' do
        expect { GetMaskedFields.new(LEAD, user.token).call }.to raise_error(ExceptionHandler::InvalidDataError, '025003')
      end
    end

    context 'when something went wrong' do
      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
          headers: {
            'Authorization' => "Bearer #{ user.token }"
          }
        ).to_return(
          status: 500,
          body: {}.to_json,
          headers: {}
        )
      end

      it 'raises invalid data error' do
        expect { GetMaskedFields.new(LEAD, user.token).call }.to raise_error(ExceptionHandler::InternalServerError, '025004')
      end
    end
  end
end
