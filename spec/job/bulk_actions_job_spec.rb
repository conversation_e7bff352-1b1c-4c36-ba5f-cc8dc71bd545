require 'rails_helper'

RSpec.describe BulkActionJob, type: :job do
  describe "#perform" do
    let!(:bulk_job) { create(:bulk_job, id: 10, status: 'queued', operation: UPDATE_OPERATION, user: User.create(name: '<PERSON>', tenant: Tenant.create({id: 99})), tenant_id:99 )}
    context "when performed" do
      before(:each) do
        allow_any_instance_of(EntityAction::Update).to receive(:perform)
      end

      it 'should add job to bulk_actions queue' do
        BulkActionJob.perform_later(10)
        expect(BulkActionJob).to(have_been_enqueued.at_least(:once)
                .with(10))
      end

      it 'should call EntityAction:Update' do
        expect_any_instance_of(EntityAction::Update).to receive(:perform)
        BulkActionJob.new.perform(bulk_job.id)
      end

      it 'should call EntityAction:Delete' do
        expect_any_instance_of(EntityAction::Delete).to receive(:perform)
        bulk_job.update({ operation: DELETE_OPERATION })
        BulkActionJob.new.perform(bulk_job.id)
      end

      it 'should call EntityAction:Email' do
        expect_any_instance_of(EntityAction::Email).to receive(:perform)
        bulk_job.update({ operation: EMAIL_OPERATION })
        BulkActionJob.new.perform(bulk_job.id)
      end

      it 'should call EntityAction:WhatsappMessage' do
        expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:perform)
        bulk_job.update({ operation: WHATSAPP_MESSAGE_OPERATION })
        BulkActionJob.new.perform(bulk_job.id)
      end

      it 'should set sidekiq job id on bulk job' do
        BulkActionJob.perform_later(10)
        expect(bulk_job.reload.sidekiq_job_id).not_to be_blank
      end

      context 'when bulk job is paused' do
        before do
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 1)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 2)
          create(:record, bulk_job: bulk_job, tenant_id: 99, entity_id: 3)
          expect(Rails.logger).to receive(:info).with("BULK JOB ROUND: #{bulk_job.id} | started")
        end

        context 'due to round robin' do
          it 'logs info, does not delete records' do
            expect_any_instance_of(EntityAction::Update).to receive(:perform).and_return({ round_robin_aborted: true })
            expect(Rails.logger).to receive(:info).with("BULK JOB ROUND: #{bulk_job.id} | Round Robin Paused")

            BulkActionJob.new.perform(bulk_job.id)
            expect(bulk_job.records.count).to eq(3)
          end
        end

        context 'due to manually paused' do
          it 'logs info, does not delete records' do
            expect_any_instance_of(EntityAction::Update).to receive(:perform).and_return({ paused: true })
            expect(Rails.logger).to receive(:info).with("BULK JOB ROUND: #{bulk_job.id} | Paused")

            BulkActionJob.new.perform(bulk_job.id)
            expect(bulk_job.records.count).to eq(3)
          end
        end
      end

      context 'when bulk job is retrying status' do
        let!(:whatsapp_message_bulk_job) { create(:bulk_job, id: 11, status: 'queued', operation: WHATSAPP_MESSAGE_OPERATION, user: User.create(name: 'Tony', tenant: Tenant.create({id: 100})), tenant_id: 100 )}

        before do
          create(:record, bulk_job: whatsapp_message_bulk_job, tenant_id: 99, entity_id: 1)
          create(:record, bulk_job: whatsapp_message_bulk_job, tenant_id: 99, entity_id: 2)
          create(:record, bulk_job: whatsapp_message_bulk_job, tenant_id: 99, entity_id: 3)
        end

        it 'does not delete records' do
          expect_any_instance_of(EntityAction::WhatsappMessage).to receive(:perform).and_return({ retrying: true })

          BulkActionJob.new.perform(whatsapp_message_bulk_job.id)
          expect(whatsapp_message_bulk_job.records.count).to eq(3)
        end
      end
    end
  end
end
