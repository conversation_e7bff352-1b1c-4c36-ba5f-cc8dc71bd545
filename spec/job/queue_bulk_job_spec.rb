# frozen_string_literal: true

require 'rails_helper'

RSpec.describe QueueBulkJob, type: :job do
  describe "#perform" do
    let(:tenant){ create(:tenant) }
    let!(:user){ create(:user, name: '<PERSON>', tenant: tenant) }
    let!(:bulk_job) { create(:bulk_job, status: 'queuing', operation: UPDATE_OPERATION, user: user, tenant_id: tenant.id )}
    let(:note_bulk_job) { create(:bulk_job, status: 'queuing', entity: 'note', operation: DELETE_OPERATION, user: user, tenant_id: tenant.id )}

    context 'when performed' do
      context 'when bulk job is not in queuing status' do
        before { bulk_job.completed! }
        it 'does noting' do
          expect(QueueBulkJob.new.perform(bulk_job.id)).to be_nil
        end
      end

      context 'when bulk job is in queuing status' do
        it 'calls EntityAction::Fetch service' do
          expect_any_instance_of(EntityAction::Fetch).to receive(:call).once
          QueueBulkJob.new.perform(bulk_job.id)
        end

        it 'calls EntityAction::FetchNotes service' do
          expect_any_instance_of(EntityAction::FetchNotes).to receive(:call).once
          QueueBulkJob.new.perform(note_bulk_job.id)
        end

        it 'updates satus of bulk job to queued status' do
          expect_any_instance_of(EntityAction::Fetch).to receive(:call).once
          QueueBulkJob.new.perform(bulk_job.id)
          expect(bulk_job.reload.queued?).to be_truthy
        end
      end
    end
  end
end
