require 'rails_helper'

RSpec.describe "Bulk Job controller", type: :request do
  let(:user){ Tenant.create(id: 99); User.create(id: 1, tenant_id: 99, name: '<PERSON>')}
  let(:tenant_data) { file_fixture('tenant-data.json').read }
  let(:valid_auth_token){ get_test_jwt(user.id, user.tenant_id) }
  let(:header_without_permission) { {
    'Authorization' => "Bearer #{get_test_jwt(user.id, user.tenant_id, false, false)}",
    'Content-Type' => 'application/json'
  } }
  let(:headers){
    {
      'Authorization' => "Bearer #{valid_auth_token}",
      'Content-Type' => 'application/json'
    }
  }

  let(:invalid_headers){
    {
      'Authorization' => "Bearer #{invalid_jwt}",
      'Content-Type' => 'application/json'
    }
  }

  let(:headers_without_authorization){
    {
      'Content-Type' => 'application/json'
    }
  }

  describe "#user authentication" do
    context 'headers without authorization' do
      before { post '/v1/bulk-action-jobs', params: '', headers: headers_without_authorization }
      it 'should throw invalid token error' do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
      end
    end
  end

  describe '#create' do
    let(:params){
      {
        entity: LEAD,
        operation: UPDATE_OPERATION,
        filters: {
          "jsonRule": {
            "rules": [
              {
                "operator": "equal",
                "id": "ownerId",
                "field": "ownerId",
                "type": "long",
                "value": 11
              }
            ],
            "condition": "AND",
            "valid": true
          }
        },
        executeWorkflow: true,
        executeScoreRule: true,
        payload: { ownerId: 11 }
      }.with_indifferent_access
    }

    let(:email_params) {
      {
        "entity":"lead",
        "operation":"email",
        "execute_workflow":"false",
        "filters":{
          "sort":"updatedAt, desc",
          "page":"10",
          "size":"100",
          "jsonRule":{
            "rules":[
              {
                "operator":"equal",
                "id":"ownerId",
                "field":"ownerId",
                "type":"long",
                "value":"11"
              }
            ],
            "condition":"AND",
            "valid":"true"
          }
        },
        "payload":{
          "subject":"Test subject",
          "body":"Test Email Body",
          "emailTemplateId":"123",
          # Currently, receiving String instead of Array from Web
          "cc":"[{\"entity\":\"user\",\"id\":\"1\",\"name\":\"User Name\",\"email\":\"<EMAIL>\"}]",
          "bcc":"[{\"entity\":\"user\",\"id\":\"2\",\"name\":\"Other User Name\",\"email\":\"<EMAIL>\"}]",
          "attachments":[
            {
              "id":nil,
              "data": Rack::Test::UploadedFile.new(file_fixture('json_rule.json'), 'application/json'),
              "fileName":"somefilename.pdf"
            }
          ]
        }
      }
    }

    let(:whatsapp_message_params) do
      {
        "entity":"lead",
        "operation":"whatsappMessage",
        "execute_workflow":"false",
        "filters":{
          "sort":"updatedAt, desc",
          "page":"10",
          "size":"100",
          "jsonRule":{
            "rules":[
              {
                "operator":"equal",
                "id":"ownerId",
                "field":"ownerId",
                "type":"long",
                "value":"11"
              }
            ],
            "condition":"AND",
            "valid":"true"
          }
        },
        "payload": {
          "connectedAccount": {
            "id": 123,
            "name": "Whatsapp Account 1"
          },
          "whatsappTemplate": {
            "id": 123,
            "name": "Whatsapp Template 1"
          },
          "messageSendTo": [
            {
              "name": "Record's primary phone number",
              "type": "PRIMARY_PHONE_NUMBER"
            }
          ]
        }
      }.with_indifferent_access
    end
    before do
      Thread.current[:user] = user
      Thread.current[:token] = valid_auth_token
      stub_request(:get, SERVICE_IAM + "/v1/tenants").
        with(
          headers: {
            'Accept' => 'application/json',
            'Authorization' => "Bearer #{valid_auth_token}"
          }
        ).
        to_return(status: 200, body: tenant_data, headers: {})
    end

    context 'with valid data' do
      context 'when update operation' do
        before do
          expect {
            post '/v1/bulk-action-jobs', params: params.to_json, headers: headers
          }.to have_enqueued_job(QueueBulkJob)
        end

        it 'should create job and return its id' do
          expect(response).to have_http_status(:created)
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          bulk_job = BulkJob.find data['id']
          expect(bulk_job.filters).to eq({
            "jsonRule": {
              "rules": [
                {
                  "operator": "equal",
                  "id": "ownerId",
                  "field": "ownerId",
                  "type": "long",
                  "value": 11
                }
              ],
              "condition": "AND",
              "valid": true
            }
          }.with_indifferent_access)
          expect(bulk_job.execute_score_rule).to eq(true)
          expect(bulk_job.payload).to eq({ ownerId: 11 }.with_indifferent_access)
        end
      end

      context 'when email operation' do
        it 'should create bulk job and upload attachments to S3' do
          expect(FileUploader).to receive(:upload).with(/tmp\/RackMultipart/, /somefilename.pdf/)
          expect {
            post '/v1/bulk-action-jobs', params: email_params, headers: headers.except('Content-Type')
          }.to have_enqueued_job(QueueBulkJob)

          expect(response).to have_http_status(:created)
          data = JSON.parse(response.body)
          expect(data).to have_key('id')
          bulk_job = BulkJob.find data['id']
          expect(bulk_job.filters).to eq({
            "sort": "updatedAt, desc",
            "page": "10",
            "size": "100",
            "jsonRule": {
              "rules": [
                {
                  "operator": "equal",
                  "id": "ownerId",
                  "field": "ownerId",
                  "type": "long",
                  "value": "11"
                }
              ],
              "condition": "AND",
              "valid": "true"
            }
          }.with_indifferent_access)
          expect(bulk_job.payload.keys).to match_array(%w[subject body emailTemplateId cc bcc attachments])
          expect(bulk_job.payload['attachments'].first['fileName']).to eq('somefilename.pdf')
        end
      end

      context 'when whatsapp message operation' do
        context 'when whatsapp credits are present for bulk action' do
          before do
            stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-credits/status").with(
              headers: {
                'Authorization' => "Bearer #{valid_auth_token}"
              }
            ).to_return(
              status: 200,
              body: {
                "availableForBulkMessages": true
              }.to_json,
              headers: {}
            )

            expect {
              post '/v1/bulk-action-jobs', params: whatsapp_message_params.to_json, headers: headers
            }.to have_enqueued_job(QueueBulkJob)
          end

          it 'should create job and return its id' do
            expect(response).to have_http_status(:created)
            data = JSON.parse(response.body)
            expect(data).to have_key('id')
            bulk_job = BulkJob.find data['id']
            expect(bulk_job.filters).to eq({
              "sort":"updatedAt, desc",
              "page":"10",
              "size":"100",
              "jsonRule":{
                "rules":[
                  {
                    "operator":"equal",
                    "id":"ownerId",
                    "field":"ownerId",
                    "type":"long",
                    "value":"11"
                  }
                ],
                "condition":"AND",
                "valid":"true"
              }
            }.with_indifferent_access)
            expect(bulk_job.payload).to eq({
              "connectedAccount": {
                "id": 123,
                "name": "Whatsapp Account 1"
              },
              "whatsappTemplate": {
                "id": 123,
                "name": "Whatsapp Template 1"
              },
              "messageSendTo": [
                {
                  "name": "Record's primary phone number",
                  "type": "PRIMARY_PHONE_NUMBER"
                }
              ]
            }.with_indifferent_access)
          end
        end

        context 'when sufficient whatsapp credits are not present for bulk action' do
          before do
            stub_request(:get, "#{SERVICE_MESSAGE}/v1/messages/whatsapp-credits/status").with(
              headers: {
                'Authorization' => "Bearer #{valid_auth_token}"
              }
            ).to_return(
              status: 200,
              body: {
                "availableForBulkMessages": false
              }.to_json,
              headers: {}
            )

            expect {
              post '/v1/bulk-action-jobs', params: whatsapp_message_params.to_json, headers: headers
            }.not_to have_enqueued_job(QueueBulkJob)
          end

          it 'should not create job' do
            expect(response).to have_http_status(:unprocessable_entity)
            data = JSON.parse(response.body)
            expect(data).to eq({"errorCode"=>"025012", "message"=>"Insufficient whatsapp credits balance for bulk action"})
            expect(BulkJob.count).to eq(0)
          end
        end
      end
    end

    context 'with invalid data' do
      context 'when operation is blank' do
        before do
          invalid_params = params.clone
          invalid_params[:operation] = nil
          post '/v1/bulk-action-jobs', params: invalid_params.to_json, headers: headers
        end

        it 'should throw invalid data error' do
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_data)
          expect(response.parsed_body['message']).to match("Operation can't be blank")
        end
      end

      context 'when filesize exceeded' do
        before { expect(File).to receive(:size).and_return(30_000_000) }

        it 'should return proper error code' do
          post '/v1/bulk-action-jobs', params: email_params, headers: headers.except('Content-Type')

          expect(response).to have_http_status(:unprocessable_entity)
          data = JSON.parse(response.body)
          expect(data['errorCode']).to eq('025007')
          expect(data['message']).to eq('File Size Limit Exceeded')
        end
      end
    end

    context 'with invalid token' do
      before { post '/v1/bulk-action-jobs', params: params.to_json, headers: invalid_headers }
      it 'should throw invalid token error' do
        expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
      end
    end
  end

  describe '#search' do
    context 'with bulk jobs present' do
      context 'when execute workflow is false' do
        before(:each) do
          create_list(:bulk_job, 2, user_id: user.id, tenant_id: user.tenant_id, successful: 3, failure: 0)
          BulkJob.first.create_error_summary_file
          BulkJob.last.create_summary_file
          post '/v1/bulk-action-jobs/search?page=0&size=10&sort=updatedAt,desc', headers: headers
        end

        it 'should return list of all bulk jobs' do
          expect(response).to have_http_status(:ok)
          bulk_jobs = JSON.parse(response.body)
          bulk_jobs['content'].each do |bulk_job|
            expect(bulk_job['status']).to eq('COMPLETED')
            expect(bulk_job['entity']).to eq('LEAD')
            expect(bulk_job['operation']).to eq('UPDATE')
            expect(bulk_job['payload']).not_to be_blank
            expect(bulk_job['submittedAt']).to be < DateTime.now.ago(1.hour).to_s
            expect(bulk_job['completedAt']).to be < DateTime.now.to_s
            expect(bulk_job['submittedBy']).to eq({ "id"=> 1, "name"=> 'Tony Stark' })
            expect(bulk_job['successful']).to be(3)
            expect(bulk_job['failure']).to be(0)
            expect(bulk_job['executeWorkflow']).to be(false)
            expect(bulk_job['executeScoreRule']).to be(false)
            expect(bulk_jobs['content'][0]['recordActions']).to eq({
              "delete"=>true,
              "update"=>true
            })
          end

          expect(bulk_jobs.except('content')).to match({"totalElements"=>2, "totalPages"=>1, "last"=>true, "numberOfElements"=>2, "first"=>true, "size"=>10, "number"=>0})
        end

        it 'has record actions' do
          bulk_jobs = JSON.parse(response.body)
          bulk_jobs['content']
        end

        it 'has success summary file on record' do
          bulk_jobs = JSON.parse(response.body)
          expect(bulk_jobs['content'][0]['successFile']).to eq SummaryFile::SUCCESS_FILE_NAME
          expect(bulk_jobs['content'][0]['errorFile']).to eq nil
        end

        it 'has error summary file on record' do
          bulk_jobs = JSON.parse(response.body)
          expect(bulk_jobs['content'][1]['successFile']).to eq nil
          expect(bulk_jobs['content'][1]['errorFile']).to eq SummaryFile::ERROR_FILE_NAME
        end
      end

      context 'when execute workflow is true' do
        before(:each) do
          create_list(:bulk_job, 2, user_id: user.id, tenant_id: user.tenant_id, successful: 3, failure: 0, execute_workflow: true, execute_score_rule: true)
          post '/v1/bulk-action-jobs/search?page=0&size=10&sort=updatedAt,desc', headers: headers
        end

        it 'should return list of all bulk jobs' do
          expect(response).to have_http_status(:ok)
          bulk_jobs = JSON.parse(response.body)
          bulk_jobs['content'].each do |bulk_job|
            expect(bulk_job['status']).to eq('COMPLETED')
            expect(bulk_job['entity']).to eq('LEAD')
            expect(bulk_job['operation']).to eq('UPDATE')
            expect(bulk_job['payload']).not_to be_blank
            expect(bulk_job['submittedAt']).to be < DateTime.now.ago(1.hour).to_s
            expect(bulk_job['completedAt']).to be < DateTime.now.to_s
            expect(bulk_job['submittedBy']).to eq({ "id"=> 1, "name"=> 'Tony Stark' })
            expect(bulk_job['successful']).to be(3)
            expect(bulk_job['failure']).to be(0)
            expect(bulk_job['executeWorkflow']).to be(true)
            expect(bulk_job['executeScoreRule']).to be(true)
          end

          expect(bulk_jobs.except('content')).to match(
            {
              'totalElements' => 2, 'totalPages' => 1,
              'last' => true, 'numberOfElements' => 2,
              'first' => true, 'size' => 10,
              'number' => 0
            }
          )
        end
      end
    end
  end

  describe '#summmary-file' do
    context 'with valid job id' do
      before(:each) do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id)
        allow(GetPresignedS3Url).to receive(:remote_url).and_return("http//www.aws.com/test.csv")
      end

      context 'when file exists' do
        context 'when owner downloads file' do
          before do
            create(:summary_file, bulk_job_id: @bulk_job.id, file_type: SummaryFile::SUCCESS)
            get "/v1/bulk-action-jobs/#{@bulk_job.id}/summary-file", headers: headers
          end

          it 'should return url' do
            expect(response).to have_http_status(:ok)
            bulk_jobs = JSON.parse(response.body)
          end
        end

        context 'when file is of different tenant' do
          before do
            job = create(:bulk_job, tenant: create(:tenant))
            create(:summary_file, bulk_job_id: job.id, file_type: SummaryFile::SUCCESS)
            get "/v1/bulk-action-jobs/#{job.id}/summary-file", headers: headers
          end

          it 'should return unauthorized' do
            expect(response).to have_http_status(:unauthorized)
          end
        end
      end

      context 'when file does not exists' do
        it 'should return 404' do
          get "/v1/bulk-action-jobs/#{@bulk_job.id}/summary-file", headers: headers
          expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
        end
      end

      context 'with invalid job id' do
        it 'should return 404' do
          get "/v1/bulk-action-jobs/1111/summary-file", headers: headers
          expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
        end
      end
    end
  end

  describe '#error-file' do
    context 'with valid job id' do
      before(:each) do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id)
        allow(GetPresignedS3Url).to receive(:remote_url).and_return("http//www.aws.com/test.csv")
      end

      context 'when file exists' do
        context 'when owner downloads file' do
          before do
            create(:summary_file, bulk_job_id: @bulk_job.id, file_type: SummaryFile::ERROR)
            get "/v1/bulk-action-jobs/#{@bulk_job.id}/error-file", headers: headers
          end

          it 'should return url' do
            expect(response).to have_http_status(:ok)
            bulk_jobs = JSON.parse(response.body)
          end
        end

        context 'when file is of different tenant' do
          before do
            job = create(:bulk_job, tenant: create(:tenant))
            create(:summary_file, bulk_job_id: job.id, file_type: SummaryFile::ERROR)
            get "/v1/bulk-action-jobs/#{job.id}/summary-file", headers: headers
          end

          it 'should return unauthorized' do
            expect(response).to have_http_status(:unauthorized)
          end
        end
      end

      context 'when file does not exists' do
        it 'should return 404' do
          get "/v1/bulk-action-jobs/#{@bulk_job.id}/error-file", headers: headers
          expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
        end
      end

      context 'with invalid job id' do
        it 'should return 404' do
          get "/v1/bulk-action-jobs/1111/error-file", headers: headers
          expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
        end
      end
    end
  end

  describe '#attachment' do
    context 'with valid job id' do
      before(:each) do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id)
        allow(GetPresignedS3Url).to receive(:remote_url).and_return("http//www.aws.com/test.jpg")
      end

      context 'when attachment exists' do
        context 'when owner downloads file' do
          before do
            @bulk_job.update!(operation: WHATSAPP_MESSAGE_OPERATION, payload: {
              'whatsappTemplate' => {
                'dynamicTemplateMediaName' => 'test.jpg'
              }
            })
            get "/v1/bulk-action-jobs/#{@bulk_job.id}/attachment", headers: headers
          end

          it 'should return url' do
            expect(response).to have_http_status(:ok)
            response_body = JSON.parse(response.body)
            expect(response_body['url']).to eq("http//www.aws.com/test.jpg")
          end
        end

        context 'when file is of different tenant' do
          before do
            job = create(:bulk_job, tenant: create(:tenant), operation: WHATSAPP_MESSAGE_OPERATION, payload: {
              'whatsappTemplate' => {
                'dynamicTemplateMediaName' => 'test.jpg'
              }
            })
            get "/v1/bulk-action-jobs/#{job.id}/attachment", headers: headers
          end

          it 'should return unauthorized' do
            expect(response).to have_http_status(:unauthorized)
          end
        end
      end

      context 'when attachment does not exist' do
        before do
          @bulk_job.update!(operation: WHATSAPP_MESSAGE_OPERATION, payload: {
            'whatsappTemplate' => {}
          })
        end

        it 'should return 404' do
          get "/v1/bulk-action-jobs/#{@bulk_job.id}/attachment", headers: headers
          expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
        end
      end

      context 'with invalid job id' do
        it 'should return 404' do
          get "/v1/bulk-action-jobs/1111/attachment", headers: headers
          expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
        end
      end

      context 'with non-whatsapp operation' do
        before do
          @bulk_job.update!(operation: EMAIL_OPERATION)
        end

        it 'should return 404' do
          get "/v1/bulk-action-jobs/#{@bulk_job.id}/attachment", headers: headers
          expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
        end
      end
    end
  end

  describe '#abort' do
    context 'with valid job id' do
      before(:each) do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, id: 1, status: 'queued')
      end

      context 'with invalid permission' do
        context 'when current user is not bulk job creator user' do
          context 'and user does not have update all on bulk job entity' do
            before do
              create(:user, tenant_id: user.tenant_id, id: 2)
              @bulk_job.update(user_id: 2)
            end

            it 'raises unauthorized error' do
              post '/v1/bulk-action-jobs/1/abort', headers: header_without_permission
              expect(response.parsed_body['errorCode']).to eq('025002')
            end
          end
        end
      end

      context 'when bulk job exists' do
        context 'and it is not in aborted or completed state' do
          it 'aborts the bulk job and schedules job to for further processing' do
            post '/v1/bulk-action-jobs/1/abort', headers: headers
            expect(@bulk_job.reload.aborting?).to be_truthy
            expect(response).to be_ok
            expect(response.parsed_body['id']).to eq('1')
          end
        end

        context 'and it is in aborted or completed state' do
          before { @bulk_job.completed! }

          it 'does not abort job' do
            post '/v1/bulk-action-jobs/1/abort', headers: headers
            expect(@bulk_job.reload.completed?).to be_truthy
            expect(response).not_to be_ok
            expect(response.parsed_body['errorCode']).to eq('025010')
            expect(response.parsed_body['message']).to eq('Cannot abort the bulk job which is in completed state')
          end
        end
      end
    end
  end

  describe '#pause' do
    context 'with valid job id' do
      before(:each) do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, id: 1, status: 'queued')
      end

      context 'with invalid permission' do
        context 'when current user is not bulk job creator user' do
          context 'and user does not have update all on bulk job entity' do
            before do
              create(:user, tenant_id: user.tenant_id, id: 2)
              @bulk_job.update(user_id: 2)
            end

            it 'raises unauthorized error' do
              post '/v1/bulk-action-jobs/1/pause', headers: header_without_permission
              expect(response.parsed_body['errorCode']).to eq('025002')
            end
          end
        end
      end

      context 'when bulk job exists' do
        context 'and it is not in paused or completed state' do
          it 'pauses the bulk job and schedules job to for further processing' do
            post '/v1/bulk-action-jobs/1/pause', headers: headers
            expect(@bulk_job.reload.paused?).to be_truthy
            expect(response).to be_ok
            expect(response.parsed_body['id']).to eq('1')
          end
        end

        context 'and it is in paused or completed state' do
          before { @bulk_job.completed! }

          it 'does not pause the job' do
            post '/v1/bulk-action-jobs/1/pause', headers: headers
            expect(@bulk_job.reload.completed?).to be_truthy
            expect(response).not_to be_ok
            expect(response.parsed_body['errorCode']).to eq('025008')
            expect(response.parsed_body['message']).to eq('Cannot pause the bulk job which is in completed state')
          end
        end
      end
    end
  end

  describe '#resume' do
    context 'with valid job id' do
      before(:each) do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, id: 1, status: 'paused')
      end

      context 'with invalid permission' do
        context 'when current user is not bulk job creator user' do
          context 'and user does not have update all on bulk job entity' do
            before do
              create(:user, tenant_id: user.tenant_id, id: 2)
              @bulk_job.update(user_id: 2)
            end

            it 'raises unauthorized error' do
              post '/v1/bulk-action-jobs/1/resume', headers: header_without_permission
              expect(response.parsed_body['errorCode']).to eq('025002')
            end
          end
        end
      end

      context 'when bulk job exists' do
        context 'and it is in paused state' do
          it 'resumes the bulk job and schedules job to for further processing' do
            expect { post '/v1/bulk-action-jobs/1/resume', headers: headers }.to have_enqueued_job
            expect(@bulk_job.reload.queued?).to be_truthy
            expect(response).to be_ok
            expect(response.parsed_body['id']).to eq('1')
          end
        end

        context 'and it is not in paused state' do
          before { @bulk_job.completed! }

          it 'does not resume job' do
            expect { post '/v1/bulk-action-jobs/1/resume', headers: headers }.not_to have_enqueued_job
            expect(@bulk_job.reload.completed?).to be_truthy
            expect(response).not_to be_ok
            expect(response.parsed_body['errorCode']).to eq('025009')
            expect(response.parsed_body['message']).to eq('Cannot resume the bulk job which is in completed state')
          end
        end
      end
    end
  end

  describe '#destroy' do
    context 'with valid job id' do
      before(:each) do
        @bulk_job = create(:bulk_job, user_id: user.id, tenant_id: user.tenant_id, id: 1, status: 'paused')
      end

      context 'when bulk job is from another tenant' do
        it 'raises error' do
          another_user = create(:user, id: 22)
          create(:bulk_job, tenant_id: another_user.tenant_id, user_id: 22, id: 2)
          delete '/v1/bulk-action-jobs/2', headers: headers
          expect(response.parsed_body['errorCode']).to eq('025002')
        end
      end

      context 'with invalid permission' do
        context 'and user does not have update all on bulk job entity' do
          before do
            create(:user, tenant_id: user.tenant_id, id: 2)
            @bulk_job.update(user_id: 2)
          end

          it 'raises unauthorized error' do
            delete '/v1/bulk-action-jobs/1', headers: header_without_permission
            expect(response.parsed_body['errorCode']).to eq('025002')
          end
        end
      end

      context 'when bulk job exists' do
        context 'with whatsapp template media' do
          before do
            @bulk_job.update!(operation: WHATSAPP_MESSAGE_OPERATION, payload: {
              'whatsappTemplate' => {
                'dynamicTemplateMediaName' => 'test.jpg'
              }
            })
          end

          it 'deletes the bulk job and its attachments' do
            delete_file_service = instance_double(DeleteFileFromS3)
            expect(DeleteFileFromS3).to receive(:new).with([
              "#{@bulk_job.remote_file_location}/test.jpg"
            ]).and_return(delete_file_service)
            expect(delete_file_service).to receive(:call)

            delete '/v1/bulk-action-jobs/1', headers: headers
            expect(BulkJob.count).to eq(0)
            expect(response).to be_ok
          end
        end

        context 'with summary and error files' do
          before do
            create(:summary_file, bulk_job_id: @bulk_job.id, file_type: SummaryFile::SUCCESS)
            create(:summary_file, bulk_job_id: @bulk_job.id, file_type: SummaryFile::ERROR)
          end

          it 'deletes the bulk job and its files' do
            delete_file_service = instance_double(DeleteFileFromS3)
            expect(DeleteFileFromS3).to receive(:new).with([
              "#{@bulk_job.remote_file_location}/#{SummaryFile::SUCCESS_FILE_NAME}",
              "#{@bulk_job.remote_file_location}/#{SummaryFile::ERROR_FILE_NAME}"
            ]).and_return(delete_file_service)
            expect(delete_file_service).to receive(:call)
            delete '/v1/bulk-action-jobs/1', headers: headers
            expect(BulkJob.count).to eq(0)
            expect(response).to be_ok
          end
        end

        it 'deletes the bulk job' do
          delete '/v1/bulk-action-jobs/1', headers: headers
          expect(BulkJob.count).to eq(0)
          expect(response).to be_ok
        end
      end
    end
  end
end
