module TestHelper

  def get_test_jwt(user_id = '1', tenant_id= '99', update_all = true, delete_all = false)
    payload = {
      iss: 'sell',
      data: {
        expiry: (Time.current + 1.hour).to_i,
        userId: user_id.to_s,
        tenantId: tenant_id.to_s,
        expiresIn: (Time.current + 1.hour).to_i,
        tokenType: 'bearer',
        accessToken: SecureRandom.uuid,
        username: '<EMAIL>',
        permissions:[{
          'id' => 12,
          'name' =>  'lead',
          'description' =>  'has access to lead',
          'limits' =>  -1,
          'units' =>  'count',
          'action' =>  {
            'read' => true,
            'write' => true,
            'update' => true,
            'delete' => true,
            'email' => false,
            'call' => false,
            'sms' => false,
            'task' => false,
            'note' => false,
            'readAll' => false,
            'updateAll' => update_all,
            'deleteAll' => delete_all,
            "random" => true
          }
        }]
      }
    }
    JWT.encode payload, nil, 'none'
  end

  def invalid_jwt
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
  end

  def jwt_with_insufficient_data
    payload = { iss: 'sell', data: { expiry: (Time.current + 1.hour).to_i, accessToken: SecureRandom.uuid,}}
    JWT.encode payload, nil, 'none'
  end
end
