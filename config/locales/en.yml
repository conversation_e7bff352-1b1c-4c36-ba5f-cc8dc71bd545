# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  email_not_found: "No email available"
  not_found: "Email Template not found"
  cannot_read_email_template: "Uhoh! You do not have the necessary permission to view Email Templates"
  connected_account_not_found: "Your email account seems to be disconnected. Please connect again. If the issue persists, please contact our support team."
  invalid_provider: "Uhoh! The email address seems to be invalid, please enter a valid email and try."
  authorization_error: "Invalid credentials"
  third_party_authorization_error: "Third party api auth error, please reconnect your account."
  outlook_authorization_error: "Oops! The authentication failed, please try after a few minutes."
  smtp_unauthorization: "Oops! The authentication failed, please reconnect your account."
  no_email_permission: "Uhoh! You don't have enough permissions to send email."
  inactive_email_template: "Email Template is Inactive"
  rate_limit_error: "Rate limit exceeded for your connected email account"
  unsupported_media_type: "Oops! Your provider does not support the requested format"
  http_method_not_implemented: "Uhoh! Your provider does not support the request"
  bad_gateway: "Uhoh! Your request cannot be processed due to bad gateway"
  service_unavailable: "Uhoh! Your connected account service is unavailable, please contact your email provider."
  gateway_timeout: "Oops! Gateway timed out"
  no_contact_associated: "Uhoh! No Contact associated with Deal"
  something_went_wrong: "Something went wrong."
  could_not_delete_file: "Could not delete file."
  bulk_jobs:
    cannot_change_status: 'Cannot %{to} the bulk job which is in %{current} state'
    cannot_change_campaign_action_job_status: 'Cannot change the status of the campaign action job manually'
  whatsapp_message:
    insufficient_whatsapp_credits_for_bulk: 'Insufficient whatsapp credits balance for bulk action'
    template_entity_and_preview_entity_mismatch: 'Template entity mismatch or All template variables are not mapped'
    message_not_allowed: 'Conversation permission is not present'
    no_whatsapp_template_read_permission: "Uhoh! User doesn't have permission to read whatsapp template"
    whatsapp_template_not_found: 'Whatsapp template not found'
    third_party_api_error: 'Third party api error'
    inactive_whatsapp_template: 'Whatsapp Template is not Approved'
    chatbot_in_progress: 'Cannot send message as chatbot is in progress'
