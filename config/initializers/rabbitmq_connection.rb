class RabbitmqConnection
  @@channel = nil

  def self.get_channel
    return @@channel if @@channel.present?
    rabbitmq_connection = Bunny.new(
      host: <PERSON><PERSON><PERSON>['RABBITMQ_HOST'],
      user: <PERSON>N<PERSON>['RABBITMQ_USER'],
      pass: ENV['RABBITMQ_PASSWORD'],
      vhost: ENV['RABBITMQ_VIRTUAL_HOST']).start
    @@channel = rabbitmq_connection.create_channel
    @@channel.prefetch(1)
    @@channel
  end

  def self.get_exchange exchange_name
    get_channel.topic(exchange_name, durable: true)
  end

  def self.subscribe exchange_name, routing_key, queue_name
    exchange = get_exchange(exchange_name)
    get_channel.queue(queue_name, auto_delete: false, durable: true).bind(exchange, routing_key: routing_key).subscribe do |delivery_info, metadata, payload|
      begin
        yield payload
      rescue Exception => e
        Rails.logger.info "BulkActionJob:RabbitMQ exception: routing_key: #{routing_key} | message: #{e.to_s}"
        if e.to_s.downcase.include?('activerecord') || e.to_s.downcase.include?('connection')
          ActiveRecord::Base.connection.close if ActiveRecord::Base.connection
          ActiveRecord::Base.connection_pool.with_connection do
            yield payload
          end
        end
      ensure
        ActiveRecord::Base.clear_active_connections!
      end
    end
  end
end
