# frozen_string_literal: true

LEAD = 'lead'
DEAL = 'deal'
CONTACT = 'contact'
TASK = 'task'
CALL_LOG = 'call'
USER = 'user'
EMAIL = 'email'
COMPANY = 'company'
MEETING = 'meeting'
NOTE = 'note'
PRODUCTIVITY = 'productivity'
UPDATE_OPERATION = 'update'
DELETE_OPERATION = 'delete'
EMAIL_OPERATION = 'email'
WHATSAPP_MESSAGE_OPERATION = 'whatsapp_message'
REDIS_HOST = ENV['REDIS_HOST']
SCHEDULER_EXCHANGE = 'ex.scheduler'
BULK_ACTIONS_EXCHANGE = 'ex.bulkActions'
MESSAGE_EXCHANGE = 'ex.message'
BULK_JOB_PAUSED = 'bulkActions.paused'
COMPLETED_WITH_ERROR = 'COMPLETED_WITH_ERROR'
COMPLETED = 'COMPLETED'
ERROR = 'error'
EMBARK = 'embark'

ENTITIES_PAGE_OFFSET_ONE = [CALL_LOG, EMAIL, MEETING].freeze

DEFAULT_SK_QUEUE = 'default'
DELETE_SK_QUEUE = 'delete'
QUEUE_SK_QUEUE = 'queue_job'
JWT_KEY = 'test'

if Rails.env.development? || Rails.env.test?
  SERVICE_EMAIL = 'http://localhost:3000'
  SERVICE_MEETING = 'http://localhost:3000'
  SERVICE_SALES = 'http://localhost:8086'
  SERVICE_DEAL = 'http://localhost:8090'
  SERVICE_IAM = 'http://localhost:8081'
  SERVICE_SEARCH = 'http://localhost:8083'
  SERVICE_PRODUCTIVITY = 'http://localhost:8087'
  SERVICE_CALL = 'http://localhost:3000'
  SERVICE_EMAIL = 'http://localhost:3000'
  SERVICE_COMPANY = 'http://localhost:3000'
  SERVICE_MESSAGE = 'http://localhost:3000'
  SERVICE_CONFIG = 'http://localhost:8086'
  S3_BULK_ACTION_BUCKET = 'qa-bulk-actions'
else
  SERVICE_EMAIL = 'http://sd-emails'
  SERVICE_MEETING = 'http://sd-meetings'
  SERVICE_SALES = 'http://sd-sales'
  SERVICE_IAM = 'http://sd-iam'
  SERVICE_DEAL = 'http://sd-deal'
  SERVICE_SEARCH = 'http://sd-search'
  SERVICE_PRODUCTIVITY = 'http://sd-productivity'
  SERVICE_CALL = 'http://sd-call'
  SERVICE_EMAIL = 'http://sd-emails'
  SERVICE_COMPANY = 'http://sd-company'
  SERVICE_MESSAGE = 'http://sd-message'
  SERVICE_CONFIG = 'http://sd-config'
  S3_BULK_ACTION_BUCKET = ENV['S3_BULK_ACTION_BUCKET']
end

STATUS_QUEUING = 'queuing'
STATUS_QUEUED = 'queued'
STATUS_IN_PROGRESS = 'in_progress'
STATUS_PAUSED = 'paused'
STATUS_ABORTED = 'aborted'
STATUS_COMPLETED = 'completed'
STATUS_ABORTING = 'aborting'
STATUS_SUCCEEDED = 'succeeded'
STATUS_FAILED = 'failed'
STATUS_RETRYING = 'retrying'

INSUFFICIENT_WHATSAPP_CREDITS_REASON = 'insufficient_whatsapp_credits'.freeze
INACTIVE_WHATSAPP_TEMPLATE_REASON = 'inactive_whatsapp_template'.freeze
TEMPLATE_ENTITY_MISMATCH_OR_VARIABLSES_NOT_MAPPED_REASON = 'template_entity_mismatch_or_variables_not_mapped'.freeze
CAMPAIGN_ACTION_COMMAND = 'campaign.action.command'
CAMPAIGN_ACTION_COMMAND_QUEUE = 'q.campaign.action.command.bulkActions'
CAMPAIGN_EXCHANGE = 'ex.campaign'
START = 'START'.freeze
PAUSE = 'PAUSE'.freeze
RESUME = 'RESUME'.freeze
ABORT = 'ABORT'.freeze
BULK_ACTION = 'bulk_action'.freeze
CAMPAIGN_ACTION = 'campaign_action'.freeze
CAMPAIGN_ACTIVITY_BULK_JOB_STARTED = 'bulkActions.activity.job.started'.freeze
CAMPAIGN_ACTIVITY_BULK_JOB_STATUS = 'bulkActions.activity.job.status'.freeze
CAMPAIGN_ENTITY_ACTIVITY_STATUS = 'bulkActions.campaign.entity.activity.status'.freeze
SUCCESS = 'success'.freeze
SENT = 'sent'.freeze
DELIVERED = 'delivered'.freeze
READ = 'read'.freeze
FAILED = 'failed'.freeze
SENDING = 'sending'.freeze
RECEIVED = 'received'.freeze
WHATSAPP_MESSAGE_STATUS_UPDATED = 'whatsapp.message.status.updated'.freeze
WHATSAPP_MESSAGE_STATUS_UPDATED_QUEUE = 'q.whatsapp.message.status.updated.bulkActions'.freeze
WHATSAPP_CAMPAIGN_ACTIVITY_ABORTED = 'bulkActions.whatsapp.campaign.activity.aborted'.freeze
