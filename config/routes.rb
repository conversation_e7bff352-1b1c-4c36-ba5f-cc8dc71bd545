require 'sidekiq/web'
Sidekiq::Web.use ActionDispatch::Cookies
Sidekiq::Web.use ActionDispatch::Session::CookieStore, key: "_interslice_session"

Rails.application.routes.draw do
  mount Rswag::Api::Engine => '/v2'
  get 'health', to: 'application#health'
  get "/v0e8136370948463e/bulk-action-jobs/health", to: "health#status"

  mount Sidekiq::Web, at: "/sidekiq"
  namespace :v1 do
    resources :bulk_jobs, path: "bulk-action-jobs", only: [:create, :index, :destroy] do
      member do
        get :error_file, path: 'error-file'
        get :summary_file, path: 'summary-file'
        get :attachment, path: 'attachment'
        post :abort
        post :pause
        post :resume
      end
      collection do
        post :search
      end
    end
  end
  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html
end
