class CreateBulkJobs < ActiveRecord::Migration[6.1]
  def change
    create_table :bulk_jobs do |t|
      t.bigint :user_id, null: false
      t.bigint :tenant_id, null: false
      t.string :entity, null: false
      t.string :operation, null: false
      t.jsonb :filters, null: false
      t.jsonb :payload
      t.integer :number_of_records
      t.datetime :started_at
      t.datetime :completed_at
      t.boolean :execute_workflow, default: false
      t.integer :status, default: 0, null: false

      t.timestamps
    end
  end
end
