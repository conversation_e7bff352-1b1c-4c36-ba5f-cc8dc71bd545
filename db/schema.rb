# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_10_13_085303) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "bulk_jobs", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "tenant_id", null: false
    t.string "entity", null: false
    t.string "operation", null: false
    t.jsonb "filters", null: false
    t.jsonb "payload"
    t.integer "number_of_records"
    t.datetime "started_at"
    t.datetime "completed_at"
    t.boolean "execute_workflow", default: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "sidekiq_job_id"
    t.integer "successful"
    t.integer "failure"
    t.boolean "execute_score_rule", default: false
    t.boolean "is_paused_email_sent", default: false
    t.string "category", default: "bulk_action"
  end

  create_table "phone_number_wise_record_details", force: :cascade do |t|
    t.string "phone_number", null: false
    t.jsonb "payload", default: {}
    t.string "status", null: false
    t.text "error_message"
    t.bigint "record_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "phone_number_for_log"
    t.jsonb "phone_number_details", default: {}
    t.bigint "message_id"
    t.string "message_status"
    t.index ["message_id"], name: "index_phone_number_wise_record_details_on_message_id"
    t.index ["record_id"], name: "index_phone_number_wise_record_details_on_record_id"
  end

  create_table "records", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "bulk_job_id", null: false
    t.bigint "entity_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.text "error_message"
    t.bigint "parent_entity_id"
    t.string "parent_entity_service"
    t.jsonb "relations", default: {}
  end

  create_table "summary_files", force: :cascade do |t|
    t.bigint "bulk_job_id", null: false
    t.string "file_type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["bulk_job_id"], name: "index_summary_files_on_bulk_job_id"
  end

  create_table "tenants", force: :cascade do |t|
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "users", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "tenant_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.text "token"
  end

  add_foreign_key "phone_number_wise_record_details", "records"
  add_foreign_key "summary_files", "bulk_jobs"
end
