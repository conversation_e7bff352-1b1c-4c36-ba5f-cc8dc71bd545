source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.0.3'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails', branch: 'main'
gem 'rails', '6.1.7.10'
# Use postgresql as the database for Active Record
gem 'pg', '~> 1.1'
# Use Puma as the app server
gem 'puma', '~> 5.6.9'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
# gem 'jbuilder', '~> 2.7'
# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 4.0'
# Use Active Model has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# Use Active Storage variant
# gem 'image_processing', '~> 1.2'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.4', require: false
gem 'rswag-api'
gem 'rswag-ui'
# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
# gem 'rack-cors'

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: [:mri, :mingw, :x64_mingw]
  gem 'rspec-rails', '~> 4.0.0'
  gem 'rswag-specs'
  gem 'webmock'
  gem 'factory_bot_rails'
  gem 'bunny-mock', require: false
end

  gem 'dotenv-rails'
group :test do
  gem 'rspec-sidekiq'
end

group :development do
  gem 'listen', '~> 3.3'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
gem 'sd_auth', git: 'https://github.com/amuratech/sd-gems.git', tag: 'sd_auth-v1.0.4'
gem 'will_paginate'
gem 'jbuilder'
gem 'prometheus-client'
gem 'silencer'
gem 'sidekiq-limit_fetch'
gem 'sidekiq'
gem 'redis-namespace'
gem 'aws-sdk-s3', '~> 1'
gem 'bunny'
gem "rexml", ">= 3.3.9"
gem 'nokogiri', "~> 1.16.5"
gem "rack", "~> 2.2.8.1"

