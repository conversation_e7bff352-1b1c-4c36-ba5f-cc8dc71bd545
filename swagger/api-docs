{"openapi": "3.0.1", "info": {"title": "API V1", "version": "v1"}, "paths": {"/v0e8136370948463e/bulk-action-jobs/health": {"get": {"summary": "Bulk job from database", "tags": ["Bulk job"], "responses": {"200": {"description": "Database is up"}, "404": {"description": "Entity not present"}, "503": {"description": "Database is down"}}}}, "/v1/bulk-action-jobs": {"post": {"summary": "Creates Bulk Job", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "body"}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"201": {"description": "Bulk Job created"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["entity", "operation", "filters"], "properties": {"entity": {"type": "string"}, "operation": {"type": "string"}, "filters": {"type": "object"}, "payload": {"type": "object"}}}}}}}, "get": {"summary": "List Bulk Jobs", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Bulk Jobs List"}, "401": {"description": "Authentication failed"}}}}, "/v1/bulk-action-jobs/search": {"post": {"summary": "List Bulk Jobs", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Bulk Jobs List"}, "401": {"description": "Authentication failed"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"jsonRule": {"condition": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/v1/bulk-action-jobs/{id}/summary-file": {"get": {"summary": "Returns url of summary file", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Summary file"}, "401": {"description": "Authentication failed"}}}}, "/v1/bulk-action-jobs/{id}/error-file": {"get": {"summary": "Returns url of error file", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Summary file"}, "401": {"description": "Authentication failed"}}}}, "/v1/bulk-action-jobs/{id}/attachment": {"get": {"summary": "Returns url of attachment file", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Attachment file"}, "401": {"description": "Authentication failed"}, "404": {"description": "Attachment not found"}}}}, "/v1/bulk-action-jobs/{id}/abort": {"post": {"summary": "Aborts the bulk job", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Bulk Job aborted"}, "401": {"description": "Authentication failed"}}}}, "/v1/bulk-action-jobs/{id}/pause": {"post": {"summary": "Pauses the bulk job", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Bulk Job Paused"}, "401": {"description": "Authentication failed"}}}}, "/v1/bulk-action-jobs/{id}/resume": {"post": {"summary": "Resumes the bulk job", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Bulk Job Resumed"}, "401": {"description": "Authentication failed"}}}}, "/v1/bulk-action-jobs/{id}": {"delete": {"summary": "Deletes the bulk job", "tags": ["Bulk Job"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"in": "header", "name": "Authorization", "required": true, "description": "Client token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Bulk Job Deleted"}, "401": {"description": "Authentication failed"}}}}}, "servers": [{"url": "http://{defaultHost}", "variables": {"defaultHost": {"default": "localhost:3000"}}}, {"url": "https://{defaultHost}", "variables": {"defaultHost": {"default": "127.0.0.1:3000"}}}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}