---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/v1/bulk-action-jobs":
    post:
      summary: Creates Bulk Job
      tags:
      - Bulk Job
      security:
      - bearerAuth: []
      parameters:
      - name: body
      - in: header
        name: Authorization
        required: true
        description: Client token
        schema:
          type: string
      responses:
        '201':
          description: Bulk Job created
        '401':
          description: Authentication failed
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - entity
              - operation
              - filters
              properties:
                entity:
                  type: string
                operation:
                  type: string
                filters:
                  type: object
                payload:
                  type: object
    get:
      summary: List Bulk Jobs
      tags:
      - Bulk Job
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        schema:
          type: string
      - name: size
        in: query
        schema:
          type: string
      - name: sort
        in: query
        schema:
          type: string
      - in: header
        name: Authorization
        required: true
        description: Client token
        schema:
          type: string
      responses:
        '200':
          description: Bulk Jobs List
        '401':
          description: Authentication failed
  "/v1/bulk-action-jobs/search":
    post:
      summary: List Bulk Jobs
      tags:
      - Bulk Job
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        schema:
          type: string
      - name: size
        in: query
        schema:
          type: string
      - name: sort
        in: query
        schema:
          type: string
      - name: jsonRule
        in: query
        schema:
          type: object
          properties:
            condition:
              type: string
            rules:
              type: array
              items:
                type: string
      - in: header
        name: Authorization
        required: true
        description: Client token
        schema:
          type: string
      responses:
        '200':
          description: Bulk Jobs List
        '401':
          description: Authentication failed
servers:
- url: https://{defaultHost}
  variables:
    defaultHost:
      default: www.example.com
