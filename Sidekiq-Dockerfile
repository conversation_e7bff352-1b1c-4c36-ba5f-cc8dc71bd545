FROM ruby:3.0.3

RUN apt-get update \
&& apt-get install -y postgresql-client nodejs npm sudo xvfb \
&& apt-get clean \
&& rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

WORKDIR /app
COPY Gemfile Gemfile.lock ./
RUN bundle config build.nokogiri --use-system-libraries
RUN bundle check || bundle install
COPY . ./
RUN ["chmod", "+x", "./entrypoints/sidekiq-entrypoint.sh"]
ENTRYPOINT ["./entrypoints/sidekiq-entrypoint.sh"]
